import React from 'react';
import { fredoka } from '@/lib/fonts';
import { marqueeItems } from '@/lib/constants';

const MarqueeSection = () => {
  return (
    <div className="border-t border-gray-100 py-6 overflow-hidden bg-littlespark-yellow/10">
      <div className="flex animate-marquee whitespace-nowrap">
        <div className="flex space-x-12 text-lg font-semibold">
          {marqueeItems.map((item, index) => (
            <React.Fragment key={index}>
              <span className={`${item.color} whitespace-nowrap ${fredoka.className}`}>{item.text}</span>
              {index < marqueeItems.length - 1 && (
                <span className="text-orange-500 whitespace-nowrap">•</span>
              )}
            </React.Fragment>
          ))}
        </div>
        {/* Duplicate for seamless loop */}
        <div className="flex space-x-12 text-lg font-semibold ml-12">
          {marqueeItems.map((item, index) => (
            <React.Fragment key={`duplicate-${index}`}>
              <span className={`${item.color} whitespace-nowrap ${fredoka.className}`}>{item.text}</span>
              {index < marqueeItems.length - 1 && (
                <span className="text-orange-500 whitespace-nowrap">•</span>
              )}
            </React.Fragment>
          ))}
        </div>
      </div>
    </div>
  );
};

export default MarqueeSection; 