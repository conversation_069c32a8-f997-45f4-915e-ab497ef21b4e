
import React from 'react';
import LearnTip from '@/components/shared/LearnTip';
import MusicHeader from './MusicHeader';
import MusicHelpDialog from './MusicHelpDialog';

interface MusicLayoutProps {
  children: React.ReactNode;
  learningMode: boolean;
  toggleLearningMode: () => void;
}

const MusicLayout: React.FC<MusicLayoutProps> = ({ 
  children, 
  learningMode, 
  toggleLearningMode 
}) => {
  const [isHelpOpen, setIsHelpOpen] = React.useState(false);

  return (
    <div className="container mx-auto px-4 py-8">
      <MusicHeader 
        learningMode={learningMode} 
        toggleLearningMode={toggleLearningMode}
        onHelpClick={() => setIsHelpOpen(true)}
      />
      
      <MusicHelpDialog 
        isOpen={isHelpOpen}
        onOpenChange={setIsHelpOpen}
      />
      
      {learningMode && (
        <LearnTip 
          tip="Music is made up of melody (the tune), rhythm (the beat), and harmony (how notes sound together). What kind of feeling do you want your music to have?"
          subject="Music Composition 101"
          type="learning"
        />
      )}
      
      {children}
    </div>
  );
};

export default MusicLayout;
