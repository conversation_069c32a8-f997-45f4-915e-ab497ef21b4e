import { NextRequest, NextResponse } from 'next/server';
import Replicate from 'replicate';

export async function POST(req: NextRequest) {
  const { imageData, prompt } = await req.json();
  console.log('🔥 CONTROLNET ENHANCE API: Received prompt=', prompt);
  if (!imageData) {
    return NextResponse.json({ error: 'Image data is required' }, { status: 400 });
  }

  const replicate = new Replicate({ auth: process.env.REPLICATE_API_TOKEN });
  const subject = (prompt && prompt.trim()) ? prompt.trim() : 'brightly colored image';

  const input = {
    eta: 0,
    seed: 20,
    image: imageData,
    scale: 9,
    steps: 20,
    prompt: `a photo of a brightly colored ${subject}`,
    scheduler: 'DDIM',
    structure: 'scribble',
    num_outputs: 1,
    low_threshold: 100,
    high_threshold: 200,
    negative_prompt: 'Longbody, lowres, bad anatomy, bad hands, missing fingers, extra digit, fewer digits, cropped, worst quality, low quality',
    image_resolution: 512,
    return_reference_image: false
  };
  console.log('🔥 CONTROLNET ENHANCE API: Input to Replicate:', input);

  try {
    const output = await replicate.run(
      'r<PERSON><PERSON><PERSON>an/controlnet:795433b19458d0f4fa172a7ccf93178d2adb1cb8ab2ad6c8fdc33fdbcd49f477',
      { input }
    ) as Array<{ url: () => string }>;

    console.log('🔥 CONTROLNET ENHANCE API: Replicate output:', output);
    if (output && output[0] && typeof output[0].url === 'function') {
      const imageUrl = output[0].url();
      console.log('🔥 CONTROLNET ENHANCE API: Returning URL:', imageUrl);
      return NextResponse.json({ image: imageUrl });
    }
    console.log('🔥 CONTROLNET ENHANCE API: No valid output');
    return NextResponse.json({ error: 'No enhanced image generated' }, { status: 500 });
  } catch (err) {
    console.error('🔥 CONTROLNET ENHANCE API: Error:', err);
    return NextResponse.json({ error: 'Failed to enhance image', details: (err as Error).message }, { status: 500 });
  }
} 