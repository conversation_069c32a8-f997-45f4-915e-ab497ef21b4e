
import React from 'react';
import ArtPreview from './display/ArtPreview';
import ArtActionButtons from './display/ArtActionButtons';

interface ArtDisplayProps {
  generatedImage: string | null;
  isGenerating: boolean;
  title: string;
  handleGenerateArt: () => void;
  aspectRatio?: string;
  handleSaveToPortfolio?: () => void;
  isSaving?: boolean;
  isSaved?: boolean;
}

const ArtDisplay = ({
  generatedImage,
  isGenerating,
  title,
  handleGenerateArt,
  aspectRatio = "1:1",
  handleSaveToPortfolio,
  isSaving = false,
  isSaved = false
}: ArtDisplayProps) => {
  // Use state to track whether action buttons should be displayed
  const [showButtons, setShowButtons] = React.useState(false);
  
  // Update button visibility when image is loaded or generation state changes
  React.useEffect(() => {
    if (generatedImage && !isGenerating) {
      setShowButtons(true);
    } else {
      setShowButtons(false);
    }
  }, [generatedImage, isGenerating]);
  
  return (
    <div>
      <div className="mb-4">
        <div className="flex justify-between items-center mb-2">
          <h2 className="text-base sm:text-lg font-medium">Your Artwork</h2>
        </div>
        
        <ArtPreview 
          generatedImage={generatedImage} 
          isGenerating={isGenerating} 
          aspectRatio={aspectRatio}
        />
      </div>
      
      {showButtons && (
        <ArtActionButtons
          generatedImage={generatedImage!}
          title={title}
          aspectRatio={aspectRatio}
          handleGenerateArt={handleGenerateArt}
          isGenerating={isGenerating}
          handleSaveToPortfolio={handleSaveToPortfolio}
          isSaving={isSaving}
          isSaved={isSaved}
        />
      )}
    </div>
  );
};

export default ArtDisplay;
