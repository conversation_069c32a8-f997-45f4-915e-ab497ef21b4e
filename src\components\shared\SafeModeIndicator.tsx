
import React from 'react';
import { Shield } from 'lucide-react';

interface SafeModeIndicatorProps {
  type: 'video' | 'story' | 'art' | 'game' | 'music' | 'coloring' | 'creature';
}

const SafeModeIndicator = ({ type = 'creature' as const }: SafeModeIndicatorProps) => {
  const safetyMessage = {
    'game': 'Safe Mode is active - all games will be kid-friendly',
    'art': 'Safe Mode is active - all artwork will be kid-friendly',
    'story': 'Safe Mode is active - all stories will be kid-friendly',
    'music': 'Safe Mode is active - all music will be kid-friendly',
    'video': 'Safe Mode is active - all videos will be kid-friendly',
    'coloring': 'Safe Mode is active - all coloring pages will be kid-friendly',
    'creature': 'Safe Mode is active - all creatures will be kid-friendly'
  };

  return (
    <div className="flex items-center gap-2 bg-spark-green/10 rounded-lg px-3 py-2">
      <Shield className="h-4 w-4 text-spark-green" />
      <span className="text-sm text-spark-green font-medium">
        {safetyMessage[type]}
      </span>
    </div>
  );
};

export default SafeModeIndicator;
