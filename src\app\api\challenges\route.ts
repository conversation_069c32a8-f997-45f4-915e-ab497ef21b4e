import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// GET /api/challenges - Fetch all active challenges
export async function GET() {
  try {
    const challenges = await prisma.challenge.findMany({
      where: {
        is_active: true
      },
      orderBy: [
        { difficulty: 'asc' },
        { created_at: 'desc' }
      ]
    });

    return NextResponse.json({
      success: true,
      challenges
    });
  } catch (error) {
    console.error('Error fetching challenges:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch challenges' 
      },
      { status: 500 }
    );
  }
}

// POST /api/challenges - Create new challenge (for admin use)
export async function POST(request: NextRequest) {
  try {
    const {
      id,
      title,
      description,
      difficulty,
      type,
      prompt,
      created_by = 'admin',
      valid_until
    } = await request.json();

    // Validate required fields
    if (!id || !title || !description || !difficulty || !type || !prompt) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Missing required fields' 
        },
        { status: 400 }
      );
    }

    // Validate difficulty
    if (!['easy', 'medium', 'hard'].includes(difficulty)) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid difficulty level' 
        },
        { status: 400 }
      );
    }

    // Validate type
    if (!['story', 'art', 'music', 'game'].includes(type)) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid challenge type' 
        },
        { status: 400 }
      );
    }

    const challenge = await prisma.challenge.create({
      data: {
        id,
        title,
        description,
        difficulty,
        type,
        prompt,
        created_by,
        valid_until: valid_until ? new Date(valid_until) : null
      }
    });

    return NextResponse.json({
      success: true,
      challenge
    });
  } catch (error) {
    console.error('Error creating challenge:', error);
    
    // Handle unique constraint violation
    if (error instanceof Error && error.message.includes('Unique constraint')) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Challenge with this ID already exists' 
        },
        { status: 409 }
      );
    }

    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to create challenge' 
      },
      { status: 500 }
    );
  }
}
