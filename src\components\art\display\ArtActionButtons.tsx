
import React from 'react';
import { Button } from '@/components/ui/button';
import { Download, RefreshCw, Save, Check, Loader } from 'lucide-react';
import { downloadImage } from '@/utils/images';
import { toast } from 'sonner';

interface ArtActionButtonsProps {
  generatedImage: string;
  title: string;
  aspectRatio: string;
  handleGenerateArt: () => void;
  isGenerating: boolean;
  handleSaveToPortfolio?: () => void;
  isSaving?: boolean;
  isSaved?: boolean;
}

const ArtActionButtons = ({
  generatedImage,
  title,
  handleGenerateArt,
  isGenerating,
  handleSaveToPortfolio,
  isSaving = false,
  isSaved = false
}: ArtActionButtonsProps) => {
  const handleDownload = () => {
    if (generatedImage) {
      downloadImage(generatedImage, title || 'my-artwork');
      toast.success('Artwork downloaded successfully!');
    }
  };

  return (
    <div className="flex flex-col sm:flex-row flex-wrap gap-2 sm:gap-3">
      <Button 
        variant="outline" 
        className="flex-1 min-h-[44px] text-sm sm:text-base"
        onClick={handleDownload}
        disabled={isGenerating}
      >
        <Download className="mr-2 h-4 w-4" /> Download
      </Button>
      
      {handleSaveToPortfolio && (
        <Button
          variant={isSaved ? "default" : "outline"}
          className={`flex-1 min-h-[44px] text-sm sm:text-base ${
            isSaved ? 'bg-green-600 hover:bg-green-700 text-white' : ''
          }`}
          onClick={handleSaveToPortfolio}
          disabled={isGenerating || isSaving || isSaved}
        >
          {isSaving ? (
            <>
              <Loader className="mr-2 h-4 w-4 animate-spin" />
              <span className="sm:hidden">Saving...</span>
              <span className="hidden sm:inline">Saving...</span>
            </>
          ) : isSaved ? (
            <>
              <Check className="mr-2 h-4 w-4" />
              <span className="sm:hidden">Saved ✓</span>
              <span className="hidden sm:inline">Saved to Portfolio ✓</span>
            </>
          ) : (
            <>
              <Save className="mr-2 h-4 w-4" />
              <span className="sm:hidden">Save</span>
              <span className="hidden sm:inline">Save to Portfolio</span>
            </>
          )}
        </Button>
      )}
      
      <Button 
        variant="outline" 
        className="flex-1 min-h-[44px] text-sm sm:text-base"
        onClick={handleGenerateArt}
        disabled={isGenerating}
      >
        <RefreshCw className="mr-2 h-4 w-4" />
        <span className="sm:hidden">New</span>
        <span className="hidden sm:inline">Create New</span>
      </Button>
    </div>
  );
};

export default ArtActionButtons;
