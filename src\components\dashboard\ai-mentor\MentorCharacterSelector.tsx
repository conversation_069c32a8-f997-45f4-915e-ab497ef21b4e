
import React from 'react';
import { Button } from '@/components/ui/button';
import { CharacterSelectorProps } from './types';

export const MentorCharacterSelector: React.FC<CharacterSelectorProps> = ({ 
  mentorCharacter, 
  onChange 
}) => {
  return (
    <div className="flex justify-center gap-2 mb-6">
      <Button 
        size="sm" 
        variant={mentorCharacter === 'robot' ? 'default' : 'outline'}
        className={mentorCharacter === 'robot' ? 'bg-spark-blue' : ''}
        onClick={() => onChange('robot')}
      >
        Robot
      </Button>
      <Button 
        size="sm" 
        variant={mentorCharacter === 'owl' ? 'default' : 'outline'}
        className={mentorCharacter === 'owl' ? 'bg-spark-lavender' : ''}
        onClick={() => onChange('owl')}
      >
        Owl
      </Button>
      <Button 
        size="sm" 
        variant={mentorCharacter === 'explorer' ? 'default' : 'outline'}
        className={mentorCharacter === 'explorer' ? 'bg-spark-orange' : ''}
        onClick={() => onChange('explorer')}
      >
        Explorer
      </Button>
    </div>
  );
};
