
import React from 'react';

interface TestimonialEmptyStateProps {
  isAdmin: boolean;
}

const TestimonialEmptyState = ({ isAdmin }: TestimonialEmptyStateProps) => {
  return (
    <div className="text-center p-8 bg-gray-50 rounded-lg">
      <p className="text-gray-500">
        {isAdmin 
          ? 'No testimonials found. When users submit testimonials, they will appear here for approval.'
          : 'No testimonials yet. Be the first to share your experience!'}
      </p>
    </div>
  );
};

export default TestimonialEmptyState;
