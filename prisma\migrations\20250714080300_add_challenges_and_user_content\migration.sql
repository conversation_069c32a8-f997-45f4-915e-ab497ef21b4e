-- CreateTable
CREATE TABLE "challenges" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "difficulty" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "prompt" TEXT NOT NULL,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_by" TEXT NOT NULL DEFAULT 'system',
    "valid_until" TIMESTAMP(3),
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "challenges_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_content" (
    "id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "type" TEXT NOT NULL,
    "title" TEXT,
    "content_metadata" JSONB,
    "preview_url" TEXT,
    "challenge_id" TEXT,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "user_content_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "challenge_completions" (
    "id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "challenge_id" TEXT NOT NULL,
    "content_id" UUID NOT NULL,
    "completed_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "challenge_completions_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "user_content_user_id_type_idx" ON "user_content"("user_id", "type");

-- CreateIndex
CREATE INDEX "user_content_challenge_id_idx" ON "user_content"("challenge_id");

-- CreateIndex
CREATE UNIQUE INDEX "challenge_completions_content_id_key" ON "challenge_completions"("content_id");

-- CreateIndex
CREATE INDEX "challenge_completions_user_id_idx" ON "challenge_completions"("user_id");

-- CreateIndex
CREATE INDEX "challenge_completions_challenge_id_idx" ON "challenge_completions"("challenge_id");

-- CreateIndex
CREATE UNIQUE INDEX "challenge_completions_user_id_challenge_id_key" ON "challenge_completions"("user_id", "challenge_id");

-- AddForeignKey
ALTER TABLE "user_content" ADD CONSTRAINT "user_content_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_content" ADD CONSTRAINT "user_content_challenge_id_fkey" FOREIGN KEY ("challenge_id") REFERENCES "challenges"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "challenge_completions" ADD CONSTRAINT "challenge_completions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "challenge_completions" ADD CONSTRAINT "challenge_completions_challenge_id_fkey" FOREIGN KEY ("challenge_id") REFERENCES "challenges"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "challenge_completions" ADD CONSTRAINT "challenge_completions_content_id_fkey" FOREIGN KEY ("content_id") REFERENCES "user_content"("id") ON DELETE CASCADE ON UPDATE CASCADE;
