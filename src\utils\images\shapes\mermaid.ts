
/**
 * Draws a mermaid shape on the canvas
 */
export const drawMermaidShape = (ctx: CanvasRenderingContext2D, x: number, y: number, size: number) => {
  // Head
  ctx.arc(x, y - size*0.5, size*0.15, 0, 2 * Math.PI);
  
  // Hair
  for (let i = 0; i < 5; i++) {
    ctx.moveTo(x, y - size*0.5);
    ctx.quadraticCurveTo(
      x - size*0.3 + i*size*0.15, y - size*0.4,
      x - size*0.2 + i*size*0.1, y - size*0.2
    );
  }
  
  // Upper body
  ctx.moveTo(x, y - size*0.35);
  ctx.quadraticCurveTo(
    x - size*0.1, y - size*0.1,
    x, y + size*0.1
  );
  
  // Tail
  ctx.moveTo(x, y + size*0.1);
  ctx.quadraticCurveTo(
    x + size*0.3, y + size*0.3,
    x + size*0.1, y + size*0.5
  );
  ctx.quadraticCurveTo(
    x, y + size*0.4,
    x - size*0.1, y + size*0.5
  );
  ctx.quadraticCurveTo(
    x - size*0.3, y + size*0.3,
    x, y + size*0.1
  );
  
  // Tail fin
  ctx.moveTo(x, y + size*0.5);
  ctx.quadraticCurveTo(
    x + size*0.2, y + size*0.7,
    x + size*0.4, y + size*0.6
  );
  ctx.moveTo(x, y + size*0.5);
  ctx.quadraticCurveTo(
    x - size*0.2, y + size*0.7,
    x - size*0.4, y + size*0.6
  );
  
  // Arms
  ctx.moveTo(x, y - size*0.2);
  ctx.quadraticCurveTo(
    x - size*0.2, y,
    x - size*0.4, y - size*0.1
  );
  
  ctx.moveTo(x, y - size*0.2);
  ctx.quadraticCurveTo(
    x + size*0.2, y,
    x + size*0.4, y - size*0.1
  );
};
