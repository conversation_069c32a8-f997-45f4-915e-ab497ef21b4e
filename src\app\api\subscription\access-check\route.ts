import { NextRequest, NextResponse } from 'next/server';
import { checkUserAccess } from '@/lib/subscription-utils';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    const accessResult = await checkUserAccess(userId);
    return NextResponse.json(accessResult);

  } catch (error) {
    console.error('Error checking user access:', error);
    return NextResponse.json(
      { 
        hasAccess: false,
        reason: 'Failed to check access',
        error: 'Internal server error'
      },
      { status: 500 }
    );
  }
}
