
import React from 'react';
import { Sparkles } from 'lucide-react';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';

interface CreativeToolsHeaderProps {
  showChallenges: boolean;
  setShowChallenges: React.Dispatch<React.SetStateAction<boolean>>;
}

const CreativeToolsHeader = ({ showChallenges, setShowChallenges }: CreativeToolsHeaderProps) => {
  const toggleChallenges = () => {
    const newValue = !showChallenges;
    setShowChallenges(newValue);
    
    if (newValue) {
      toast.success("Challenges now visible!", {
        icon: <Sparkles className="h-5 w-5 text-spark-yellow" />,
      });
    } else {
      toast.info("Challenges hidden", {
        icon: <Sparkles className="h-5 w-5 text-spark-gray" />,
      });
    }
  };

  return (
    <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-4">
      <h2 className="text-xl sm:text-2xl font-bold font-quicksand">AI-Powered Creativity Hub</h2>
      
      <div className="flex items-center gap-6">
        <div className="flex items-center space-x-2">
          <div className="flex items-center space-x-2">
            <Switch 
              checked={showChallenges} 
              onCheckedChange={toggleChallenges} 
              id="challenges-mode"
            />
            <Label htmlFor="challenges-mode" className="cursor-pointer">
              <span className="flex items-center gap-1">
                <Sparkles className="h-4 w-4 text-spark-yellow" />
                <span className="text-sm sm:text-base">Show Challenges</span>
              </span>
            </Label>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreativeToolsHeader;
