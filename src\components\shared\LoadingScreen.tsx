
import React from 'react';
import { Loader2 } from 'lucide-react';

interface LoadingScreenProps {
  message?: string;
  fullScreen?: boolean;
  size?: 'sm' | 'md' | 'lg';
  color?: string;
}

export const LoadingScreen: React.FC<LoadingScreenProps> = ({
  message = "Loading...",
  fullScreen = true,
  size = 'lg',
  color = '#FF6B35'
}) => {
  const sizeClasses = {
    sm: 'h-8 w-8',
    md: 'h-12 w-12',
    lg: 'h-16 w-16'
  };

  const containerClasses = fullScreen 
    ? "min-h-screen bg-gradient-to-b from-white to-gray-50 flex items-center justify-center"
    : "flex items-center justify-center p-8";

  return (
    <div className={containerClasses}>
      <div className="text-center">
        <Loader2 
          className={`${sizeClasses[size]} animate-spin mx-auto mb-4`}
          style={{ color }}
        />
        <p className="text-gray-600 text-lg font-nunito">
          {message}
        </p>
      </div>
    </div>
  );
};

export default LoadingScreen;
