// Test script to verify challenges are loaded and API is working
const { PrismaClient } = require('../src/generated/prisma');

async function testChallenges() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔍 Testing Challenge System...\n');
    
    // 1. Check if challenges exist
    const challenges = await prisma.challenge.findMany({
      orderBy: { created_at: 'asc' }
    });
    
    console.log(`✅ Found ${challenges.length} challenges in database:`);
    challenges.forEach((challenge, index) => {
      console.log(`   ${index + 1}. ${challenge.title} (${challenge.type}, ${challenge.difficulty})`);
    });
    
    // 2. Check if tables exist and are accessible
    const profileCount = await prisma.profile.count();
    const userContentCount = await prisma.userContent.count();
    const completionCount = await prisma.challengeCompletion.count();
    
    console.log('\n📊 Database Status:');
    console.log(`   - Profiles: ${profileCount}`);
    console.log(`   - User Content: ${userContentCount}`);
    console.log(`   - Challenge Completions: ${completionCount}`);
    
    // 3. Test challenge types
    const challengeTypes = [...new Set(challenges.map(c => c.type))];
    console.log(`\n🎯 Available Challenge Types: ${challengeTypes.join(', ')}`);
    
    // 4. Test challenge difficulties
    const difficulties = [...new Set(challenges.map(c => c.difficulty))];
    console.log(`📈 Available Difficulties: ${difficulties.join(', ')}`);
    
    console.log('\n✅ Challenge system is ready for testing!');
    console.log('\n🚀 Next Steps:');
    console.log('   1. Sign up/login to create a user profile');
    console.log('   2. Go to dashboard to see challenges');
    console.log('   3. Start a challenge and create content');
    console.log('   4. Mark challenge as complete');
    
  } catch (error) {
    console.error('❌ Error testing challenges:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testChallenges();
