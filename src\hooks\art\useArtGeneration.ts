"use client";
import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { generateArtwork } from '@/utils/ai/artService';
import { useArtStyle } from './useArtStyle';

// Sample images shown during development or when errors occur
const fallbackImages = [
  "https://images.unsplash.com/photo-1579546929518-9e396f3cc809?q=80&w=1470&auto=format&fit=crop",
  "https://images.unsplash.com/photo-1557672172-298e090bd0f1?q=80&w=1587&auto=format&fit=crop",
  "https://images.unsplash.com/photo-1550684376-efcbd6e3f031?q=80&w=1740&auto=format&fit=crop",
  "https://images.unsplash.com/photo-1604871000636-074fa5117945?q=80&w=1587&auto=format&fit=crop"
];

export const useArtGeneration = (prompt: string, title: string, setTitle: (title: string) => void) => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedImage, setGeneratedImage] = useState<string | null>(null);
  const { artStyle, setArtStyle } = useArtStyle();
  const [aspectRatio, setAspectRatio] = useState('1:1');
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  
  // Pre-load fallback images to ensure they're available when needed
  useEffect(() => {
    fallbackImages.forEach(imgSrc => {
      const img = new Image();
      img.src = imgSrc;
    });
  }, []);
  
  const handleGenerateArt = async () => {
    if (prompt.length < 5) {
      toast.error("Please write a longer description for better results");
      return;
    }
    
    setIsGenerating(true);
    setErrorMessage(null);
    
    try {
      // Generate artwork using the new API
      const result = await generateArtwork(prompt, artStyle, aspectRatio);
      
      setGeneratedImage(result.imageUrl);
      
      // Set title if not already set
      if (!title) {
        const simplifiedTitle = prompt.split(' ').slice(0, 4).join(' ');
        setTitle(`${simplifiedTitle}...`);
      }
      
      toast.success("Artwork generated successfully!");
      
    } catch (error: unknown) {
      console.error("Art Generation Error:", error);
      handleFallbackImage();
      
      // Set an error message
      const errorMsg = error instanceof Error
        ? error.message
        : "Failed to generate art. Using a placeholder image instead.";
      setErrorMessage(errorMsg);
      
      const isDevOrPreview = window.location.hostname.includes('localhost') || 
                            window.location.hostname.includes('lovableproject.com');
                            
      if (isDevOrPreview) {
        toast.info("Using a placeholder image in development", {
          description: "Art generation services require API keys to be configured"
        });
      } else {
        toast.error("Using a placeholder image while our service is unavailable");
      }
    } finally {
      setIsGenerating(false);
    }
  };
  
  const handleFallbackImage = () => {
    // Use a random fallback image
    const randomImage = fallbackImages[Math.floor(Math.random() * fallbackImages.length)];
    setGeneratedImage(randomImage);
    
    if (!title) {
      const simplifiedTitle = prompt.split(' ').slice(0, 4).join(' ');
      setTitle(`${simplifiedTitle}...`);
    }
  };

  return {
    isGenerating,
    generatedImage,
    artStyle,
    setArtStyle,
    aspectRatio,
    setAspectRatio,
    errorMessage,
    handleGenerateArt
  };
};
