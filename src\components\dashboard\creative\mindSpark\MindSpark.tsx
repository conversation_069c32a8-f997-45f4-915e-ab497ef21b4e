import React, { useState, useEffect, useCallback } from "react";
import { useAuth } from "@/hooks/useAuth";
import { toast } from "sonner";
import { AgeGroup, MindSparkProps, Question } from "./types";
import {
    determineAgeGroup,
    saveQuestionHistory,
    fetchQuestionHistory,
} from "./questionUtils";
import QuestionHistory from "./QuestionHistory";
import ActiveQuestion from "./ActiveQuestion";
import MindSparkHeader from "./MindSparkHeader";
import MindSparkFooter from "./MindSparkFooter";
import { useQuestions } from "./hooks/useQuestions";

const MindSpark: React.FC<MindSparkProps> = ({ userAge = 10 }) => {
    const { user } = useAuth();
    const [currentQuestion, setCurrentQuestion] = useState<Question | null>(
        null
    );
    const [selectedAnswerIndex, setSelectedAnswerIndex] = useState<
        number | null
    >(null);
    const [showResults, setShowResults] = useState(false);
    const [showHistory, setShowHistory] = useState(false);
    const [questionHistory, setQuestionHistory] = useState<Question[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const [selectedAgeGroup, setSelectedAgeGroup] = useState<AgeGroup>(() => {
        return determineAgeGroup(userAge);
    });

    const { loadQuestions, generateNewQuestion } = useQuestions(
        selectedAgeGroup,
        setCurrentQuestion,
        setIsLoading
    );

    useEffect(() => {
        loadQuestions();
    }, [selectedAgeGroup, loadQuestions]);

    const loadQuestionHistory = useCallback(async () => {
        if (!user) return;

        try {
            setIsLoading(true);
            const history = await fetchQuestionHistory();
            setQuestionHistory(history);
        } catch (error) {
            console.error("Error loading question history:", error);
        } finally {
            setIsLoading(false);
        }
    }, [user]);

    useEffect(() => {
        if (user) {
            loadQuestionHistory();
        }
    }, [user, loadQuestionHistory]);

    const handleAnswerSelect = (index: number) => {
        setSelectedAnswerIndex(index);
    };

    const submitAnswer = () => {
        if (selectedAnswerIndex === null || !currentQuestion) return;

        const updatedQuestion = {
            ...currentQuestion,
            userAnswerIndex: selectedAnswerIndex,
            userAnswer: currentQuestion.answers[selectedAnswerIndex],
            isCorrect:
                selectedAnswerIndex === currentQuestion.correctAnswerIndex,
        };

        setCurrentQuestion(updatedQuestion);
        setShowResults(true);

        const updatedHistory = [updatedQuestion, ...questionHistory].slice(
            0,
            50
        );
        setQuestionHistory(updatedHistory);

        if (user) {
            saveQuestionHistory(user.id, updatedHistory);
        }

        if (updatedQuestion.isCorrect) {
            toast.success("Correct answer! Great job! 🎉");
        } else {
            toast.error("Not quite right. Try to understand why!");
        }
    };

    const handleAgeGroupChange = (value: string) => {
        setSelectedAgeGroup(value as AgeGroup);
    };

    const handleNextQuestion = () => {
        generateNewQuestion();
        setSelectedAnswerIndex(null);
        setShowResults(false);
    };

    return (
        <div className="bg-white rounded-xl shadow-sm p-6">
            <MindSparkHeader
                selectedAgeGroup={selectedAgeGroup}
                handleAgeGroupChange={handleAgeGroupChange}
                showHistory={showHistory}
                setShowHistory={setShowHistory}
            />

            {isLoading && !currentQuestion && (
                <div className="text-center py-8">
                    <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-spark-blue border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]"></div>
                    <p className="mt-2 text-gray-600">Loading questions...</p>
                </div>
            )}

            {showHistory ? (
                <QuestionHistory
                    questionHistory={questionHistory}
                    onBack={() => setShowHistory(false)}
                />
            ) : (
                <div>
                    {currentQuestion && (
                        <ActiveQuestion
                            currentQuestion={currentQuestion}
                            selectedAnswerIndex={selectedAnswerIndex}
                            showResults={showResults}
                            onAnswerSelect={handleAnswerSelect}
                            onSubmitAnswer={submitAnswer}
                            onNextQuestion={handleNextQuestion}
                        />
                    )}
                </div>
            )}

            <MindSparkFooter />
        </div>
    );
};

export default MindSpark;
