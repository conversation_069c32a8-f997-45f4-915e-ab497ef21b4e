"use client";
import React from "react";
import CreativeToolHelpDialog from "@/components/shared/CreativeToolHelpDialog";

interface StoryHelpDialogProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
}

const storyHelpSteps = [
    {
        title: "Create Your Story Title",
        content: "Give your story an exciting title that captures the reader's attention. A good title hints at the adventure, mystery, or fun that awaits inside your story."
    },
    {
        title: "Choose Reader Age",
        content: "Select the age range for your readers. This ensures your story uses appropriate vocabulary, themes, and complexity levels that match your audience perfectly."
    },
    {
        title: "Pick Your Story Genre",
        content: "Choose from adventure, mystery, comedy, science fiction, or standard stories. Each genre has its own magical style and storytelling approach."
    },
    {
        title: "Write Your Story",
        content: "Start typing your story in the main text area. Begin with an interesting opening, develop your characters, and create an exciting plot that keeps readers engaged."
    },
    {
        title: "Get AI Story Help",
        content: "Use 'Continue Story' to get AI-powered suggestions for what happens next. Explore character ideas and setting suggestions to make your story even more amazing."
    },
    {
        title: "Save Your Masterpiece",
        content: "Save your completed story and share it with family and friends. You can always return to edit, expand, or create sequels to your original story."
    }
];

const StoryHelpDialog = ({ open, onOpenChange }: StoryHelpDialogProps) => {
    return (
        <CreativeToolHelpDialog 
            open={open}
            onOpenChange={onOpenChange}
            title="Interactive Story Builder"
            description="Create amazing stories with AI-powered writing assistance"
            steps={storyHelpSteps}
            aiMentorMessage="Ready to create an amazing story? I'll guide you through bringing your imagination to life!"
        />
    );
};

export default StoryHelpDialog;
