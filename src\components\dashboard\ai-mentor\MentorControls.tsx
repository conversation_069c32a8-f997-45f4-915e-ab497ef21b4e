
import React from 'react';
import { Button } from '@/components/ui/button';
import { MessageCircle } from 'lucide-react';
import { MentorControlsProps } from './types';

export const MentorControls: React.FC<MentorControlsProps> = ({
  isChatMode,
  toggleChatMode,
  mentorCharacter
}) => {
  // Get button color based on character
  const getButtonColor = () => {
    switch (mentor<PERSON><PERSON><PERSON>) {
      case 'robot':
        return 'bg-spark-blue hover:bg-spark-blue/90';
      case 'owl':
        return 'bg-spark-lavender hover:bg-spark-lavender/90';
      case 'explorer':
      default:
        return 'bg-spark-orange hover:bg-spark-orange/90';
    }
  };
  
  return (
    <div className="flex justify-center gap-2 mt-4">
      <Button 
        size="sm" 
        className={`gap-1 ${isChatMode ? 'bg-gray-200 hover:bg-gray-300 text-gray-700' : getButtonColor()}`}
        onClick={toggleChatMode}
      >
        <MessageCircle className="h-4 w-4" />
        {isChatMode ? 'Exit Chat' : 'Chat'}
      </Button>
    </div>
  );
};
