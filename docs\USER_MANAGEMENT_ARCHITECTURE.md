# User Management & Content Creation Architecture

## Overview

Our application uses a **two-tier user management system** that separates content consumers from content creators, ensuring security and proper role-based access control.

## 1. User Types & Authentication Flows

### **Tier 1: Main Application Users (Students/Parents)**
- **Purpose**: Content consumers who take challenges and track progress
- **Authentication**: Magic link system (email-based, passwordless)
- **Database**: Main application database (`users` table)
- **Access**: Dashboard, challenges, progress tracking, subscription management
- **No Content Creation**: Cannot create or modify challenges

### **Tier 2: Content Creators (Educators/Admins)**
- **Purpose**: Create and manage educational content
- **Authentication**: CMS username/password system
- **Database**: CMS database (`users` table)
- **Access**: CMS admin panel for content creation and management
- **Roles Available**:
  - `admin`: Full system access, user management, all content operations
  - `content-creator`: Can create/edit own challenges, view published content
  - `educator`: Can create/edit own challenges, view published content
  - `reviewer`: Can view and review content (future feature)

## 2. Content Creation Workflow

### **For New Educators:**
```
1. <PERSON><PERSON> creates educator account in CMS:
   - Go to localhost:3001/admin
   - Navigate to Users → Create New
   - Set role to "educator" or "content-creator"
   - Provide username, password, name, and specialties

2. Educator receives login credentials

3. Educator logs into CMS admin panel:
   - Visit localhost:3001/admin
   - Use provided credentials

4. Educator creates challenges:
   - Navigate to Challenges → Create New
   - Fill in all required fields
   - Set status to "published" when ready
   - Content automatically appears in main app
```

### **For Admins:**
```
1. Full access to CMS admin panel
2. Can create and manage all users
3. Can create, edit, and delete any content
4. Can assign roles and permissions
5. Can manage system settings
```

## 3. Role-Based Permissions

### **CMS Permissions Matrix:**

| Action | Admin | Content Creator | Educator | Reviewer |
|--------|-------|----------------|----------|----------|
| Create Users | ✅ | ❌ | ❌ | ❌ |
| Create Challenges | ✅ | ✅ | ✅ | ❌ |
| Edit Own Challenges | ✅ | ✅ | ✅ | ❌ |
| Edit All Challenges | ✅ | ❌ | ❌ | ❌ |
| Delete Challenges | ✅ | ❌ | ❌ | ❌ |
| View All Content | ✅ | ✅ | ✅ | ✅ |
| Publish Content | ✅ | ✅ | ✅ | ❌ |

### **Main App Permissions:**
- All users can view published challenges
- No content creation permissions in main app
- Progress tracking and subscription management only

## 4. Data Flow & Synchronization

### **Content Publication Flow:**
```
CMS Database → API Endpoint → Main Application → User Dashboard
```

1. **Content Creation**: Educators create challenges in CMS
2. **API Integration**: Main app fetches content via `/my-route` endpoint
3. **Real-time Updates**: New content appears immediately on dashboard refresh
4. **Fallback System**: If CMS is unavailable, fallback challenges are shown

### **User Data Separation:**
- **CMS Users**: Content creators with authentication and role management
- **Main App Users**: Content consumers with subscription and progress tracking
- **No Cross-Pollination**: These are separate user bases with different purposes

## 5. Implementation Details

### **CMS Setup for New Educators:**

1. **Admin Creates Account:**
```javascript
// In CMS Admin Panel
{
  firstName: "Jane",
  lastName: "Smith", 
  email: "<EMAIL>",
  role: "educator",
  specialties: ["art", "story"],
  isActive: true
}
```

2. **Educator Access:**
- URL: `http://localhost:3001/admin`
- Credentials: Provided by admin
- Interface: Full CMS admin panel with role-based restrictions

### **Content Creation Process:**

1. **Challenge Creation:**
```javascript
// Educator fills out challenge form
{
  title: "Ocean Adventure Story",
  category: "story",
  difficulty: "easy",
  ageGroup: ["6-8"],
  description: "Write about underwater adventures...",
  instructions: "Step-by-step guide...",
  status: "published" // Makes it live immediately
}
```

2. **Automatic Publication:**
- Challenge appears in main app within seconds
- No manual approval process needed
- Content creator owns their content

## 6. Security Considerations

### **Access Control:**
- CMS users cannot access main application user data
- Main app users cannot access CMS
- Role-based permissions prevent unauthorized content modification
- Published content is public, drafts are private to creators

### **Authentication Separation:**
- Different authentication systems prevent cross-contamination
- Magic links for consumers (simple, secure)
- Username/password for creators (traditional, role-based)

## 7. Future Enhancements

### **Planned Features:**
1. **Content Review Workflow**: Reviewer role for quality control
2. **Bulk Content Import**: CSV/Excel import for educators
3. **Content Analytics**: Track challenge performance and engagement
4. **Collaborative Editing**: Multiple educators working on same content
5. **Version Control**: Track changes and revisions to challenges

### **Integration Possibilities:**
1. **Single Sign-On (SSO)**: For educational institutions
2. **LMS Integration**: Connect with existing learning management systems
3. **API Keys**: Allow external systems to create content programmatically

## 8. Getting Started

### **For Admins:**
1. Access CMS at `http://localhost:3001/admin`
2. Create educator accounts as needed
3. Monitor content quality and user activity
4. Manage system settings and permissions

### **For Educators:**
1. Receive credentials from admin
2. Log into CMS admin panel
3. Create engaging challenges for students
4. Monitor how content performs in main application

### **For Developers:**
1. CMS handles all content management
2. Main app focuses on user experience
3. API integration keeps systems synchronized
4. Role-based access ensures security

This architecture provides a clean separation of concerns while maintaining flexibility for future growth and feature additions.
