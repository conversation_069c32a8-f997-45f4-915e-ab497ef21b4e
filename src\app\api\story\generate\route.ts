import { NextRequest, NextResponse } from 'next/server';
import { getFormatGuideline } from '@/components/story/helpers/StoryFormatUtils';
import { moderateContent } from '@/utils/ai/contentModeration';
import { ChatGoogleGenerativeAI } from "@langchain/google-genai";
import { requireSubscription } from '@/lib/middleware/subscription-guard';
import { createServerSupabaseClient } from '@/lib/supabase/server';

import dotenv from 'dotenv';

dotenv.config();

const llm = new ChatGoogleGenerativeAI({
  model: "gemini-2.0-flash-lite",
  temperature: 0.7,
  apiKey: process.env.GOOGLE_API_KEY || "",
});

console.log('GOOGLE_API_KEY available:', !!process.env.GOOGLE_API_KEY);

async function storyGenerateHandler(request: NextRequest) {
  try {
    const {
      prompt,
      readerAge,
      storyFormat,
      type = 'story-idea',
      existingContent = '',
      title = '',
      storyStage = 'middle',
      storyTheme = '',
      storyGenre = ''
    } = await request.json();

    // Content safety check on the user prompt
    // Get user ID from the subscription guard middleware
    const supabase = await createServerSupabaseClient();
    const { data: { user } } = await supabase.auth.getUser();
    const userKey = user?.id ?? request.headers.get('x-forwarded-for') ?? 'anonymous';

    const promptCheck = await moderateContent(prompt || '', 'prompt', userKey);
    if (!promptCheck.isAppropriate) {
      return NextResponse.json(
        { success: false, error: promptCheck.reason, blocked: promptCheck.blocked ?? false },
        { status: 400 }
      );
    }
    console.log('prompt', prompt);
    console.log('readerAge', readerAge);
    console.log('storyFormat', storyFormat);
    console.log('type', type);
    console.log('existingContent', existingContent);
    console.log('title', title);
    console.log('storyStage', storyStage);
    console.log('storyTheme', storyTheme);
    console.log('storyGenre', storyGenre);

    // Get format-specific guidelines
    const formatGuideline = getFormatGuideline(storyFormat, readerAge);

    // Build context from available fields
    const storyContext = [
      title && `Title: "${title}"`,
      storyTheme && `Theme: ${storyTheme}`,
      storyGenre && `Genre: ${storyGenre}`,
      storyStage && `Current stage of the story: ${storyStage}`,
      existingContent && `Current story content:\n${existingContent}`
    ].filter(Boolean).join('\n');

    // Base prompt template that ensures child-friendly content
    const basePrompt = `You are a creative children's story writer. Your task is to create engaging, age-appropriate stories for children.

IMPORTANT GUIDELINES:
1. Write at a level appropriate for ${readerAge}-year-old readers
2. Keep content strictly child-friendly and positive
3. Avoid any scary, violent, or inappropriate themes
4. Focus on themes of friendship, discovery, growth, and adventure
5. Use clear, engaging language with appropriate vocabulary
6. Include descriptive details and dialogue when appropriate

${formatGuideline}

${storyContext ? `\nSTORY CONTEXT:\n${storyContext}\n` : ''}

${prompt}`;

    let generatedContentRaw: string;

    try {
      const response = await llm.invoke([
        {
          role: "user",
          content: basePrompt
        }
      ]);

      generatedContentRaw = response.content.toString();
      console.log('generatedContentRaw', generatedContentRaw);

      if (!generatedContentRaw) {
        throw new Error("Gemini API failed or returned empty result");
      }
    } catch (apiError) {
      console.error('Gemini API error:', apiError);

      // Provide fallback content when API is unavailable
      if (type === 'story-idea') {
        generatedContentRaw = `Once upon a time, there was a magical adventure waiting to unfold. A brave young explorer discovered something amazing that would change everything. What happens next in this exciting story?`;
      } else if (type === 'story-continuation') {
        generatedContentRaw = `The adventure continued as our hero discovered something wonderful and exciting that filled them with joy and curiosity.`;
      } else if (type === 'character-generation') {
        generatedContentRaw = `Meet Alex, a kind and brave young adventurer with sparkling eyes and a heart full of curiosity, always ready to help friends and explore new places.`;
      } else {
        generatedContentRaw = `Let's create an amazing story together! What wonderful adventure should we explore today?`;
      }

      console.log('Using fallback content due to API error');
    }

    // Post-moderation of AI output
    const outCheck = await moderateContent(generatedContentRaw, 'text');
    const generatedContent = outCheck.isAppropriate ? generatedContentRaw : "Let's imagine another story that's safe and fun for everyone!";

    console.log('generatedContent', generatedContent);

    return NextResponse.json({
      success: true,
      content: generatedContent,
      metadata: {
        type,
        readerAge,
        storyFormat,
        title,
        storyStage,
        storyTheme,
        storyGenre
      }
    });

  } catch (error) {
    console.error('Error generating story:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to generate story' },
      { status: 500 }
    );
  }
}

// Export the protected handler
export const POST = requireSubscription(storyGenerateHandler);