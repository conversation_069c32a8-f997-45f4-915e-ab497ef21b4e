'use client';

import { useState, useEffect } from 'react';
import { getChallenges, CMSChallenge, getMediaUrl } from '@/lib/cms-api';
import { useSubscriptionStatus } from '@/hooks/useSubscriptionStatus';

interface CMSChallengeGridProps {
  category?: string;
  ageGroup?: string;
  featured?: boolean;
}

export function CMSChallengeGrid({ category, ageGroup, featured }: CMSChallengeGridProps) {
  const [challenges, setChallenges] = useState<CMSChallenge[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { hasActiveSubscription } = useSubscriptionStatus();

  useEffect(() => {
    async function loadChallenges() {
      try {
        setLoading(true);

        // Fetch both CMS and database challenges
        const [cmsData, dbResponse] = await Promise.allSettled([
          getChallenges({ category, ageGroup, featured }),
          fetch('/api/challenges').then(res => res.ok ? res.json() : { success: false, challenges: [] })
        ]);

        let allChallenges: CMSChallenge[] = [];

        // Add CMS challenges
        if (cmsData.status === 'fulfilled') {
          allChallenges.push(...cmsData.value);
        }

        // Add database challenges (convert to CMS format)
        if (dbResponse.status === 'fulfilled' && dbResponse.value.success) {
          const dbChallenges = dbResponse.value.challenges.map((challenge: any) => ({
            id: challenge.id,
            title: challenge.title,
            description: challenge.description,
            category: challenge.type,
            ageGroup: '6-14', // Default age group for DB challenges
            difficulty: challenge.difficulty,
            subscriptionTier: 'free',
            featured: false,
            source: 'database'
          }));
          allChallenges.push(...dbChallenges);
        }

        setChallenges(allChallenges);
      } catch (err) {
        setError('Failed to load challenges');
        console.error('Error loading challenges:', err);
      } finally {
        setLoading(false);
      }
    }

    loadChallenges();
  }, [category, ageGroup, featured]);

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(6)].map((_, i) => (
          <div key={`challenge-skeleton-${i}`} className="bg-gray-200 animate-pulse rounded-lg h-64"></div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600">{error}</p>
        <button 
          onClick={() => window.location.reload()} 
          className="mt-4 px-4 py-2 bg-blue-500 text-white rounded"
        >
          Try Again
        </button>
      </div>
    );
  }

  if (challenges.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-600">No challenges found.</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {challenges.map((challenge) => (
        <CMSChallengeCard 
          key={challenge.id} 
          challenge={challenge} 
          hasSubscription={hasActiveSubscription}
        />
      ))}
    </div>
  );
}

interface CMSChallengeCardProps {
  challenge: CMSChallenge;
  hasSubscription: boolean;
}

function CMSChallengeCard({ challenge, hasSubscription }: CMSChallengeCardProps) {
  const isLocked = challenge.subscriptionTier === 'premium' && !hasSubscription;
  
  const handleClick = () => {
    if (isLocked) {
      // Show subscription prompt
      alert('This challenge requires a premium subscription!');
      return;
    }

    // Navigate to appropriate tool based on challenge category with challenge data
    const challengeData = encodeURIComponent(JSON.stringify({
      id: challenge.id,
      title: challenge.title,
      description: challenge.description,
      instructions: challenge.instructions,
      category: challenge.category,
      difficulty: challenge.difficulty,
      estimatedTime: challenge.estimatedTime,
      learningObjectives: challenge.learningObjectives,
      materials: challenge.materials
    }));

    // Route to appropriate tool based on category
    switch (challenge.category?.toLowerCase()) {
      case 'story':
        window.location.href = `/create/story?challenge=${challengeData}`;
        break;
      case 'art':
        window.location.href = `/create/art?challenge=${challengeData}`;
        break;
      case 'music':
        window.location.href = `/create/music?challenge=${challengeData}`;
        break;
      case 'game':
        window.location.href = `/create/game?challenge=${challengeData}`;
        break;
      default:
        // Fallback to challenge detail page
        window.location.href = `/challenges/${challenge.slug}`;
    }
  };

  // Get the first tutorial or example image
  const mainImage = challenge.media?.find(m => 
    m.type === 'tutorial' || m.type === 'example'
  );

  return (
    <div 
      className={`bg-white rounded-lg shadow-md overflow-hidden cursor-pointer hover:shadow-lg transition-shadow ${
        isLocked ? 'opacity-75' : ''
      }`}
      onClick={handleClick}
    >
      {/* Challenge Image */}
      {mainImage && (
        <div className="relative">
          <img 
            src={mainImage.file.url} 
            alt={mainImage.file.alt || challenge.title}
            className="w-full h-48 object-cover"
          />
          {isLocked && (
            <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
              <div className="text-white text-center">
                <span className="text-4xl">🔒</span>
                <p className="mt-2 text-sm">Premium</p>
              </div>
            </div>
          )}
          {challenge.featured && (
            <div className="absolute top-2 right-2 bg-yellow-500 text-white px-2 py-1 rounded text-xs font-bold">
              ⭐ Featured
            </div>
          )}
        </div>
      )}

      <div className="p-4">
        {/* Title */}
        <h3 className="text-lg font-semibold mb-2 line-clamp-2">{challenge.title}</h3>
        
        {/* Description */}
        <div 
          className="text-gray-600 text-sm mb-3 line-clamp-3"
          dangerouslySetInnerHTML={{ __html: challenge.description }}
        />
        
        {/* Learning Objectives */}
        {challenge.learningObjectives && challenge.learningObjectives.length > 0 && (
          <div className="mb-3">
            <p className="text-xs font-semibold text-gray-700 mb-1">You'll Learn:</p>
            <ul className="text-xs text-gray-600">
              {challenge.learningObjectives.slice(0, 2).map((obj, index) => (
                <li key={index} className="flex items-center">
                  <span className="w-1 h-1 bg-blue-500 rounded-full mr-2"></span>
                  {obj.objective}
                </li>
              ))}
            </ul>
          </div>
        )}
        
        {/* Footer */}
        <div className="flex justify-between items-center text-xs text-gray-500">
          <div className="flex items-center space-x-2">
            <span className={`px-2 py-1 rounded ${
              challenge.category === 'art' ? 'bg-pink-100 text-pink-800' :
              challenge.category === 'story' ? 'bg-purple-100 text-purple-800' :
              challenge.category === 'music' ? 'bg-green-100 text-green-800' :
              challenge.category === 'coding' ? 'bg-blue-100 text-blue-800' :
              'bg-gray-100 text-gray-800'
            }`}>
              {challenge.category}
            </span>
            <span className={`px-2 py-1 rounded ${
              challenge.difficulty === 'easy' ? 'bg-green-100 text-green-800' :
              challenge.difficulty === 'medium' ? 'bg-yellow-100 text-yellow-800' :
              'bg-red-100 text-red-800'
            }`}>
              {challenge.difficulty}
            </span>
          </div>
          <span>{challenge.estimatedTime} min</span>
        </div>
      </div>
    </div>
  );
}
