"use client";
import { useState } from 'react';
import { toast } from 'sonner';

export const useLearningMode = () => {
  const [learningMode, setLearningMode] = useState(true);
  
  const toggleLearningMode = () => {
    setLearningMode(!learningMode);
    toast.success(learningMode 
      ? "Learning mode turned off" 
      : "Learning mode turned on! You'll receive educational tips as you create.");
  };
  
  return {
    learningMode,
    toggleLearningMode
  };
};
