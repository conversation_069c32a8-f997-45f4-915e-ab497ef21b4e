import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { createServerSupabaseClient } from '@/lib/supabase/server';

// POST /api/user-content/share - Generate shareable link for user's portfolio
export async function POST(request: NextRequest) {
  try {
    // Get authenticated user
    const supabase = await createServerSupabaseClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Authentication required' 
        },
        { status: 401 }
      );
    }

    const { contentIds, message } = await request.json();

    // Use message for sharing functionality
    console.log('Sharing with message:', message);

    // Validate that all content belongs to the user
    if (contentIds && contentIds.length > 0) {
      const userContent = await prisma.userContent.findMany({
        where: {
          id: {
            in: contentIds
          },
          user_id: user.id
        }
      });

      if (userContent.length !== contentIds.length) {
        return NextResponse.json(
          { 
            success: false, 
            error: 'Some content does not belong to you' 
          },
          { status: 403 }
        );
      }
    }

    // Get user profile for sharing
    const profile = await prisma.profile.findUnique({
      where: { id: user.id },
      select: {
        full_name: true,
        email: true
      }
    });

    // Generate a simple share token (in production, use a more secure method)
    const shareToken = Buffer.from(JSON.stringify({
      userId: user.id,
      contentIds: contentIds || [],
      timestamp: Date.now()
    })).toString('base64url');

    // Create shareable URL
    const shareUrl = `${process.env.NEXT_PUBLIC_SITE_URL}/shared/${shareToken}`;

    // TODO: In a real implementation, you might want to:
    // 1. Store the share token in the database with expiration
    // 2. Send email notifications to parents
    // 3. Add privacy controls

    return NextResponse.json({
      success: true,
      shareUrl,
      shareToken,
      message: 'Share link generated successfully',
      sharedBy: profile?.full_name || 'Little Spark User',
      sharedAt: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error generating share link:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to generate share link' 
      },
      { status: 500 }
    );
  }
}
