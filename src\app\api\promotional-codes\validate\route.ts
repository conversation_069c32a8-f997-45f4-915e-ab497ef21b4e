import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { createServerSupabaseClient } from '@/lib/supabase/server';

export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerSupabaseClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { code, planId } = await request.json();

    if (!code || !planId) {
      return NextResponse.json({ 
        error: 'Promotional code and plan ID are required' 
      }, { status: 400 });
    }

    // Find the promotional code
    const promotionalCode = await prisma.promotionalCode.findUnique({
      where: { code: code.toUpperCase() },
    });

    if (!promotionalCode) {
      return NextResponse.json({ 
        valid: false,
        error: 'Invalid promotional code' 
      }, { status: 200 });
    }

    // Check if code is active
    if (!promotionalCode.is_active) {
      return NextResponse.json({ 
        valid: false,
        error: 'This promotional code is no longer active' 
      }, { status: 200 });
    }

    // Check if code has expired
    if (promotionalCode.valid_until && new Date() > promotionalCode.valid_until) {
      return NextResponse.json({ 
        valid: false,
        error: 'This promotional code has expired' 
      }, { status: 200 });
    }

    // Check if code has reached max uses
    if (promotionalCode.max_uses && promotionalCode.current_uses >= promotionalCode.max_uses) {
      return NextResponse.json({ 
        valid: false,
        error: 'This promotional code has reached its usage limit' 
      }, { status: 200 });
    }

    // Check if user has already used this promotional code
    const existingUsage = await prisma.promotionalCodeUsage.findUnique({
      where: {
        promotional_code_id_profile_id: {
          promotional_code_id: promotionalCode.id,
          profile_id: user.id,
        },
      },
    });

    if (existingUsage) {
      return NextResponse.json({ 
        valid: false,
        error: 'You have already used this promotional code' 
      }, { status: 200 });
    }

    // Get plan pricing (you'll need to implement this based on your plan structure)
    const planPricing = getPlanPricing(planId);
    if (!planPricing) {
      return NextResponse.json({ 
        error: 'Invalid plan ID' 
      }, { status: 400 });
    }

    // Calculate discount
    let discountAmount = 0;
    let finalAmount = planPricing.amount;

    if (promotionalCode.discount_type === 'percentage') {
      discountAmount = (planPricing.amount * Number(promotionalCode.discount_value)) / 100;
    } else if (promotionalCode.discount_type === 'fixed_amount') {
      discountAmount = Math.min(Number(promotionalCode.discount_value), planPricing.amount);
    }

    finalAmount = Math.max(0, planPricing.amount - discountAmount);

    return NextResponse.json({
      valid: true,
      code: promotionalCode.code,
      description: promotionalCode.description,
      discount_type: promotionalCode.discount_type,
      discount_value: promotionalCode.discount_value,
      original_amount: planPricing.amount,
      discount_amount: discountAmount,
      final_amount: finalAmount,
      currency: planPricing.currency,
    });

  } catch (error) {
    console.error('Error validating promotional code:', error);
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 });
  }
}

// Helper function to get plan pricing - you'll need to implement this based on your plan structure
function getPlanPricing(planId: string) {
  const plans = {
    'monthly-tier': { amount: 1499, currency: 'inr' }, // ₹14.99 in paise
    'quarterly-tier': { amount: 3999, currency: 'inr' }, // ₹39.99 in paise
    'annual-tier': { amount: 14999, currency: 'inr' }, // ₹149.99 in paise
  };

  return plans[planId as keyof typeof plans] || null;
}
