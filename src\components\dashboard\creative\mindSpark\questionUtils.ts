
// Main export file that re-exports all functionality from the utils directory
import { questionsByAge } from './utils/staticQuestions';
import { determineAgeGroup, INITIAL_QUESTIONS_COUNT } from './utils/ageGroups';
import { getQuestionsWithAI, getFallbackQuestions } from './utils/aiQuestions';
import { saveQuestionHistory, fetchQuestionHistory } from './utils/questionStorage';
import { saveChatHistory } from './utils/chatStorage';

// Re-export everything for backward compatibility
export {
  questionsByAge,
  determineAgeGroup,
  INITIAL_QUESTIONS_COUNT,
  getQuestionsWithAI,
  getFallbackQuestions,
  saveQuestionHistory,
  fetchQuestionHistory,
  saveChatHistory
};
