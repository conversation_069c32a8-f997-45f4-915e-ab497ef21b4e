
import React from 'react';
import { Square } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface AspectRatioSelectorProps {
  aspectRatio: string;
  setAspectRatio: (ratio: string) => void;
}

const AspectRatioSelector = ({ aspectRatio, setAspectRatio }: AspectRatioSelectorProps) => {
  return (
    <div className="space-y-4">
      <div className="block text-lg font-medium">
        Artwork Size
      </div>
      <Select value={aspectRatio} onValueChange={setAspectRatio}>
        <SelectTrigger className="w-full">
          <SelectValue placeholder="Select size" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="1:1">
            <div className="flex items-center gap-2">
              <Square className="h-4 w-4" /> Square (1:1)
            </div>
          </SelectItem>
          <SelectItem value="4:3">
            <div className="flex items-center gap-2">
              <Square className="h-4 w-3" /> Landscape (4:3)
            </div>
          </SelectItem>
          <SelectItem value="3:4">
            <div className="flex items-center gap-2">
              <Square className="h-3 w-4" /> Portrait (3:4)
            </div>
          </SelectItem>
          <SelectItem value="16:9">
            <div className="flex items-center gap-2">
              <Square className="h-4 w-6" /> Widescreen (16:9)
            </div>
          </SelectItem>
        </SelectContent>
      </Select>
    </div>
  );
};

export default AspectRatioSelector;
