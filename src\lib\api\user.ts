import { apiClient, handleApiError } from './client';
import { Profile } from './auth';

// Types for user/profile API
export interface UpdateProfileRequest {
  full_name?: string;
  avatar_url?: string;
}

export interface ProfileResponse {
  profile: Profile;
  message?: string;
}

// User Profile API service
export class UserApiService {
  /**
   * Get current user's profile
   */
  static async getProfile(): Promise<Profile> {
    try {
      const response = await apiClient.get<ProfileResponse>('/api/user/profile');
      return response.data.profile;
    } catch (error) {
      const message = handleApiError(error);
      throw new Error(message);
    }
  }

  /**
   * Update current user's profile
   */
  static async updateProfile(data: UpdateProfileRequest): Promise<Profile> {
    try {
      const response = await apiClient.put<ProfileResponse>('/api/user/profile', data);
      return response.data.profile;
    } catch (error) {
      const message = handleApiError(error);
      throw new Error(message);
    }
  }
} 