"use client";

import React from "react";
import SmoothScrolling from "@/components/common/SmoothScrolling";
import PrivacyHero from "@/components/privacy/PrivacyHero";
import PrivacyPromise from "@/components/privacy/PrivacyPromise";
import PrivacySafeguards from "@/components/privacy/PrivacySafeguards";
import PrivacyFAQ from "@/components/privacy/PrivacyFAQ";
import PrivacyPolicy from "@/components/privacy/PrivacyPolicy";
import {
    Breadcrumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Home } from "lucide-react";

const Privacy = () => {
    return (
        <div className="min-h-screen bg-white relative">
            {/* Smooth Scrolling */}
            <SmoothScrolling />

            {/* Background blobs */}
            <div className="absolute overflow-hidden w-full h-screen pointer-events-none">
                <div className="absolute -top-24 -left-24 w-96 h-96 bg-littlespark-lavender/30 rounded-full blur-3xl"></div>
                <div className="absolute top-1/3 -right-24 w-72 h-72 bg-littlespark-yellow/30 rounded-full blur-3xl"></div>
                <div className="absolute -bottom-24 left-1/4 w-80 h-80 bg-littlespark-blue/20 rounded-full blur-3xl"></div>
            </div>

            {/* Spacer for fixed navbar */}
            <div className="h-20"></div>

            {/* Main Content */}
            <div className="container mx-auto px-4 pt-8 pb-12 relative z-10">
                <div className="max-w-7xl mx-auto">
                    <Breadcrumb className="mb-8">
                        <BreadcrumbList>
                            <BreadcrumbItem>
                                <BreadcrumbLink href="/">
                                    <Home className="h-4 w-4" />
                                    <span className="sr-only">Home</span>
                                </BreadcrumbLink>
                            </BreadcrumbItem>
                            <BreadcrumbSeparator />
                            <BreadcrumbItem>
                                <BreadcrumbPage>Privacy</BreadcrumbPage>
                            </BreadcrumbItem>
                        </BreadcrumbList>
                    </Breadcrumb>
                </div>

                <div className="max-w-4xl mx-auto">
                    <PrivacyHero />
                    <PrivacyPromise />
                    <PrivacySafeguards />
                    <PrivacyFAQ />
                    <PrivacyPolicy />
                </div>
            </div>
        </div>
    );
};

export default Privacy;
