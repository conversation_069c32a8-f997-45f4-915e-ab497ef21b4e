
import React from 'react';
import { TrendingUp } from 'lucide-react';
import { Progress } from '@/components/ui/progress';

interface SkillProgressProps {
  skillLevel: number;
  skillProgress: number;
}

const SkillProgress = ({ skillLevel, skillProgress }: SkillProgressProps) => {
  return (
    <div className="mb-6">
      <div className="flex justify-between items-center mb-2">
        <div className="font-semibold flex items-center gap-1">
          <TrendingUp className="h-4 w-4 text-spark-green" /> 
          Creativity Skills
        </div>
        <div className="text-sm text-gray-600">{skillLevel}/10</div>
      </div>
      <Progress value={skillProgress} className="h-3" />
    </div>
  );
};

export default SkillProgress;
