
import React from 'react';
// import VoiceEnabledInput from '@/components/shared/VoiceEnabledInput';

interface MusicTitleInputProps {
  musicTitle: string;
  setMusicTitle: (value: string) => void;
}

const MusicTitleInput = ({ musicTitle, setMusicTitle }: MusicTitleInputProps) => {
  return (
    <div>
      <label htmlFor="musicTitle" className="block text-sm text-center font-medium mb-1">Music Title</label>
      <input
        id="musicTitle"
        type="text"
        value={musicTitle}
        onChange={(e) => setMusicTitle(e.target.value)}
        placeholder="My Awesome Theme"
        className="w-full rounded-md border px-3 py-2"
      />
    </div>
  );
};

export default MusicTitleInput;
