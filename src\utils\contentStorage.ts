export interface ContentMetadata {
  fullContent?: string;
  preview?: string;
  prompt?: string;
  artStyle?: string;
  aspectRatio?: string;
  storyTheme?: string;
  storyGenre?: string;
  storyStage?: string;
  description?: string;
  mood?: string;
  style?: string;
  generationId?: string;
}

export interface ContentPayload {
  type: string;
  title: string;
  user_id?: string;
  content_metadata: ContentMetadata;
  preview_url: string | null;
  challenge_id?: string; // Link content to a specific challenge
  content_hash?: string; // For duplicate prevention
}

// Generate a simple hash for content to prevent duplicates
function generateContentHash(payload: ContentPayload): string {
  const hashContent = JSON.stringify({
    type: payload.type,
    title: payload.title,
    preview_url: payload.preview_url,
    // Include key metadata for hashing
    metadata: payload.content_metadata
  });

  // Simple hash function (in production, consider using crypto.subtle.digest)
  let hash = 0;
  for (let i = 0; i < hashContent.length; i++) {
    const char = hashContent.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash).toString(36);
}

export const saveUserContent = async (payload: ContentPayload): Promise<boolean> => {
  try {
    // Get authenticated user if user_id not provided
    let userId = payload.user_id;

    if (!userId) {
      // Import dynamically to avoid SSR issues
      const { createBrowserSupabaseClient } = await import('@/lib/supabase/client');
      const supabase = createBrowserSupabaseClient();
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        console.error('No authenticated user found');
        return false;
      }

      userId = user.id;
    }

    // Generate content hash for duplicate prevention
    const contentHash = payload.content_hash || generateContentHash(payload);

    // Call the API endpoint to save content
    const response = await fetch('/api/user-content', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        user_id: userId,
        type: payload.type,
        title: payload.title,
        content_metadata: payload.content_metadata,
        preview_url: payload.preview_url,
        challenge_id: payload.challenge_id,
        content_hash: contentHash
      })
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));

      // Handle duplicate content specifically
      if (response.status === 409 && errorData.duplicate) {
        console.warn('Content already saved:', errorData);
        return false; // Return false to indicate duplicate
      }

      console.error('Failed to save content:', errorData);
      return false;
    }

    const result = await response.json();
    console.log('Content saved successfully:', result);
    return true;

  } catch (error) {
    console.error('Error saving content:', error);
    return false;
  }
};