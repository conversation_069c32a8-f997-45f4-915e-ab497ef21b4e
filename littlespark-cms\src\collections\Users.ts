import type { CollectionConfig } from 'payload'

export const Users: CollectionConfig = {
  slug: 'users',
  admin: {
    useAsTitle: 'email',
    defaultColumns: ['email', 'firstName', 'lastName', 'role'],
    group: 'Admin',
  },
  auth: true,
  access: {
    create: ({ req: { user } }) => {
      // Only admins can create new admin users
      return user?.role === 'admin'
    },
    read: ({ req: { user } }) => {
      // Users can read their own profile, admins can read all
      if (user?.role === 'admin') return true
      return {
        id: {
          equals: user?.id,
        },
      }
    },
    update: ({ req: { user } }) => {
      // Users can update their own profile, admins can update all
      if (user?.role === 'admin') return true
      return {
        id: {
          equals: user?.id,
        },
      }
    },
    delete: ({ req: { user } }) => {
      // Only admins can delete users
      return user?.role === 'admin'
    },
  },
  fields: [
    {
      name: 'firstName',
      type: 'text',
      required: true,
    },
    {
      name: 'lastName',
      type: 'text',
      required: true,
    },
    {
      name: 'role',
      type: 'select',
      required: true,
      options: [
        { label: 'Super Admin', value: 'admin' },
        { label: 'Content Creator', value: 'content-creator' },
        { label: 'Educator', value: 'educator' },
        { label: 'Reviewer', value: 'reviewer' },
      ],
      defaultValue: 'educator',
      admin: {
        description: 'User role determines access permissions',
      },
    },
    {
      name: 'bio',
      type: 'textarea',
      admin: {
        description: 'Brief bio about the content creator/educator',
      },
    },
    {
      name: 'avatar',
      type: 'upload',
      relationTo: 'media',
      admin: {
        description: 'Profile picture',
      },
    },
    {
      name: 'specialties',
      type: 'array',
      fields: [
        {
          name: 'specialty',
          type: 'select',
          options: [
            { label: 'Art & Drawing', value: 'art' },
            { label: 'Creative Writing', value: 'story' },
            { label: 'Music & Sound', value: 'music' },
            { label: 'Coding & Logic', value: 'coding' },
            { label: 'Video Creation', value: 'video' },
            { label: 'Game Design', value: 'game' },
          ],
        },
      ],
      admin: {
        description: 'Areas of expertise for content creators',
      },
    },
    {
      name: 'isActive',
      type: 'checkbox',
      defaultValue: true,
      admin: {
        description: 'Whether this user account is active',
      },
    },
  ],
}
