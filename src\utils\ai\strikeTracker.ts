// Simple localStorage-based strike tracker to complement server-side moderation
// Blocks the user for BLOCK_MINUTES after STRIKE_LIMIT local violations.

const STRIKE_LIMIT = 3;
const BLOCK_MINUTES = 5;

interface StrikeData {
  count: number;
  blockedUntil: number; // epoch ms
}

const STORAGE_KEY = 'ls_strikes';

const now = () => Date.now();

const load = (): StrikeData => {
  if (typeof window === 'undefined') return { count: 0, blockedUntil: 0 };
  try {
    const raw = localStorage.getItem(STORAGE_KEY);
    if (!raw) return { count: 0, blockedUntil: 0 };
    const parsed = JSON.parse(raw);
    return { count: parsed.count || 0, blockedUntil: parsed.blockedUntil || 0 };
  } catch {
    return { count: 0, blockedUntil: 0 };
  }
};

const save = (data: StrikeData) => {
  if (typeof window === 'undefined') return;
  localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
};

export const incrementStrike = (): StrikeData => {
  // Skip strike tracking in development environment
  if (process.env.NODE_ENV === 'development') {
    console.log('[DEV] Skipping strike increment in development mode');
    return { count: 0, blockedUntil: 0 };
  }

  const data = load();
  // If already blocked, keep same blockedUntil
  if (data.blockedUntil && data.blockedUntil > now()) {
    return data;
  }
  data.count += 1;
  if (data.count >= STRIKE_LIMIT) {
    data.blockedUntil = now() + BLOCK_MINUTES * 60 * 1000;
    data.count = 0;
  }
  save(data);
  return data;
};

export const resetStrikes = () => {
  save({ count: 0, blockedUntil: 0 });
};

// Clear any existing blocks in development mode
export const clearDevelopmentBlocks = () => {
  if (process.env.NODE_ENV === 'development') {
    console.log('[DEV] Clearing any existing content safety blocks');
    resetStrikes();
  }
};

export const isBlocked = (): boolean => {
  // Skip blocking in development environment
  if (process.env.NODE_ENV === 'development') {
    return false;
  }

  const { blockedUntil } = load();
  return blockedUntil > now();
};

export const getRemainingBlockMinutes = (): number => {
  // Skip blocking in development environment
  if (process.env.NODE_ENV === 'development') {
    return 0;
  }

  const { blockedUntil } = load();
  if (blockedUntil <= now()) return 0;
  return Math.ceil((blockedUntil - now()) / 60000);
};