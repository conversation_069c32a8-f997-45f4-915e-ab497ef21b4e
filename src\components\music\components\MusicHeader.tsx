
import React from 'react';
import { Music, ChevronLeft } from 'lucide-react';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import LearningModeToggle from '@/components/shared/LearningModeToggle';
import { MusicHelpButton } from './MusicHelpDialog';
import SafeModeIndicator from '@/components/shared/SafeModeIndicator';

interface MusicHeaderProps {
  learningMode: boolean;
  toggleLearningMode: () => void;
  onHelpClick: () => void;
}

const MusicHeader = ({ learningMode, toggleLearningMode, onHelpClick }: MusicHeaderProps) => {
  return (
    <div>
      <div className="mb-4 text-left">
        <Link href="/dashboard">
          <Button variant="ghost" size="sm" className="text-left">
            <ChevronLeft className="mr-1" />
            Back to Dashboard
          </Button>
        </Link>
      </div>
      
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4 lg:gap-0 mb-6">
        <div>
          <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold font-quicksand flex items-center">
            <Music className="mr-2 h-6 w-6 sm:h-7 sm:w-7 lg:h-8 lg:w-8 text-spark-lavender" />
            <span className="sm:hidden">Music Studio</span>
            <span className="hidden sm:inline">Music Composer Studio</span>
          </h1>
          <div className="mt-2">
            <SafeModeIndicator type="music" />
          </div>
        </div>
        
        <div className="flex items-center gap-2 sm:gap-4 justify-end lg:justify-start">
          <LearningModeToggle 
            learningMode={learningMode}
            toggleLearningMode={toggleLearningMode}
          />
          <MusicHelpButton onClick={onHelpClick} />
        </div>
      </div>
    </div>
  );
};

export default MusicHeader;
