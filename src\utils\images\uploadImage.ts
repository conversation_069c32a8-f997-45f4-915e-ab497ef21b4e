
import { toast } from 'sonner';

/**
 * Handles user image uploads, processes them for coloring
 */
export const handleImageUpload = (
  file: File | null, 
  canvasRef: React.RefObject<HTMLCanvasElement>,
  setSelectedImage: (img: string) => void,
  // { toast: contextToast }: { toast: any }
) => {
  if (!file) {
    toast.error("No file selected");
    return;
  }
  
  // Check if the file is an image
  if (!file.type.match('image.*')) {
    toast.error("Please upload an image file (JPEG, PNG, etc.)");
    return;
  }
  
  // Show loading state
  const canvas = canvasRef.current;
  if (canvas) {
    const ctx = canvas.getContext('2d');
    if (ctx) {
      ctx.fillStyle = '#f8f9fa';
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      ctx.font = '16px Arial';
      ctx.textAlign = 'center';
      ctx.fillStyle = '#6c757d';
      ctx.fillText('Processing your image...', canvas.width/2, canvas.height/2);
    }
  }
  
  const reader = new FileReader();
  reader.onload = (event) => {
    if (!event.target?.result) return;
    
    const img = new Image();
    img.onload = () => {
      const canvas = canvasRef.current;
      if (!canvas) return;
      
      const ctx = canvas.getContext('2d');
      if (!ctx) return;
      
      // Clear canvas
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      // Calculate aspect ratio to maintain proportions
      const hRatio = canvas.width / img.width;
      const vRatio = canvas.height / img.height;
      const ratio = Math.min(hRatio, vRatio);
      
      // Calculate centered position
      const centerX = (canvas.width - img.width * ratio) / 2;
      const centerY = (canvas.height - img.height * ratio) / 2;
      
      // Draw image preserving aspect ratio
      ctx.drawImage(img, 0, 0, img.width, img.height, 
                   centerX, centerY, img.width * ratio, img.height * ratio);
      
      // Apply improved coloring page effect
      applyColoringPageEffect(canvas, ctx);
      
      setSelectedImage(event?.target?.result as string);
      
      toast.success("Image uploaded successfully! Start coloring!");
    };
    
    img.onerror = () => {
      toast.error("Failed to load the uploaded image. Please try again.");
    };
    
    img.src = event.target.result as string;
  };
  
  reader.onerror = () => {
    toast.error("Failed to read the uploaded file. Please try again.");
  };
  
  reader.readAsDataURL(file);
};

/**
 * Applies coloring page effect to an image on canvas with improved outline detection
 */
const applyColoringPageEffect = (
  canvas: HTMLCanvasElement, 
  ctx: CanvasRenderingContext2D
) => {
  if (canvas.width > 0 && canvas.height > 0) {
    try {
      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
      const data = imageData.data;
      
      // Apply Sobel edge detection for better outlines
      const edges = detectEdges(imageData);
      
      // Create a new image with white background and black edges
      for (let i = 0; i < data.length; i += 4) {
        // Make everything white first (background)
        data[i] = 255;     // R
        data[i + 1] = 255; // G
        data[i + 2] = 255; // B
        
        // If we detected an edge, make it black
        if (edges[i/4] > 30) {  // Threshold for edge detection sensitivity
          data[i] = 0;      // R
          data[i + 1] = 0;  // G
          data[i + 2] = 0;  // B
        }
      }
      
      ctx.putImageData(imageData, 0, 0);
    } catch (e) {
      console.error("Error processing image data:", e);
      // If image processing fails, try a simpler approach
      applySimpleOutlineEffect(canvas, ctx);
    }
  }
};

/**
 * Fallback simple outline effect if the edge detection fails
 */
const applySimpleOutlineEffect = (
  canvas: HTMLCanvasElement,
  ctx: CanvasRenderingContext2D
) => {
  try {
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const data = imageData.data;
    
    // Simple greyscale conversion with threshold for outline effect
    for (let i = 0; i < data.length; i += 4) {
      const avg = (data[i] + data[i + 1] + data[i + 2]) / 3;
      
      // Apply threshold for outline effect - if close to white, make it white, otherwise dark
      if (avg > 200) {
        data[i] = 255; // R
        data[i + 1] = 255; // G
        data[i + 2] = 255; // B
      } else {
        data[i] = 0; // R
        data[i + 1] = 0; // G
        data[i + 2] = 0; // B
      }
    }
    
    ctx.putImageData(imageData, 0, 0);
  } catch (e) {
    console.error("Error in simple outline effect:", e);
  }
};

/**
 * Edge detection using Sobel operator
 * Returns an array of edge magnitudes
 */
const detectEdges = (imageData: ImageData): number[] => {
  const width = imageData.width;
  const height = imageData.height;
  const data = imageData.data;
  const edges = new Array(width * height).fill(0);
  
  // Sobel kernels for edge detection
  const sobelX = [-1, 0, 1, -2, 0, 2, -1, 0, 1];
  const sobelY = [-1, -2, -1, 0, 0, 0, 1, 2, 1];
  
  // Apply Sobel operator to each pixel (except the border)
  for (let y = 1; y < height - 1; y++) {
    for (let x = 1; x < width - 1; x++) {
      let magX = 0;
      let magY = 0;
      
      // Apply convolution
      for (let ky = -1; ky <= 1; ky++) {
        for (let kx = -1; kx <= 1; kx++) {
          const idx = ((y + ky) * width + (x + kx)) * 4;
          const kernelIdx = (ky + 1) * 3 + (kx + 1);
          
          // Use grayscale value (average of RGB)
          const pixelValue = (data[idx] + data[idx + 1] + data[idx + 2]) / 3;
          
          magX += pixelValue * sobelX[kernelIdx];
          magY += pixelValue * sobelY[kernelIdx];
        }
      }
      
      // Calculate magnitude
      const magnitude = Math.sqrt(magX * magX + magY * magY);
      edges[y * width + x] = magnitude;
    }
  }
  
  return edges;
};
