import { <PERSON>ather, Paintbrush, Palette, Music, Gamepad2, Video, Bot, <PERSON>, <PERSON>rkles, Book, PenTool, Share2, Shield, Ban, Eye, Download } from 'lucide-react';

// ============================================================================
// MARQUEE BANNER CONSTANTS
// ============================================================================
export const marqueeItems = [
  { text: "Storytelling", color: "text-littlespark-primary" },
  { text: "Art Creation", color: "text-littlespark-blue" },
  { text: "Music Composition", color: "text-littlespark-orange" },
  { text: "Game Design", color: "text-littlespark-primary" },
  { text: "Video Creation", color: "text-littlespark-yellow" },
  { text: "AI Coloring", color: "text-littlespark-lavender" },
  { text: "Critical Thinking", color: "text-littlespark-primary" },
  { text: "Digital Literacy", color: "text-littlespark-blue" },
];

// ============================================================================
// FEATURES SECTION CONSTANTS
// ============================================================================
export const features = [
  {
    title: "Story Creator",
    description: "Turn ideas into adventures! With gentle AI guidance, kids can write their own stories and expand their imagination—one creative twist at a time.",
    icon: Feather,
    iconColor: "text-littlespark-pink",
    borderColor: "border-littlespark-pink",
    delay: "0.2s"
  },
  {
    title: "Art Studio",
    description: "Watch their ideas come to life! Kids can create vibrant artwork using easy text prompts—no artistic experience needed, just imagination.",
    icon: Paintbrush,
    iconColor: "text-littlespark-blue",
    borderColor: "border-littlespark-blue",
    delay: "0.3s"
  },
  {
    title: "Coloring Pages",
    description: "Choose from ready-to-color pages or upload your own—then bring them to life with a rainbow of digital colors and tools.",
    icon: Palette,
    iconColor: "text-littlespark-teal",
    borderColor: "border-littlespark-teal",
    delay: "0.4s"
  },
  {
    title: "Music Maker",
    description: "Compose playful tunes using kid-friendly AI tools designed to help little creators explore sound, rhythm, and melody in a fun way.",
    icon: Music,
    iconColor: "text-littlespark-lavender",
    borderColor: "border-littlespark-lavender",
    delay: "0.5s"
  },
  {
    title: "Game Designer",
    description: "Create fun, interactive games—no coding required! Kids get step-by-step guidance from AI to design their own games and bring their ideas to life.",
    icon: Gamepad2,
    iconColor: "text-littlespark-yellow",
    borderColor: "border-littlespark-yellow",
    delay: "0.6s"
  },
  {
    title: "Video Creator",
    description: "Bring stories to the screen! Kids can turn their ideas into short animated videos with easy AI tools that help shape characters, scenes, and dialogue.",
    icon: Video,
    iconColor: "text-littlespark-orange",
    borderColor: "border-littlespark-orange",
    delay: "0.7s"
  },
  {
    title: "AI Buddy",
    description: "Meet your new sidekick! Whether it's an Owl, Explorer, or Robot, this friendly chatbot is here to answer questions, share fun facts, and chat about your child's favorite topics—all in a safe, kid-friendly way.",
    icon: Bot,
    iconColor: "text-littlespark-blue",
    borderColor: "border-littlespark-blue",
    delay: "0.8s"
  },
  {
    title: "Creature Coder",
    description: "Bring imaginary creatures to life! Kids can code their own characters using simple step-by-step tools—customizing how they move, sound, and act in a magical, hands-on coding adventure.",
    icon: Code,
    iconColor: "text-littlespark-teal",
    borderColor: "border-littlespark-teal",
    delay: "0.9s"
  }
];

// Features organized by layout rows
export const firstRowFeatures = features.slice(0, 3);
export const secondRowFeatures = features.slice(3, 6);
export const thirdRowFeatures = features.slice(6, 8);

// ============================================================================
// HOW IT WORKS SECTION CONSTANTS
// ============================================================================
export const howItWorksSteps = [
  {
    icon: Sparkles,
    color: "littlespark-pink",
    colorHex: "#ec4899",
    title: "Choose a Creative Tool",
    description: "From stories to games, kids pick what they want to make.",
    delay: "0.2s"
  },
  {
    icon: Book,
    color: "littlespark-blue",
    colorHex: "#3b82f6",
    title: "Start with an Idea",
    description: "They write a word, sentence, or theme—and the AI offers fun, age-appropriate suggestions to help them get started.",
    delay: "0.4s"
  },
  {
    icon: PenTool,
    color: "littlespark-orange",
    colorHex: "#f97316",
    title: "Create together",
    description: "AI becomes their creative sidekick—guiding, supporting, and encouraging them while they stay in the driver's seat.",
    delay: "0.6s"
  },
  {
    icon: Palette,
    color: "littlespark-primary",
    colorHex: "#00c69e",
    title: "Make It Their Own",
    description: "They can customize with drawings, characters, music, or words to match their unique style and imagination.",
    delay: "0.8s"
  },
  {
    icon: Share2,
    color: "littlespark-yellow",
    colorHex: "#F5E45F",
    title: "Save or Share",
    description: "Every project is saved privately and can be downloaded to share proudly with family and friends.",
    delay: "1.0s"
  }
];

// ============================================================================
// SAFETY SECTION CONSTANTS
// ============================================================================
export const safetyFeatures = [
  {
    icon: Shield,
    color: "littlespark-blue",
    title: "Age-Appropriate Tools",
    description: "Designed for all skill levels, even pre-readers, with intuitive interfaces that grow with your child.",
    delay: "0.2s"
  },
  {
    icon: Ban,
    color: "littlespark-pink",
    title: "No AI Training",
    description: "We explicitly prevent your child's content from being used to train AI systems. Their creativity remains their own.",
    delay: "0.4s"
  },
  {
    icon: Eye,
    color: "littlespark-orange",
    title: "Parental Control",
    description: "Complete visibility into your child's creations with easy-to-use content moderation options for peace of mind.",
    delay: "0.6s"
  },
  {
    icon: Download,
    color: "littlespark-blue",
    title: "Download-Only Sharing",
    description: "Share creations only when you want to. Downloads give you full control over what leaves the app.",
    delay: "0.8s"
  }
];

export const privacyCommitments = [
  "No data sharing with third-party services",
  "AI processing occurs through secure, encrypted connections",
  "Uploads and chat history are never used for AI training",
  "Download your creations at any time with one click"
];

// ============================================================================
// TESTIMONIALS SECTION CONSTANTS
// ============================================================================
export interface Testimonial {
  id: string;
  content: string;
  author: string;
  role: string;
  rating: number;
  avatar_url: string;
}

export const testimonials: Testimonial[] = [
  {
    id: '1',
    content: "Little Spark has transformed our family screen time. Instead of just consuming content, my kids are creating their own stories and art. They're learning while having fun!",
    author: "Sarah K.",
    role: "Parent of 8 & 10 year olds",
    rating: 5,
    avatar_url: "https://images.unsplash.com/photo-1579503841516-e0bd7fca5faa?crop=entropy&cs=tinysrgb&fit=crop&fm=jpg&h=150&ixid=MnwxfDB8MXxyYW5kb218MHx8cGVyc29ufHx8fHx8MTY4ODY1NjYyMA&ixlib=rb-4.0.3&q=80&w=150"
  },
  {
    id: '2',
    content: "My daughter struggled with writing and storytelling, but Little Spark has made it so approachable and fun. The AI guides her just enough without taking over the creative process.",
    author: "Michael T.",
    role: "Dad of a 9-year-old",
    rating: 5,
    avatar_url: "https://images.unsplash.com/photo-1605462863863-10d9e47e15ee?crop=entropy&cs=tinysrgb&fit=crop&fm=jpg&h=150&ixid=MnwxfDB8MXxyYW5kb218MHx8bWFufHx8fHx8MTY4ODY1NjYyMQ&ixlib=rb-4.0.3&q=80&w=150"
  },
  {
    id: '3',
    content: "As a teacher, I've seen how Little Spark encourages even reluctant students to engage with creative writing and digital art. The AI suggestions help overcome the intimidation of a blank page.",
    author: "Lisa M.",
    role: "Elementary School Teacher",
    rating: 5,
    avatar_url: "https://images.unsplash.com/photo-1573497019940-1c28c88b4f3e?crop=entropy&cs=tinysrgb&fit=crop&fm=jpg&h=150&ixid=MnwxfDB8MXxyYW5kb218MHx8d29tYW58fHx8fHwxNjg4NjU2NjIx&ixlib=rb-4.0.3&q=80&w=150"
  },
  {
    id: '4',
    content: "My son is neurodivergent and often struggles with expressing his ideas. Little Spark has been a game-changer, giving him tools to bring his incredibly creative thoughts to life.",
    author: "Jamie R.",
    role: "Parent & Child Development Specialist",
    rating: 5,
    avatar_url: "https://images.unsplash.com/photo-1580489944761-15a19d654956?crop=entropy&cs=tinysrgb&fit=crop&fm=jpg&h=150&ixid=MnwxfDB8MXxyYW5kb218MHx8d29tYW58fHx8fHx8MTY4ODY1NjYyMQ&ixlib=rb-4.0.3&q=80&w=150"
  },
  {
    id: '5',
    content: "We started with the free version and upgraded within a week. The premium features unlocked so many creative possibilities that my kids use daily. Well worth the investment!",
    author: "David L.",
    role: "Father of three",
    rating: 4,
    avatar_url: "https://images.unsplash.com/photo-1568602471122-7832951cc4c5?crop=entropy&cs=tinysrgb&fit=crop&fm=jpg&h=150&ixid=MnwxfDB8MXxyYW5kb218MHx8bWFufHx8fHx8MTY4ODY1NjYyMQ&ixlib=rb-4.0.3&q=80&w=150"
  },
  {
    id: '6',
    content: "I love how Little Spark gives my kids a safe introduction to AI. They're learning how to collaborate with technology rather than just consuming it. It's preparing them for the future!",
    author: "Nina C.",
    role: "Tech-savvy mom",
    rating: 5,
    avatar_url: "https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?crop=entropy&cs=tinysrgb&fit=crop&fm=jpg&h=150&ixid=MnwxfDB8MXxyYW5kb218MHx8d29tYW58fHx8fHx8MTY4ODY1NjYyMQ&ixlib=rb-4.0.3&q=80&w=150"
  }
];

// ============================================================================
// NAVIGATION CONSTANTS
// ============================================================================
export const navigationLinks = [
  { href: "/pricing", label: "Pricing" },
  { href: "/about", label: "About" },
  { href: "/gift", label: "Gift Little Spark" },
  { href: "/faq", label: "FAQ" },
];

// ============================================================================
// BRAND CONSTANTS
// ============================================================================
export const brandAssets = {
  logo: {
    src: "https://littlespark.ai/lovable-uploads/cfb63cf2-3b7c-4355-9fd5-721735667d10.png",
    alt: "Little Spark",
    width: 100,
    height: 100,
  },
  dashboardPreview: {
    src: "https://littlespark.ai/lovable-uploads/4794359f-412d-4e9b-8326-00effde5fb25.png",
    alt: "Little Spark Dashboard Preview",
    width: 600,
    height: 400,
  },
};

// ============================================================================
// TEXT CONTENT CONSTANTS
// ============================================================================
export const heroContent = {
  title: {
    main: "Ignite Your Child's",
    highlight: "Creativity",
    suffix: "With AI",
  },
  subtitle: "A safe, playful space where kids ages 5–13 can explore AI through storytelling, art, music, and more—building confidence, creativity, and tech skills along the way.",
  buttons: {
    primary: "Get Started for Free",
    secondary: "How It Works",
  },
};

export const featuresContent = {
  title: "Create with AI, the Fun & Safe Way",
  subtitle: "Turn screen time into creative time. From storytelling to music, LittleSpark gives your child tools to imagine, build, and create—while learning how to use AI with care and confidence.",
};

export const howItWorksContent = {
  title: "How Little Spark Works",
  subtitle: "A simple step-by-step process to unleash your child's creativity with AI",
};

export const safetyContent = {
  title: "Created for Kids. Built for Peace of Mind.",
  subtitle: "We prioritize your child's safety and privacy. All creative content stays within the tool.",
  privacyTitle: "Our Privacy Commitment",
  privacyButton: "View Our Full Privacy Policy",
};

export const testimonialsContent = {
  title: "What Little Spark Families Are Saying",
  subtitle: "See how Little Spark is helping kids build creativity, confidence, and curiosity—with AI made just for them.",
};

export const ctaContent = {
  title: "Start Creating with Little Spark—Made for Curious Kids & Peace-of-Mind Parents",
  subtitle: "Give your child a fun, safe space to imagine, build, and explore—all with gentle support from AI that's designed just for them.",
  buttons: {
    primary: "Get Started for Free",
    secondary: "See Pricing",
  },
};

// ============================================================================
// FAQ SECTION CONSTANTS
// ============================================================================
export interface FAQItem {
  question: string;
  answer: string;
}

export const faqData: FAQItem[] = [
  {
    question: "What age group is Little Spark designed for?",
    answer: "Little Spark is designed for children aged 5-13. The interface and AI responses are tailored to be engaging and appropriate for this age range, with complexity and topics that adjust based on the age level you select."
  },
  {
    question: "Is there any inappropriate content my child might encounter?",
    answer: "No. Little Spark uses advanced content filtering and age-appropriate AI models specifically trained for children. All content is moderated in real-time, and we have strict safety protocols to ensure your child only encounters content suitable for their age group. Additionally, parents have full visibility and control over their child's creations."
  },
  {
    question: "How does the free trial work?",
    answer: "Our free trial gives you and your child access to basic creative tools for 7 days. You can explore story creation, simple art projects, and basic AI interactions. No credit card required to start. After the trial, you can choose to upgrade to our premium plan for access to all features, including advanced tools, unlimited projects, and priority support."
  },
  {
    question: "Can multiple children use the same account?",
    answer: "Yes! Our family plans support multiple child profiles under one parent account. Each child gets their own secure space with age-appropriate settings, and parents can manage all profiles from a single dashboard. You can add up to 4 children on our standard family plan."
  },
  {
    question: "How do I cancel my subscription?",
    answer: "You can cancel your subscription anytime through your account settings. Simply go to the billing section in your parent dashboard and click 'Cancel Subscription.' Your access will continue until the end of your current billing period, and all your child's projects will remain safely stored for download."
  },
  {
    question: "What data does Little Spark collect about my child?",
    answer: "We collect minimal data necessary for the service to function: basic profile information (age range, first name), creative projects, and usage patterns to improve the experience. We never collect personal information like full names, addresses, or photos. All data is encrypted, never shared with third parties, and never used to train AI models. Parents have full control and can request data deletion at any time."
  }
];

export const faqContent = {
  title: "Frequently Asked Questions",
  subtitle: "Find quick answers to common questions about how our platform works, how it supports your child's creativity, and what makes it a safe space for imaginative exploration.",
};

// ============================================================================
// FOOTER SECTION CONSTANTS
// ============================================================================
export const footerContent = {
  company: {
    name: "Little Spark",
    tagline: "Inspiring creativity, confidence, and curiosity—through safe, kid-friendly AI.",
  },
  contact: {
    title: "Contact Us",
    email: "<EMAIL>",
  },
  newsletter: {
    title: "Newsletter",
    placeholder: "Enter your email",
    buttonText: "Sign Up",
    buttonSubmitting: "Signing up...",
    description: "Stay updated with new features and announcements",
  },
  links: [
    { href: "/pricing", label: "Pricing" },
    { href: "/about", label: "About Us" },
    { href: "/privacy", label: "Privacy Policy" },
    { href: "/terms", label: "Terms of Service" },
    { href: "/blog", label: "Blog" },
  ],
  bottomLinks: [
    { href: "/privacy", label: "Privacy" },
    { href: "/terms", label: "Terms" },
    { href: "/cookies", label: "Cookies" },
    { href: "/sitemap", label: "Sitemap" },
  ],
  social: {
    instagram: "https://instagram.com/littlespark.ai/",
  },
  copyright: "Little Spark. All rights reserved.",
};

// ============================================================================
// ANIMATION CONSTANTS
// ============================================================================
export const animationDelays = {
  header: "0.1s",
  stagger: {
    start: 0.2,
    increment: 0.1,
  },
}; 