import React, { useEffect } from 'react';

interface RewardfulScriptProps {
  apiKey: string;
}

const RewardfulScript: React.FC<RewardfulScriptProps> = ({ apiKey }) => {
  useEffect(() => {
    // For now, we'll just log that ThriveCart is being used instead
    console.log("ThriveCart is being used instead of Rewardful affiliate platform");
    
    // This is a placeholder for the previous Rewardful implementation
    // We're keeping the component to avoid breaking imports, but it doesn't do anything
    return () => {
      // Cleanup function
    };
  }, [apiKey]);

  return null; // This component doesn't render anything
};

export default RewardfulScript;
