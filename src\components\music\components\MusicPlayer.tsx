import React from 'react';
import AudioPlayer from 'react-h5-audio-player';
import 'react-h5-audio-player/lib/styles.css';

interface MusicPlayerProps {
  audioUrl: string;
  title?: string;
} 

const MusicPlayer: React.FC<MusicPlayerProps> = ({ audioUrl, title }) => (
  <div className="bg-white p-6 rounded-xl shadow-md border">
    {title && (
      <h3 className="text-lg font-semibold mb-4 text-gray-800">{title}</h3>
    )}
    <AudioPlayer
      src={audioUrl}
      header={title}
      autoPlay={false}
      showSkipControls={false}
      showJumpControls={false}
      customAdditionalControls={[]}
      layout="stacked-reverse"
      className="rounded-lg"
    />
  </div>
);

export default MusicPlayer; 