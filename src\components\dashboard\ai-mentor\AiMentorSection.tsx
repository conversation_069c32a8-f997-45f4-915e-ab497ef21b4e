"use client";

import React from 'react';
import { MentorCharacterSelector } from './MentorCharacterSelector';
import { MentorNameEditor } from './MentorNameEditor';
import AiMentor from '@/components/AiMentor';
import { useAiMentor } from './hooks/useAiMentor';
import { ChatSection } from './ChatSection';

export const AiMentorSection: React.FC = () => {
  const {
    mentor<PERSON><PERSON>cter,
    mentorMessage,
    isEditingName,
    mentorName,
    mentorNames,
    userMessage,
    chatHistory,
    isProcessing,
    setMentorName,
    setUserMessage,
    setIsEditingName,
    changeMentorCharacter,
    handleSaveName,
    handleSendMessage,
    clearChat
  } = useAiMentor();

  return (
    <div className="bg-gradient-to-b from-spark-lavender/10 to-spark-blue/10 rounded-2xl p-6 shadow-md mb-8">
      <div className="mb-4 text-center">
        <h2 className="text-2xl font-bold mb-2">My AI Buddy</h2>
        <p className="text-sm text-gray-600 mb-4">Choose your creative assistant!</p>
        
        <MentorCharacterSelector 
          mentorCharacter={mentorCharacter} 
          onChange={changeMentorCharacter} 
        />
        
        <MentorNameEditor 
          isEditing={isEditingName}
          mentorName={mentorName}
          setMentorName={setMentorName}
          onSave={handleSaveName}
          setIsEditing={setIsEditingName}
          mentorCharacter={mentorCharacter}
          mentorNames={mentorNames}
        />
      </div>
      
      <div className="flex flex-col md:flex-row gap-6 items-start">
        <div className="w-full md:w-1/3 flex-shrink-0">
          <AiMentor 
            character={mentorCharacter} 
            message={mentorMessage}
            name={mentorNames[mentorCharacter]}
            showControls={false}
          />
        </div>
        
        <div className="w-full md:w-2/3">
          <ChatSection
            chatHistory={chatHistory}
            isProcessing={isProcessing}
            userMessage={userMessage}
            setUserMessage={setUserMessage}
            handleSendMessage={handleSendMessage}
            clearChat={clearChat}
            mentorNames={mentorNames}
            mentorCharacter={mentorCharacter}
          />
        </div>
      </div>
    </div>
  );
};
