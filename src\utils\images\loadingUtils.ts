
/**
 * Creates a loading indicator on canvas
 * @returns Animation frame ID for cleanup
 */
export const createLoadingIndicator = (
  canvas: HTMLCanvasElement,
  ctx: CanvasRenderingContext2D
): number => {
  // Draw loading background
  ctx.fillStyle = '#f8f9fa';
  ctx.fillRect(0, 0, canvas.width, canvas.height);
  
  // Draw fancy text
  ctx.font = 'bold 20px Arial';
  ctx.textAlign = 'center';
  ctx.fillStyle = '#3b82f6';
  ctx.fillText('Creating your coloring page...', canvas.width/2, canvas.height/2 - 30);
  
  ctx.font = '16px Arial';
  ctx.fillStyle = '#6c757d';
  ctx.fillText('This may take a few moments', canvas.width/2, canvas.height/2);
  
  // Draw loading spinner
  const centerX = canvas.width / 2;
  const centerY = canvas.height / 2 + 40;
  const radius = 20;
  
  // Draw spinning animation
  let angle = 0;
  let dots = '';
  let dotCount = 0;
  let lastDotUpdate = Date.now();
  
  const drawSpinner = () => {
    // Update the spinner
    ctx.clearRect(centerX - radius - 15, centerY - radius - 15, radius * 2 + 30, radius * 2 + 30);
    
    // Draw circle segments
    for (let i = 0; i < 8; i++) {
      const segmentAngle = angle + (i * Math.PI / 4);
      const opacity = 0.3 + (i * 0.1);
      
      ctx.beginPath();
      ctx.arc(centerX, centerY, radius, segmentAngle, segmentAngle + Math.PI/8);
      ctx.strokeStyle = `rgba(59, 130, 246, ${opacity})`;
      ctx.lineWidth = 4;
      ctx.stroke();
    }
    
    // Update dots animation
    if (Date.now() - lastDotUpdate > 500) {
      dotCount = (dotCount + 1) % 4;
      dots = '.'.repeat(dotCount);
      lastDotUpdate = Date.now();
      
      // Clear text area and redraw
      ctx.clearRect(centerX - 100, centerY + radius + 5, 200, 30);
      ctx.font = '16px Arial';
      ctx.fillStyle = '#6c757d';
      ctx.fillText(`Please wait${dots}`, centerX, centerY + radius + 20);
    }
    
    angle += 0.1;
    return requestAnimationFrame(drawSpinner);
  };
  
  return drawSpinner();
};

/**
 * Clears the loading animation
 */
export const clearLoadingIndicator = (animationId: number): void => {
  cancelAnimationFrame(animationId);
};
