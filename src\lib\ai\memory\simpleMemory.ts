export interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
}

export class SimpleMemory {
  private userId: string;
  private character: string;

  constructor(userId: string, character: string) {
    this.userId = userId;
    this.character = character;
  }

  private getStorageKey(): string {
    return `chat_history_${this.userId}_${this.character}`;
  }

  async addMessage(content: string, role: 'user' | 'assistant'): Promise<void> {
    try {
      const message: ChatMessage = {
        role,
        content,
        timestamp: new Date().toISOString()
      };

      const history = await this.getHistory();
      history.push(message);

      // Keep only last 20 messages for performance
      const trimmedHistory = history.slice(-20);

      if (typeof window !== 'undefined') {
        localStorage.setItem(this.getStorageKey(), JSON.stringify(trimmedHistory));
      }
    } catch (error) {
      console.error('Error adding message to memory:', error);
    }
  }

  async getHistory(): Promise<ChatMessage[]> {
    try {
      if (typeof window === 'undefined') {
        return [];
      }

      const stored = localStorage.getItem(this.getStorageKey());
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Error getting history from memory:', error);
      return [];
    }
  }

  async getRecentHistory(count: number = 6): Promise<ChatMessage[]> {
    const history = await this.getHistory();
    return history.slice(-count);
  }

  async getConversationSummary(): Promise<string> {
    const history = await this.getRecentHistory(6);
    
    if (history.length === 0) {
      return '';
    }

    const conversations = history
      .map(msg => `${msg.role === 'user' ? 'Child' : 'Assistant'}: ${msg.content}`)
      .join('\n');

    return `Recent conversation:\n${conversations}`;
  }

  async clearHistory(): Promise<void> {
    try {
      if (typeof window !== 'undefined') {
        localStorage.removeItem(this.getStorageKey());
      }
    } catch (error) {
      console.error('Error clearing history:', error);
    }
  }
} 