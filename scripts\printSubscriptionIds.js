#!/usr/bin/env node
const { prisma } = require('../src/lib/prisma');

async function main() {
  const profiles = await prisma.profile.findMany({
    select: { id: true, subscription_id: true }
  });
  profiles.forEach(({ id, subscription_id }) => {
    console.log(`User ID: ${id} — Subscription ID: ${subscription_id || 'None'}`);
  });
}

main()
  .then(() => process.exit(0))
  .catch((err) => {
    console.error(err);
    process.exit(1);
  }); 