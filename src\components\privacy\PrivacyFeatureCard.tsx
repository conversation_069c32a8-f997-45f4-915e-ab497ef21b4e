import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { fredoka } from "@/lib/fonts";

interface PrivacyFeatureCardProps {
    icon: React.ReactNode;
    title: string;
    description: string;
}

const PrivacyFeatureCard = ({
    icon,
    title,
    description,
}: PrivacyFeatureCardProps) => {
    return (
        <Card className="h-full">
            <CardHeader>
                <CardTitle className="flex items-center gap-3">
                    {icon}
                    <span className={`max-md:text-lg text-xl font-[800] ${fredoka.className}`}>{title}</span>
                </CardTitle>
            </CardHeader>
            <CardContent>
                <p className="text-gray-700 text-sm leading-relaxed">
                    {description}
                </p>
            </CardContent>
        </Card>
    );
};

export default PrivacyFeatureCard;
