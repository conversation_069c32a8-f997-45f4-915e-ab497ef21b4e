"use client";
import React from "react";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { useStory } from "./StoryContext";

const ReaderAgeSelector = () => {
    const { readerAge, setReaderAge } = useStory();

    // Map age numbers to full descriptions with proper typing
    const ageMap: Record<string, string> = {
        "6": "6 years old (1st Grade) - Simple stories with basic words",
        "8": "8 years old (3rd Grade) - Moderate stories with some challenge",
        "10": "10 years old (5th Grade) - More complex stories and themes",
        "12": "12 years old (7th Grade) - Advanced stories with deeper themes"
    };

    // Create options array for the select component
    const ageOptions = Object.entries(ageMap).map(([value, label]) => ({
        value,
        label
    }));

    // Get the current age description
    const getCurrentAgeDescription = (): string => {
        return ageMap[readerAge.toString()] || ageMap["8"];
    };

    return (
        <div className="mb-6 relative">
            <div className="flex items-center justify-center gap-2 mb-6">
                <label className="text-lg font-medium">Reader Age</label>
                <span className="text-sm text-gray-500">
                    (Controls reading level and content filtering)
                </span>
            </div>
            <Select
                value={getCurrentAgeDescription()}
                onValueChange={(value) => {
                    // Find the age value by matching the full description
                    const ageEntry = Object.entries(ageMap).find(([desc]) => desc === value);
                    if (ageEntry) {
                        setReaderAge(parseInt(ageEntry[0]));
                    }
                }}
            >
                <SelectTrigger className="w-full max-w-xl mx-auto py-2">
                    <SelectValue />
                </SelectTrigger>
                <SelectContent className="w-full max-w-xl left-1/2 -translate-x-1/2">
                    {ageOptions.map((option) => (
                        <SelectItem
                            key={option.value}
                            value={option.label}
                        >
                            {option.label}
                        </SelectItem>
                    ))}
                </SelectContent>
            </Select>
        </div>
    );
};

export default ReaderAgeSelector;
