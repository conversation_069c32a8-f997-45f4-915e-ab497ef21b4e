// Utility to reset content safety blocks in development mode

import { resetStrikes } from './strikeTracker';

/**
 * Reset all content safety blocks and strikes for development
 * This should only be used in development environment
 */
export const resetAllContentSafetyBlocks = () => {
  if (process.env.NODE_ENV === 'development') {
    console.log('[DEV] Resetting all content safety blocks and strikes');
    
    // Reset client-side strikes
    resetStrikes();
    
    // Clear any localStorage entries that might be causing issues
    if (typeof window !== 'undefined') {
      try {
        // Remove the strikes storage key
        localStorage.removeItem('ls_strikes');
        console.log('[DEV] Cleared localStorage content safety data');
      } catch (error) {
        console.warn('[DEV] Could not clear localStorage:', error);
      }
    }
    
    console.log('[DEV] Content safety reset complete');
  } else {
    console.warn('Content safety reset is only available in development mode');
  }
};

/**
 * Check if user is currently blocked and provide debug info
 */
export const debugContentSafetyStatus = () => {
  if (process.env.NODE_ENV === 'development') {
    console.log('[DEV] Content Safety Debug Status:');
    
    if (typeof window !== 'undefined') {
      try {
        const strikeData = localStorage.getItem('ls_strikes');
        console.log('[DEV] Strike data:', strikeData ? JSON.parse(strikeData) : 'No data');
        
        const now = Date.now();
        if (strikeData) {
          const parsed = JSON.parse(strikeData);
          if (parsed.blockedUntil && parsed.blockedUntil > now) {
            const remainingMs = parsed.blockedUntil - now;
            const remainingMin = Math.ceil(remainingMs / 60000);
            console.log(`[DEV] User is blocked for ${remainingMin} more minutes`);
          } else {
            console.log('[DEV] User is not blocked');
          }
        }
      } catch (error) {
        console.warn('[DEV] Could not read localStorage:', error);
      }
    }
  }
};

// Auto-reset on import in development
if (process.env.NODE_ENV === 'development') {
  // Automatically reset blocks when this module is imported in development
  setTimeout(() => {
    resetAllContentSafetyBlocks();
  }, 100);
}
