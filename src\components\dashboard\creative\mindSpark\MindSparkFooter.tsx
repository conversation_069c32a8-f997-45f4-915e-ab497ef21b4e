
import React from 'react';

const MindSparkFooter: React.FC = () => {
  return (
    <div className="mt-6 p-3 bg-spark-purple/10 rounded-lg flex items-center gap-2">
      <div className="h-5 w-5 text-spark-purple">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <path d="M12 2a10 10 0 1 0 10 10H12V2z" />
          <path d="M12 2a10 10 0 0 1 10 10" />
          <path d="M12 12 7 7" />
          <path d="M21 3h-6M15 9h6" />
        </svg>
      </div>
      <span className="text-sm text-spark-purple font-medium">AI-powered questions are unique and tailored to the selected age group</span>
    </div>
  );
};

export default MindSparkFooter;
