
import React from 'react';
import { Button } from '@/components/ui/button';
import { HelpCircle } from 'lucide-react';
import CreativeToolHelpDialog from '@/components/shared/CreativeToolHelpDialog';

interface MusicHelpDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}

export const musicHelpSteps = [
  {
    title: "1. Choose a Music Style",
    content: "Select from styles like orchestral, electronic, rock, jazz, or pop to define your musical landscape."
  },
  {
    title: "2. Describe Your Music",
    content: "Write a detailed description of the mood, instruments, and feeling you want to create."
  },
  {
    title: "3. Select Music Mood",
    content: "Pick a mood like Happy, Sad, Epic, or Mysterious to guide the emotional tone of your composition."
  },
  {
    title: "4. Generate Your Music",
    content: "Click 'Generate Music' and watch as our AI transforms your description into a unique musical piece."
  },
  {
    title: "5. Refine & Save",
    content: "Listen to your generated music, make adjustments, and save your musical creation to your portfolio."
  }
];

const MusicHelpDialog: React.FC<MusicHelpDialogProps> = ({ isOpen, onOpenChange }) => {
  return (
    <CreativeToolHelpDialog 
      open={isOpen}
      onOpenChange={onOpenChange}
      title="Music Composer Studio"
      description="Create amazing music with our interactive composition tool"
      steps={musicHelpSteps}
      aiMentorMessage="Ready to compose? I'll guide you through creating your own music masterpiece!"
    />
  );
};

export const MusicHelpButton: React.FC<{ onClick: () => void }> = ({ onClick }) => {
  return (
    <Button 
      variant="outline" 
      onClick={onClick}
      className="gap-2"
    >
      <HelpCircle className="h-4 w-4" /> How to Use
    </Button>
  );
};

export default MusicHelpDialog;
