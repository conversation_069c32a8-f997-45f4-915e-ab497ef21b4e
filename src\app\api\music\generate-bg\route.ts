/* eslint-disable @typescript-eslint/no-explicit-any */
import { NextResponse } from 'next/server';
import Replicate from 'replicate';
import { moderateContent } from '@/utils/ai/contentModeration';

// Initialize Replicate only if token is available
let replicate: Replicate | null = null;
try {
  if (process.env.REPLICATE_API_TOKEN) {
    replicate = new Replicate({
      auth: process.env.REPLICATE_API_TOKEN,
    });
  }
} catch (error) {
  console.warn('Failed to initialize Replicate:', error);
}

export async function POST(request: Request) {
  try {
    const { title, description, mood, style, duration = 10 } = await request.json();

    // Validate required fields
    if (!description) {
      return NextResponse.json(
        { error: 'Music description is required' },
        { status: 400 }
      );
    }

    // Content moderation
    const userKey = request.headers.get('x-forwarded-for') ?? 'anonymous';
    const inCheck = await moderateContent(description, 'prompt', userKey);
    if (!inCheck.isAppropriate) {
      return NextResponse.json({ error: inCheck.reason, blocked: inCheck.blocked ?? false }, { status: 400 });
    }

    // Check if Replicate is available
    if (!replicate || !process.env.REPLICATE_API_TOKEN) {
      console.warn('Replicate API not configured, returning mock response');
      
      // Return a mock response for development/demo purposes
      return NextResponse.json({
        success: true,
        audioUrl: '/generated-music/sample-background-music.mp3',
        generationId: `demo_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
        metadata: {
          type: 'background',
          title: title || 'Generated Music',
          description,
          mood: mood || 'relaxing',
          style: style || 'orchestral',
          duration,
          prompt: `Demo music for: ${description}`,
          note: 'This is a demo response. Configure REPLICATE_API_TOKEN for actual music generation.'
        }
      });
    }

    // Build enhanced prompt for MusicGen
    let enhancedPrompt = "";
    
    // Add tempo and energy keywords based on mood
    const tempoKeywords: Record<string, string> = {
      'happy': 'upbeat, energetic, lively',
      'sad': 'slow, gentle, soft',
      'energetic': 'fast, driving, powerful',
      'relaxing': 'calm, peaceful, ambient',
      'romantic': 'tender, warm, intimate',
      'epic': 'dramatic, cinematic, powerful'
    };
     
    // Add style-specific production keywords
    const styleKeywords: Record<string, string> = {
      'orchestral': 'symphonic, rich harmonies, full orchestra, strings, brass, woodwinds',
      'piano': 'solo piano, expressive, melodic, classical piano',
      'electronic': 'synthesizer, electronic beats, ambient pads, digital',
      'acoustic': 'acoustic guitar, fingerpicking, natural reverb, organic',
      'jazz': 'jazz ensemble, saxophone, piano, swing rhythm',
      'rock': 'electric guitar, drums, bass, rock band',
      'ambient': 'atmospheric, ethereal, spacious, reverb',
      'classical': 'chamber music, baroque, elegant, refined',
      'folk': 'traditional instruments, acoustic, storytelling',
      'pop': 'catchy melody, modern production, polished'
    };
    
    // Start with style-specific keywords
    const styleKey = style?.toLowerCase() || 'orchestral';
    if (styleKeywords[styleKey]) {
      enhancedPrompt += styleKeywords[styleKey];
    } else {
      enhancedPrompt += style || 'instrumental';
    }
    
    // Add mood-specific tempo and energy
    const moodKey = mood?.toLowerCase() || 'relaxing';
    if (tempoKeywords[moodKey]) {
      enhancedPrompt += `, ${tempoKeywords[moodKey]}`;
    }
    
    // Add core musical description (cleaned up)
    if (description) {
      const musicDesc = description
        .replace(/Create|Generate|Make|Compose|instrumental|background|music/gi, '')
        .replace(/\s+/g, ' ')
        .trim();
      
      // Extract only musical terms
      const musicalTerms = musicDesc.match(/\b(melody|harmony|rhythm|tempo|chord|scale|dynamics|texture|arrangement|improvisation|progression|cadence|crescendo|diminuendo|staccato|legato|forte|piano|andante|allegro|adagio)\b/gi);
      
      if (musicalTerms && musicalTerms.length > 0) {
        enhancedPrompt += `, ${musicalTerms.join(', ')}`;
      } else if (musicDesc && musicDesc.length > 10) {
        // Use first 50 characters of cleaned description
        enhancedPrompt += `, ${musicDesc.substring(0, 50)}`;
      }
    }
    
    // Add high-quality production keywords
    enhancedPrompt += ', studio quality, professional recording, mastered';

    // Add duration-specific prompt guidance
    let finalPrompt = enhancedPrompt;
    if (duration >= 20) {
      finalPrompt += ', extended arrangement, multiple sections';
    } else if (duration >= 15) {
      finalPrompt += ', complete musical phrase';
    } else {
      finalPrompt += ', concise musical idea';
    }

    console.log('Generating background music with enhanced prompt:', finalPrompt);

    // Configure input parameters
    const input: any = {
      prompt: finalPrompt,
      duration: Math.min(duration, 30), // Keep it reasonable for MusicGen
          model_version: "large",
          normalization_strategy: "peak",
          top_k: 250,
          top_p: 0.0,
          temperature: 1.0,
      continuation: false
    };

    // Try music generation with MusicGen
    const modelVersions: string[] = [
      "meta/musicgen:b05b1dff1d8c6dc63d14b0cdb42135378dcb87f6373b0d3d341ede46e59e2dbe", // Large MusicGen - most reliable
      "meta/musicgen:7a76a8258b23fae65c5a22debb8841d1d7e816b75c2f24218cd2bd8573787906",
      "meta/musicgen:671ac645ce5e552cc63a54a2bbff63fcf798043055d2dac5fc9e36a837eedcfb"
    ];
    
    let output;
    let lastError;
    
    for (const modelVersion of modelVersions) {
      try {
        console.log(`Trying model version: ${modelVersion}`);
        output = await replicate.run(modelVersion as any, { input });
        console.log(`Successfully generated with model: ${modelVersion}`);
        break;
      } catch (error) {
        console.log(`Model ${modelVersion} failed:`, error);
        lastError = error;
        continue;
      }
    }
    
    if (!output) {
      console.error('All models failed, last error:', lastError);
      // Return a fallback response
      return NextResponse.json({
        success: false,
        error: 'Music generation service temporarily unavailable. Please try again later.',
        audioUrl: '/generated-music/sample-background-music.mp3',
        generationId: `fallback_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
        metadata: {
          type: 'background',
          title: title || 'Sample Music',
          description,
          mood: mood || 'relaxing',
          style: style || 'orchestral',
          duration,
          prompt: finalPrompt,
          note: 'Fallback sample due to service unavailability'
        }
      });
    }

    console.log('Replicate output type:', typeof output);
    console.log('Replicate output:', output);

    // Generate unique ID for this generation
    const generationId = `bg_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

    // Handle different output formats from Replicate - simplified
    let audioUrl: string = '';
    
    if (typeof output === 'string') {
      // Direct URL from Replicate
      audioUrl = output;
    } else if (Array.isArray(output) && output.length > 0) {
      // Array of URLs - use the first one
      audioUrl = output[0];
    } else if (output && typeof output === 'object' && 'audio' in output) {
      // Object with audio property
      audioUrl = (output as any).audio;
    } else if (output instanceof ReadableStream || (output && typeof output === 'object' && 'getReader' in output)) {
      // Handle ReadableStream - convert to base64 data URL
      try {
        console.log('Converting ReadableStream to audio data...');
        const reader = (output as ReadableStream).getReader();
        const chunks: Uint8Array[] = [];
        
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;
          chunks.push(value);
        }
        
        // Combine all chunks into a single Uint8Array
        const totalLength = chunks.reduce((acc, chunk) => acc + chunk.length, 0);
        const combined = new Uint8Array(totalLength);
        let offset = 0;
        
        for (const chunk of chunks) {
          combined.set(chunk, offset);
          offset += chunk.length;
        }
        
        // Convert to base64
        const base64 = Buffer.from(combined).toString('base64');
        audioUrl = `data:audio/mpeg;base64,${base64}`;
        
        console.log('Successfully converted ReadableStream to base64 data URL');
      } catch (streamError) {
        console.error('Error processing ReadableStream:', streamError);
        // Return fallback instead of throwing
      return NextResponse.json({
        success: false,
          error: 'Error processing audio stream. Please try again.',
        audioUrl: '/generated-music/sample-background-music.mp3',
        generationId: `stream_error_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
        metadata: {
          type: 'background',
          title: title || 'Sample Music',
          description,
          mood: mood || 'relaxing',
          style: style || 'orchestral',
          duration,
          prompt: finalPrompt,
            note: 'Stream processing error - using fallback'
        }
      });
      }
    } else {
      console.error('Unhandled output format:', output);
      // Return fallback instead of throwing
      return NextResponse.json({
        success: false,
        error: 'Unexpected response format from music generation service.',
        audioUrl: '/generated-music/sample-background-music.mp3',
        generationId: `format_error_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
        metadata: {
          type: 'background',
          title: title || 'Sample Music',
          description,
          mood: mood || 'relaxing',
          style: style || 'orchestral',
          duration,
          prompt: finalPrompt
        }
      });
    }

    // Validate that we have a valid URL
    if (!audioUrl || typeof audioUrl !== 'string') {
      return NextResponse.json({
        success: false,
        error: 'No valid audio URL received from music generation service',
        audioUrl: '/generated-music/sample-background-music.mp3',
        generationId: `invalid_url_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
        metadata: {
          type: 'background',
          title: title || 'Sample Music',
          description,
          mood: mood || 'relaxing',
          style: style || 'orchestral',
          duration,
          prompt: finalPrompt
        }
      });
    }

    // Return the audio URL
    return NextResponse.json({
      success: true,
      audioUrl,
      generationId,
      metadata: {
        type: 'background',
        title,
        description,
        mood,
        style,
        duration,
        prompt: finalPrompt
      }
    });

  } catch (error) {
    console.error('Background music generation error:', error);
    
    // Always return a response, never throw
    return NextResponse.json(
      { 
        success: false,
        error: 'Music generation service is currently unavailable. Please try again later.',
        audioUrl: '/generated-music/sample-background-music.mp3',
        generationId: `error_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
        details: process.env.NODE_ENV === 'development' ? (error instanceof Error ? error.message : 'Unknown error') : 'Service temporarily unavailable'
      },
      { status: 200 } // Return 200 instead of 500 to prevent frontend errors
    );
  }
} 