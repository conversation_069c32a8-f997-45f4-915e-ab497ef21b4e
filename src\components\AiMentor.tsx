"use client";
import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, MessageCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import Image from "next/image";

type MentorCharacter = "robot" | "owl" | "explorer";

interface AiMentorProps {
    character?: MentorCharacter;
    message?: string;
    onMessageEnd?: () => void;
    showControls?: boolean;
    name?: string;
}

const AiMentor = ({
    character = "robot",
    message = "Hi! I'm your creative buddy. What would you like to make today?",
    onMessageEnd,
    showControls = true,
    name,
}: AiMentorProps) => {
    const [isAnimating, setIsAnimating] = useState(false);
    const [displayedMessage, setDisplayedMessage] = useState("");
    const [messageIndex, setMessageIndex] = useState(0);

    // Character assets mapping - updated with new branded character images
    const characterAssets = {
        robot: {
            image: "/lovable-uploads/bde9444a-4cdd-4a25-a69c-caa49bc65986.png",
            alt: "Friendly robot mentor",
            color: "bg-spark-blue",
            hoverColor: "hover:bg-spark-blue/90",
            name: name || "Sparky",
        },
        owl: {
            image: "/lovable-uploads/a13f4588-0676-4318-bb7e-a624030d14eb.png",
            alt: "Wise owl mentor",
            color: "bg-spark-lavender",
            hoverColor: "hover:bg-spark-lavender/90",
            name: name || "Professor Hootie",
        },
        explorer: {
            image: "/lovable-uploads/bd7e2dce-201d-450b-81e5-cc70e0856d59.png",
            alt: "Female explorer mentor",
            color: "bg-spark-orange",
            hoverColor: "hover:bg-spark-orange/90",
            name: name || "Captain Nova",
        },
    };

    // Text animation effect
    useEffect(() => {
        if (messageIndex < message.length) {
            setIsAnimating(true);
            const timer = setTimeout(() => {
                setDisplayedMessage(message.substring(0, messageIndex + 1));
                setMessageIndex(messageIndex + 1);
            }, 30); // Speed of text appearance

            return () => clearTimeout(timer);
        } else {
            setIsAnimating(false);
            if (onMessageEnd) onMessageEnd();
        }
    }, [message, messageIndex, onMessageEnd]);

    const resetAnimation = () => {
        setDisplayedMessage("");
        setMessageIndex(0);
    };

    // When message prop changes, reset animation
    useEffect(() => {
        resetAnimation();
    }, [message]);

    const {
        image,
        alt,
        color,
        name: characterName,
    } = characterAssets[character];

    return (
        <div className="flex flex-col items-center">
            <div className="relative mb-4">
                {/* Character image with animated bounce effect */}
                <div
                    className={`w-24 h-24 md:w-32 md:h-32 rounded-full overflow-hidden border-4 ${color} shadow-lg animate-float`}
                >
                    <Image
                        src={image}
                        alt={alt}
                        width={128}
                        height={128}
                        className="w-full h-full object-cover"
                    />
                </div>

                {/* Character name - now centered */}
                <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 bg-white px-3 py-1 rounded-full shadow-md text-sm font-bold text-center w-auto">
                    {characterName}
                </div>

                {/* Decorative sparkle effect */}
                <div className="absolute -top-2 -right-2">
                    <Sparkles className="w-6 h-6 text-spark-yellow" />
                </div>

                {/* Speaking/animation indicator */}
                {isAnimating && (
                    <div className="absolute bottom-0 right-0 w-5 h-5 bg-green-400 rounded-full border-2 border-white pulse"></div>
                )}
            </div>

            {/* Speech bubble */}
            <div className="relative bg-white rounded-2xl p-4 shadow-md max-w-xs md:max-w-sm mb-4">
                <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 w-4 h-4 bg-white rotate-45"></div>
                <p className="text-center min-h-[3rem]">
                    {displayedMessage}
                    {isAnimating && <span className="animate-pulse">|</span>}
                </p>
            </div>

            {/* Controls - Simplified to only show needed buttons */}
            {showControls && (
                <div className="flex gap-2">
                    <Button size="sm" variant="outline" className="gap-1">
                        <MessageCircle className="h-4 w-4" />
                        Text
                    </Button>
                </div>
            )}
        </div>
    );
};

export default AiMentor;
