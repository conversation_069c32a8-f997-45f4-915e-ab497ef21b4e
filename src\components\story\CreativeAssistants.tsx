"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import { User2, MapPin } from "lucide-react";
import { useStory } from "./StoryContext";
import {
    generateCharacter,
    generateStoryOption,
} from "@/utils/ai/storyService";
import { toast } from "sonner";

const CreativeAssistants = () => {
    const {
        content,
        setContent,
        readerAge,
        lastFormat,
        isGenerating,
        setIsGenerating,
        title,
        storyTheme,
        storyGenre,
    } = useStory();

    const handleGenerateCharacter = async () => {
        setIsGenerating(true);
        try {
            const character = await generateCharacter(
                readerAge,
                lastFormat,
                title,
                storyTheme,
                storyGenre
            );
            setContent(content + "\n\n" + character);
            toast.success("New character generated!");
        } catch (error) {
            console.error("Error generating character:", error);
            toast.error("Failed to generate character. Please try again.");
        } finally {
            setIsGenerating(false);
        }
    };

    const handleGenerateSetting = async () => {
        setIsGenerating(true);
        try {
            const setting = await generateStoryOption(
                content,
                "setting",
                readerAge,
                lastFormat,
                title,
                storyTheme,
                storyGenre
            );
            setContent(content + "\n\n" + setting);
            toast.success("New setting generated!");
        } catch (error) {
            console.error("Error generating setting:", error);
            toast.error("Failed to generate setting. Please try again.");
        } finally {
            setIsGenerating(false);
        }
    };

    return (
        <div className="mb-6">
            <h2 className="flex items-center gap-2 text-xl font-bold mb-4">
                <span className="text-blue-400 text-2xl">⭐</span>
                Creative Assistants
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Button
                    onClick={handleGenerateCharacter}
                    disabled={isGenerating}
                    className="!bg-[#00C2FF] hover:!bg-[#00C2FF]/90 rounded-full h-12"
                >
                    <User2 className="h-5 w-5 mr-2" />
                    Generate Character
                </Button>
                <Button
                    onClick={handleGenerateSetting}
                    disabled={isGenerating}
                    className="!bg-[#FF7A50] hover:!bg-[#FF7A50]/90 rounded-full h-12"
                >
                    <MapPin className="h-5 w-5 mr-2" />
                    Setting Idea
                </Button>
            </div>
        </div>
    );
};

export default CreativeAssistants;
