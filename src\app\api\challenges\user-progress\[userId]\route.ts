import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// GET /api/challenges/user-progress/[userId] - Get user's challenge completion status
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    const { userId } = await params;

    if (!userId) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'User ID is required' 
        },
        { status: 400 }
      );
    }

    // Get all challenge completions for the user
    const completions = await prisma.challengeCompletion.findMany({
      where: {
        user_id: userId
      },
      include: {
        challenge: {
          select: {
            id: true,
            title: true,
            type: true,
            difficulty: true
          }
        },
        content: {
          select: {
            id: true,
            title: true,
            type: true,
            preview_url: true,
            created_at: true
          }
        }
      },
      orderBy: {
        completed_at: 'desc'
      }
    });

    // Transform to a simple object for frontend consumption
    const userChallenges: Record<string, boolean> = {};
    const challengeDetails: Array<{
      challengeId: string;
      challengeTitle: string;
      challengeType: string;
      difficulty: string;
      completedAt: string;
      contentId: string;
      contentTitle: string;
      contentPreviewUrl: string | null;
    }> = [];

    completions.forEach(completion => {
      userChallenges[completion.challenge_id] = true;
      challengeDetails.push({
        challengeId: completion.challenge_id,
        challengeTitle: completion.challenge.title,
        challengeType: completion.challenge.type,
        difficulty: completion.challenge.difficulty,
        completedAt: completion.completed_at.toISOString(),
        contentId: completion.content.id,
        contentTitle: completion.content.title || 'Untitled',
        contentPreviewUrl: completion.content.preview_url
      });
    });

    // Get user statistics
    const totalCompletions = completions.length;
    const completionsByType = completions.reduce((acc, completion) => {
      const type = completion.challenge.type;
      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return NextResponse.json({
      success: true,
      userChallenges,
      challengeDetails,
      statistics: {
        totalCompletions,
        completionsByType
      }
    });
  } catch (error) {
    console.error('Error fetching user challenge progress:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch user progress' 
      },
      { status: 500 }
    );
  }
}
