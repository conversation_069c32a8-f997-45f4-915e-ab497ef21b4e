import { useState } from "react";
import { use<PERSON>entorCharacter } from "./useMentorCharacter";
import { useMentorChat } from "./useMentorChat";
import { useMentorNameEditor } from "./useMentorNameEditor";
import { ChatMessage } from "../types";

export const useAiMentor = () => {
    const {
        mentor<PERSON>haracter,
        mentorN<PERSON>s,
        mentorMessage,
        changeMentorCharacter,
        updateMentorName,
    } = useMentorCharacter();

    const { messages, isLoading, sendMessage, clearChat } = useMentorChat({
        character: mentor<PERSON><PERSON><PERSON>,
        characterName: mentorNames[mentor<PERSON><PERSON><PERSON>]
    });

    const {
        isEditingName,
        mentorName,
        setMentorName,
        setIsEditingName,
        handleSaveName,
    } = useMentorNameEditor(mentorCharacter, updateMentorName);

    // State for managing user input
    const [userMessage, setUserMessage] = useState("");

    // Format messages for the chat UI
    const chatHistory: ChatMessage[] = messages.map((msg) => ({
        id: msg.id,
        role: msg.role === "assistant" ? "ai" : "user",
        content: msg.content,
        timestamp: msg.timestamp,
    }));

    // Handle sending messages
    const handleSendMessage = async () => {
        if (!userMessage.trim()) return;

        await sendMessage(userMessage);
        // Input clearing is now handled in UI components
    };

    // Combine the change mentor character functionality with chat history reset
    const handleChangeMentorCharacter = (character: typeof mentorCharacter) => {
        changeMentorCharacter(character);
        clearChat();
    };

    return {
        // Character state
        mentorCharacter,
        mentorMessage,
        mentorNames,
        changeMentorCharacter: handleChangeMentorCharacter,

        // Name editor state
        isEditingName,
        mentorName,
        setMentorName,
        setIsEditingName,
        handleSaveName,

        // Chat state
        userMessage,
        chatHistory,
        isProcessing: isLoading,
        setUserMessage,
        handleSendMessage,
        clearChat,
    };
};
