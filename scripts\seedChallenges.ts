import { PrismaClient } from '../src/generated/prisma';

const prisma = new PrismaClient();

const challenges = [
  {
    id: "1",
    title: "Magical Forest Story",
    description: "Write a short story about a child who discovers a magical forest.",
    difficulty: "easy",
    type: "story",
    prompt: "Once upon a time, in a forest unlike any other...",
  },
  {
    id: "2",
    title: "Space Explorer Portrait",
    description: "Draw your own space explorer character visiting a new planet.",
    difficulty: "medium",
    type: "art",
    prompt: "Imagine a brave explorer discovering a colorful new world...",
  },
  {
    id: "3",
    title: "Heroes Journey Music",
    description: "Compose a short melody that represents a hero setting off on an adventure.",
    difficulty: "medium",
    type: "music",
    prompt: "Think of the excitement and determination of beginning a quest...",
  },
  {
    id: "4",
    title: "Puzzle Game Design",
    description: "Design a simple puzzle game with at least three levels.",
    difficulty: "hard",
    type: "game",
    prompt: "Create puzzles that get progressively more challenging...",
  },
  {
    id: "weekly-robot",
    title: "Creative Challenge of the Week",
    description: "First create a story about what the robot does, then draw it in the Art Studio. What special powers does it have?",
    difficulty: "medium",
    type: "story",
    prompt: "Imagine a friendly robot with amazing abilities...",
  },
  {
    id: "ocean-adventure",
    title: "Ocean Adventure Story",
    description: "Write about a underwater adventure with sea creatures.",
    difficulty: "easy",
    type: "story",
    prompt: "Deep beneath the ocean waves, something magical awaits...",
  },
  {
    id: "dragon-friend",
    title: "Friendly Dragon Art",
    description: "Create artwork of a kind dragon who helps people.",
    difficulty: "easy",
    type: "art",
    prompt: "A gentle dragon with sparkling scales and a warm smile...",
  },
  {
    id: "celebration-song",
    title: "Celebration Music",
    description: "Compose a joyful song for a special celebration.",
    difficulty: "easy",
    type: "music",
    prompt: "Music that makes everyone want to dance and smile...",
  }
];

async function seedChallenges() {
  try {
    console.log('🌱 Seeding challenges...');
    
    // Clear existing challenges
    await prisma.challenge.deleteMany();
    console.log('🗑️ Cleared existing challenges');
    
    // Insert new challenges
    for (const challenge of challenges) {
      await prisma.challenge.create({
        data: challenge
      });
      console.log(`✅ Created challenge: ${challenge.title}`);
    }
    
    console.log('🎉 Successfully seeded challenges!');
  } catch (error) {
    console.error('❌ Error seeding challenges:', error);
  } finally {
    await prisma.$disconnect();
  }
}

seedChallenges();
