import { NextRequest, NextResponse } from 'next/server';
import Replicate from 'replicate';

export async function POST(req: NextRequest) {
  const { prompt } = await req.json();
  console.log('🔥 API: Received prompt:', prompt);
  
  if (!prompt) {
    return NextResponse.json({ error: 'Prompt is required' }, { status: 400 });
  }

  const replicate = new Replicate({ auth: process.env.REPLICATE_API_TOKEN });
  
  // Generate high-quality line art/coloring page outline
  const input = {
    prompt: `professional coloring book line art, bold black outlines of ${prompt}, thick consistent line weight 3-4px, completely enclosed shapes, clean vector style, minimal interior details, pure white background, high contrast black lines, simple geometric forms, child-friendly design, easy to color within lines, no gaps in outlines, smooth curves, perfect for coloring`,
    negative_prompt: "color, shading, gradient, fill, photorealism, blur, color artifacts, thin lines, broken lines, gaps, noise, messy lines, complex details, realistic textures, shadows, highlights, watermarks, text",
    width: 768,
    height: 768,
    num_outputs: 1,
    guidance_scale: 15,
    num_inference_steps: 40,
    scheduler: "DPMSolverMultistep"
  };

  console.log('🔥 API: Input to Replicate:', input);

  try {
    const output = await replicate.run(
      'stability-ai/stable-diffusion:ac732df83cea7fff18b8472768c88ad041fa750ff7682a21affe81863cbe77e4',
      { input }
    ) as string[];

    console.log('🔥 API: Replicate output:', output);

    if (output && Array.isArray(output) && output.length > 0) {
      const imageResult = output[0];
      console.log('🔥 API: Image result type:', typeof imageResult);

      // Check if it's a ReadableStream
      if (imageResult && typeof imageResult === 'object' && 'locked' in imageResult) {
        console.log('🔥 API: Converting ReadableStream to blob');
        const response = new Response(imageResult as ReadableStream);
        const blob = await response.blob();
        const buffer = await blob.arrayBuffer();
        const base64 = Buffer.from(buffer).toString('base64');
        const dataUrl = `data:image/png;base64,${base64}`;

        console.log('🔥 API: Success - returning data URL');
        return NextResponse.json({ image: dataUrl });
      } else {
        // If it's already a URL string
        console.log('🔥 API: Success - returning image URL:', imageResult);
        return NextResponse.json({ image: imageResult });
      }
    } else {
      console.log('🔥 API: Error - no image in output');
      return NextResponse.json({ error: 'No image generated' }, { status: 500 });
    }
  } catch (err) {
    console.error('🔥 API: Replicate API error:', err);

    // Provide fallback placeholder coloring page
    const fallbackMessage = `Sorry, we couldn't generate a coloring page for "${prompt}" right now. Please try again later or try a different prompt like "cute cat", "happy flower", or "friendly robot".`;

    return NextResponse.json({
      error: 'Failed to generate image',
      details: err instanceof Error ? err.message : 'Unknown error',
      fallbackMessage
    }, { status: 500 });
  }
} 