"use client";

import React from "react";
import { Info } from "lucide-react";
import { useStory } from "./StoryContext";
import VoiceEnabledInput from "@/components/shared/VoiceEnabledInput";

const StoryContent = () => {
    const { content, setContent, learningMode, challengeData } = useStory();

    // Get placeholder text based on challenge or default
    const getPlaceholder = () => {
        if (challengeData) {
            return challengeData.instructions?.split('\n')[0] || "Start your story here...";
        }
        return "Once upon a time...";
    };

    const handleVoiceInput = (text: string) => {
        // Add new line if there's existing content
        const newContent = content 
            ? content.trim() + "\n" + text.trim()
            : text.trim();
        setContent(newContent);
    };

    return (
        <div className="space-y-4">
            <VoiceEnabledInput
                value={content}
                onChange={(e) => setContent(e.target.value)}
                onTextAdded={handleVoiceInput}
                isTextArea={true}
                placeholder={getPlaceholder()}
                className="min-h-[200px] resize-none py-3 px-4 w-full pr-12 rounded-xl border-2 border-gray-200 focus:border-[#00B7FD] focus:ring-2 focus:ring-[#00B7FD]/20 outline-none transition-colors text-lg"
            />
            {learningMode && (
                <div className="flex items-center gap-2 bg-blue-50 p-4 rounded-lg">
                    <Info className="h-5 w-5 text-blue-500" />
                    <div className="text-blue-600">
                        <h4 className="font-semibold text-sm">Story Structure</h4>
                        <p className="text-sm">
                            Remember to include a beginning, middle, and end in your story. 
                            Use descriptive words to help your readers imagine what&apos;s happening!
                        </p>
                    </div>
                </div>
            )}
        </div>
    );
};

export default StoryContent;
