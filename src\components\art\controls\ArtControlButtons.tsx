
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Wand2 } from 'lucide-react';

interface ArtControlButtonsProps {
  handleGenerateArt: () => void;
  isGenerating: boolean;
  prompt: string;
}

const ArtControlButtons = ({ 
  handleGenerateArt, 
  isGenerating,
  prompt 
}: ArtControlButtonsProps) => {
  return (
    <div className="mt-4">
      <Button 
        onClick={handleGenerateArt} 
        disabled={isGenerating || !prompt || prompt.length < 3} 
        className="w-full gap-2"
        variant="default"
      >
        <Wand2 className="h-4 w-4" /> 
        {isGenerating ? "Creating Art..." : "Create My Art"}
      </Button>
    </div>
  );
};

export default ArtControlButtons;
