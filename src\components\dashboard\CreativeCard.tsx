import React from "react";
import Link from "next/link";
import { Badge } from "@/components/ui/badge";
import { Lock } from "lucide-react";

interface CreativeCardProps {
    icon: React.ReactNode;
    title: string;
    description: string;
    bgColor: string;
    available: boolean;
    linkTo?: string;
    badge?: string;
    onClick?: () => void;
    isLocked?: boolean;
}

const CreativeCard = ({
    icon,
    title,
    description,
    bgColor = "bg-gray-300", // Default value if undefined
    available,
    linkTo,
    badge,
    onClick,
    isLocked = false,
}: CreativeCardProps) => {
    // Safe bgColor - ensure it's never undefined
    const safeBgColor = bgColor || "bg-gray-300";

    const CardContent = () => (
        <div
            className={`
      h-full rounded-xl overflow-hidden shadow-md transition-all duration-200
      ${!available && "opacity-60"}
      ${
          available && !isLocked &&
          "hover:shadow-lg hover:translate-y-[-4px] hover:scale-[1.02]"
      }
      ${isLocked && "cursor-not-allowed"}
      relative
    `}
        >
            {isLocked && (
                <div className="absolute top-3 sm:top-4 right-3 sm:right-4 z-20 bg-black/60 p-1.5 sm:p-2 rounded-full">
                    <Lock className="h-4 w-4 sm:h-5 sm:w-5 text-white" />
                </div>
            )}
            <div className={`${safeBgColor} p-4 sm:p-6 relative overflow-hidden h-40 sm:h-44 lg:h-48 ${isLocked ? 'opacity-50' : ''}`}>
                {/* Decorative elements for visual interest */}
                <div className="absolute -right-6 sm:-right-8 -top-6 sm:-top-8 w-16 sm:w-20 lg:w-24 h-16 sm:h-20 lg:h-24 rounded-full bg-white/10 blur-xl"></div>
                <div className="absolute -left-1 sm:-left-2 bottom-0 w-8 sm:w-10 lg:w-12 h-8 sm:h-10 lg:h-12 rounded-full bg-white/5"></div>

                <div className="bg-white/20 p-2 sm:p-3 rounded-full w-fit mb-3 sm:mb-4 z-10 relative">
                    <div className="h-5 w-5 sm:h-6 sm:w-6 text-black">{icon}</div>
                </div>
                <h3 className="text-lg sm:text-xl lg:text-2xl font-bold text-black mb-1 font-fredoka relative z-10">
                    {title}
                </h3>
                <p className="text-sm sm:text-base text-black font-nunito relative z-10 leading-tight">
                    {description}
                </p>
            </div>
            <div className="bg-white p-3 sm:p-4 flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2 sm:gap-0">
                <Badge
                    variant={available ? "default" : "secondary"}
                    className={`font-semibold text-xs sm:text-sm ${
                        isLocked ? "bg-red-100 text-red-800" :
                        available
                            ? "bg-yellow-400 text-yellow-900"
                            : "bg-blue-100 text-blue-800"
                    }`}
                >
                    {isLocked ? (
                        <span className="sm:hidden">Subscription Req.</span>
                    ) : null}
                    {isLocked ? (
                        <span className="hidden sm:inline">Subscription Required</span>
                    ) : available ? "Available now" : badge || "Coming soon"}
                </Badge>

                {available && !isLocked && (
                    <span className="text-xs sm:text-sm font-medium text-gray-600">
                        <span className="sm:hidden">Tap to open →</span>
                        <span className="hidden sm:inline">Click to open →</span>
                    </span>
                )}
                {isLocked && (
                    <span className="text-xs sm:text-sm font-medium text-red-600">
                        <span className="sm:hidden">Unlock →</span>
                        <span className="hidden sm:inline">Unlock with subscription →</span>
                    </span>
                )}
            </div>
        </div>
    );

    if (onClick) {
        return (
            <div className={`h-full ${isLocked ? 'cursor-pointer' : ''}`} onClick={onClick}>
                <CardContent />
            </div>
        );
    }

    if (linkTo && available && !isLocked) {
        return (
            <Link href={linkTo} className="block h-full">
                <CardContent />
            </Link>
        );
    }

    return <CardContent />;
};

export default CreativeCard;
