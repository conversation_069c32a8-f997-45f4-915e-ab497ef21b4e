import { NextRequest, NextResponse } from 'next/server';

const CMS_BASE_URL = process.env.CMS_BASE_URL || 'http://localhost:3001';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    const cmsParams = new URLSearchParams();
    cmsParams.append('where[status][equals]', 'published');
    
    // Forward query parameters
    searchParams.forEach((value, key) => {
      if (key !== 'status') {
        if (key === 'genre' || key === 'ageGroup') {
          cmsParams.append(`where[${key}][contains]`, value);
        } else {
          cmsParams.append(`where[${key}][equals]`, value);
        }
      }
    });
    
    const cmsUrl = `${CMS_BASE_URL}/api/story-templates?${cmsParams}`;
    console.log('Fetching story templates from CMS:', cmsUrl);
    
    const response = await fetch(cmsUrl, {
      headers: {
        'Content-Type': 'application/json',
      },
      signal: AbortSignal.timeout(10000),
    });
    
    if (!response.ok) {
      throw new Error(`CMS API error: ${response.status}`);
    }
    
    const data = await response.json();
    
    const transformedTemplates = data.docs?.map((template: any) => ({
      id: template.id,
      title: template.title,
      slug: template.slug,
      description: template.description,
      genre: template.genre,
      ageGroup: template.ageGroup,
      storyPrompt: template.storyPrompt,
      characterOptions: template.characterOptions?.map((char: any) => ({
        ...char,
        image: char.image ? {
          ...char.image,
          url: char.image.url.startsWith('http') ? char.image.url : `${CMS_BASE_URL}${char.image.url}`
        } : null
      })),
      settingOptions: template.settingOptions?.map((setting: any) => ({
        ...setting,
        image: setting.image ? {
          ...setting.image,
          url: setting.image.url.startsWith('http') ? setting.image.url : `${CMS_BASE_URL}${setting.image.url}`
        } : null
      })),
      plotPoints: template.plotPoints,
      writingPrompts: template.writingPrompts,
      estimatedLength: template.estimatedLength,
      learningObjectives: template.learningObjectives,
      subscriptionTier: template.subscriptionTier,
      featured: template.featured,
      seasonal: template.seasonal,
      publishedAt: template.publishedAt,
    })) || [];
    
    return NextResponse.json({
      success: true,
      storyTemplates: transformedTemplates,
      total: data.totalDocs || 0,
      page: data.page || 1,
      totalPages: data.totalPages || 1,
    });
    
  } catch (error) {
    console.error('Error fetching story templates from CMS:', error);
    
    // Fallback story templates
    const fallbackTemplates = [
      {
        id: 'fallback-story-1',
        title: 'Space Adventure',
        slug: 'space-adventure',
        description: 'Create an exciting story about space exploration',
        genre: ['adventure', 'sci-fi'],
        ageGroup: ['9-11'],
        storyPrompt: 'You are an astronaut who discovers a mysterious planet...',
        characterOptions: [
          { name: 'Brave Explorer', description: 'A fearless space explorer' },
          { name: 'Curious Scientist', description: 'A scientist who loves discoveries' }
        ],
        settingOptions: [
          { name: 'Alien Planet', description: 'A colorful planet with strange creatures' },
          { name: 'Space Station', description: 'A high-tech space station' }
        ],
        plotPoints: [
          { title: 'Discovery', description: 'Find something unexpected', order: 1, optional: false }
        ],
        learningObjectives: [{ objective: 'Improve creative writing skills' }],
        subscriptionTier: 'free',
        featured: true,
        publishedAt: new Date().toISOString(),
      }
    ];
    
    return NextResponse.json({
      success: false,
      storyTemplates: fallbackTemplates,
      error: 'CMS unavailable, showing fallback content',
      total: fallbackTemplates.length,
    });
  }
}
