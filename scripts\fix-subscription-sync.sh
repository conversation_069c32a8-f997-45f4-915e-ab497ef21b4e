#!/bin/bash

# Script to fix subscription synchronization issues
# This script calls the admin API to fix accounts with payment/status mismatches

echo "🔧 Subscription Status Sync Fix Script"
echo "======================================"

# Set the base URL (adjust for your environment)
BASE_URL="http://localhost:3000"

# Check if we're in production
if [ "$NODE_ENV" = "production" ]; then
    BASE_URL="https://your-production-domain.com"
fi

echo "Using base URL: $BASE_URL"
echo ""

# First, check which accounts need fixing
echo "📊 Checking accounts that need fixing..."
response=$(curl -s "$BASE_URL/api/admin/fix-subscription-sync")

if [ $? -eq 0 ]; then
    echo "✅ Successfully checked accounts"
    echo "$response" | jq '.'
    
    # Extract the count of accounts needing fixing
    needs_fixing=$(echo "$response" | jq -r '.needsFixing // 0')
    
    if [ "$needs_fixing" -gt 0 ]; then
        echo ""
        echo "⚠️  Found $needs_fixing accounts that need fixing"
        echo ""
        read -p "Do you want to proceed with fixing these accounts? (y/N): " confirm
        
        if [[ $confirm =~ ^[Yy]$ ]]; then
            echo ""
            echo "🔄 Applying fixes..."
            
            # Apply the fixes
            fix_response=$(curl -s -X POST "$BASE_URL/api/admin/fix-subscription-sync")
            
            if [ $? -eq 0 ]; then
                echo "✅ Fix operation completed"
                echo "$fix_response" | jq '.'
                
                # Check if the fix was successful
                success=$(echo "$fix_response" | jq -r '.success // false')
                
                if [ "$success" = "true" ]; then
                    fixed_count=$(echo "$fix_response" | jq -r '.summary.accountsFixed // 0')
                    echo ""
                    echo "🎉 Successfully fixed $fixed_count accounts!"
                    echo ""
                    echo "📋 Summary:"
                    echo "$fix_response" | jq -r '.summary'
                    echo ""
                    echo "✅ All subscription statuses have been synchronized"
                    echo "💡 Users should now see the correct 'active' status in their UI"
                else
                    echo "❌ Fix operation failed"
                    echo "$fix_response" | jq -r '.error // "Unknown error"'
                fi
            else
                echo "❌ Failed to apply fixes"
            fi
        else
            echo "❌ Fix operation cancelled"
        fi
    else
        echo "✅ No accounts need fixing - all subscription statuses are synchronized"
    fi
else
    echo "❌ Failed to check accounts"
fi

echo ""
echo "🏁 Script completed"
