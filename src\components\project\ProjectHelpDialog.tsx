"use client";

import React from "react";
import CreativeToolHelpDialog from "@/components/shared/CreativeToolHelpDialog";

interface ProjectHelpDialogProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
}

const projectHelpSteps = [
    {
        title: "Choose Your Project Type",
        content: "Select whether you're creating a story, art, music, or general project to get relevant tools and suggestions."
    },
    {
        title: "Add a Creative Title",
        content: "Give your project a catchy, descriptive title that captures what it's about."
    },
    {
        title: "Describe Your Vision",
        content: "Write a brief description of what you want to create and what inspired you."
    },
    {
        title: "Create Your Content",
        content: "Start building your project! Write, plan, or describe your creative work in detail."
    },
    {
        title: "Get AI Ideas",
        content: "Use the 'Get Creative Ideas' button to receive AI-powered suggestions and inspiration."
    },
    {
        title: "Save & Share",
        content: "Save your project to your portfolio and download it to share with friends and family."
    }
];

const ProjectHelpDialog = ({ open, onOpenChange }: ProjectHelpDialogProps) => {
    return (
        <CreativeToolHelpDialog 
            open={open}
            onOpenChange={onOpenChange}
            title="Interactive Project Builder"
            description="Create and organize any creative project with AI-powered assistance"
            steps={projectHelpSteps}
            aiMentorMessage="Ready to start your creative project? I'll help you organize your ideas and bring them to life!"
        />
    );
};

export default ProjectHelpDialog;
