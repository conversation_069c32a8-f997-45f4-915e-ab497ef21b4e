
import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Check } from 'lucide-react';
import { MentorNameEditorProps } from './types';

export const MentorNameEditor: React.FC<MentorNameEditorProps> = ({
  isEditing,
  mentorName,
  setMentorName,
  onSave,
  setIsEditing,
  mentorCharacter,
  mentorNames
}) => {
  if (isEditing) {
    return (
      <div className="flex items-center gap-2 justify-center mb-4">
        <div className="flex items-center">
          <Input
            value={mentorName}
            onChange={(e) => setMentorName(e.target.value)}
            placeholder={`Name your ${mentor<PERSON>haracter}`}
                className="max-w-[200px] pr-10" // Added padding for the button
                autoFocus
          />
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={onSave} 
            className="absolute right-[calc(50%-100px)] ml-2"
          >
            <Check className="h-4 w-4" />
          </Button>
        </div>
      </div>
    );
  }

  return (
    <Button 
      size="sm" 
      variant="default"
      className="mb-4 flex items-center gap-1 mx-auto bg-green-500 hover:bg-green-600 text-white"
      onClick={() => setIsEditing(true)}
    >
      Rename {mentorNames[mentorCharacter]}
    </Button>
  );
};
