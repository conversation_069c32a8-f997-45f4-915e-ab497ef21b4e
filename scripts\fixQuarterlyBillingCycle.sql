-- Fix quarterly subscription billing cycles and end dates
-- Update billing_cycle from 'month' to 'quarter' for quarterly plans
-- Also fix end dates to be 3 months from start date

UPDATE profiles 
SET 
    billing_cycle = 'quarter',
    subscription_end = subscription_start + INTERVAL '3 months',
    updated_at = NOW()
WHERE plan_id = 'quarterly-tier' 
    AND subscription_status IN ('active', 'trialing', 'cancel_at_period_end')
    AND subscription_start IS NOT NULL;

-- Verify the fix
SELECT 
    email,
    plan_id,
    billing_cycle,
    subscription_status,
    subscription_start,
    subscription_end,
    EXTRACT(DAY FROM (subscription_end - subscription_start)) as duration_days
FROM profiles 
WHERE plan_id = 'quarterly-tier' 
    AND subscription_status IN ('active', 'trialing', 'cancel_at_period_end')
ORDER BY email; 