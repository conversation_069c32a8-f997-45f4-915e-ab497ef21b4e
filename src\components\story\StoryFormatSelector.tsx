import React from "react";
import { Button } from "@/components/ui/button";
import { useStory } from "./StoryContext";

interface StoryFormatSelectorProps {
    showOptions?: boolean;
    onGenreChange?: () => void;
}

const StoryFormatSelector = ({
    showOptions = true,
    onGenreChange,
}: StoryFormatSelectorProps) => {
    const { storyGenre, setStoryGenre } = useStory();

    const genres = [
        {
            id: "standard",
            label: "Standard",
            color: "bg-blue-500 hover:!bg-blue-600",
        },
        {
            id: "adventure",
            label: "Adventure",
            color: "!bg-littlespark-teal hover:!bg-littlespark-teal/90",
        },
        {
            id: "mystery",
            label: "Mystery",
            color: "!bg-littlespark-orange hover:!bg-littlespark-orange/90",
        },
        {
            id: "comedy",
            label: "Comedy",
            color: "!bg-littlespark-teal hover:!bg-littlespark-teal/90",
        },
        {
            id: "scifi",
            label: "Science Fiction",
            color: "!bg-littlespark-orange hover:!bg-littlespark-orange/90",
        },
    ];

    const handleGenreSelect = (genre: string) => {
        setStoryGenre(genre);
        if (onGenreChange) {
            onGenreChange();
        }
    };

    if (!showOptions) return null;

    return (
        <div className="mb-6">
            <h3 className="text-lg font-medium text-center mb-3">
                Story Genre
            </h3>
            <div className="flex flex-wrap justify-center gap-2">
                {genres.map(({ id, label, color }) => (
                    <Button
                        key={id}
                        variant={storyGenre === id ? "default" : "outline"}
                        onClick={() => handleGenreSelect(id)}
                        className={`rounded-full ${
                            storyGenre === id
                                ? color
                                : "hover:bg-gray-100 !border-littlespark-teal border-2 text-littlespark-teal !font-bold"
                        }`}
                    >
                        {label}
                    </Button>
                ))}
            </div>
        </div>
    );
};

export default StoryFormatSelector;
