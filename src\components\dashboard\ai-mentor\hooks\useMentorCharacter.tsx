import { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Mentor<PERSON><PERSON>s } from "../types";
import { generateCharacterGreeting } from "@/utils/ai/mentorService";

export const useMentorCharacter = () => {
    const [mentor<PERSON><PERSON>cter, setMentorCharacter] = useState<MentorCharacter>(
        () => {
            const saved = localStorage.getItem("mentorCharacter");
            return (saved as MentorCharacter) || "robot";
        }
    );

    const [mentorNames, setMentorNames] = useState<MentorNames>(() => {
        const saved = localStorage.getItem("mentorNames");
        return saved
            ? JSON.parse(saved)
            : {
                  robot: "Sparky",
                  owl: "Professor <PERSON><PERSON>",
                  explorer: "Captain Nova",
              };
    });

    const [mentorMessage, setMentorMessage] = useState("");

    // Clean up old localStorage entries on first load
    useEffect(() => {
        // Clean up any old non-user-specific chat history
        const oldChatHistory = localStorage.getItem('mentorChatHistory');
        if (oldChatHistory) {
            localStorage.removeItem('mentorChatHistory');
            console.log('Cleaned up old non-user-specific chat history');
        }
    }, []);

    useEffect(() => {
        localStorage.setItem("mentorCharacter", mentorCharacter);
        localStorage.setItem("mentorNames", JSON.stringify(mentorNames));
    }, [mentorCharacter, mentorNames]);

    useEffect(() => {
        setMentorMessage(
            generateCharacterGreeting(
                mentorCharacter,
                mentorNames[mentorCharacter]
            )
        );
    }, [mentorCharacter, mentorNames]);

    const changeMentorCharacter = (character: MentorCharacter) => {
        setMentorCharacter(character);
        const greeting = generateCharacterGreeting(
            character,
            mentorNames[character]
        );
        setMentorMessage(greeting);
    };

    const updateMentorName = (character: MentorCharacter, newName: string) => {
        if (newName) {
            const newNames = {
                ...mentorNames,
                [character]: newName,
            };

            setMentorNames(newNames);

            if (character === mentorCharacter) {
                setMentorMessage(generateCharacterGreeting(character, newName));
            }
        }
    };

    return {
        mentorCharacter,
        mentorNames,
        mentorMessage,
        changeMentorCharacter,
        updateMentorName,
    };
};
