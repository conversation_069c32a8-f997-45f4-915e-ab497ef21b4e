
export interface Question {
  id: string;
  question: string;
  answers: string[];
  type: 'multiple-choice' | 'open-ended';
  difficulty: 'easy' | 'medium' | 'hard' | 'expert';
  topic: string;
  explanation?: string;
  correctAnswerIndex?: number;
  userAnswer?: string;
  userAnswerIndex?: number;
  isCorrect?: boolean;
}

export type AgeGroup = '5-7' | '8-10' | '11-13' | '14-16' | '17+';

export interface MindSparkProps {
  userAge?: number;
  safeMode?: boolean;
  toolType?: 'general' | 'stem' | 'language' | 'story' | 'art' | 'game' | 'music';
}
