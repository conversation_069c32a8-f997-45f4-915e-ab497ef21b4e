/* tslint:disable */
/* eslint-disable */
/**
 * This file was automatically generated by Payload.
 * DO NOT MODIFY IT BY HAND. Instead, modify your source Payload config,
 * and re-run `payload generate:types` to regenerate this file.
 */

/**
 * Supported timezones in IANA format.
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "supportedTimezones".
 */
export type SupportedTimezones =
  | 'Pacific/Midway'
  | 'Pacific/Niue'
  | 'Pacific/Honolulu'
  | 'Pacific/Rarotonga'
  | 'America/Anchorage'
  | 'Pacific/Gambier'
  | 'America/Los_Angeles'
  | 'America/Tijuana'
  | 'America/Denver'
  | 'America/Phoenix'
  | 'America/Chicago'
  | 'America/Guatemala'
  | 'America/New_York'
  | 'America/Bogota'
  | 'America/Caracas'
  | 'America/Santiago'
  | 'America/Buenos_Aires'
  | 'America/Sao_Paulo'
  | 'Atlantic/South_Georgia'
  | 'Atlantic/Azores'
  | 'Atlantic/Cape_Verde'
  | 'Europe/London'
  | 'Europe/Berlin'
  | 'Africa/Lagos'
  | 'Europe/Athens'
  | 'Africa/Cairo'
  | 'Europe/Moscow'
  | 'Asia/Riyadh'
  | 'Asia/Dubai'
  | 'Asia/Baku'
  | 'Asia/Karachi'
  | 'Asia/Tashkent'
  | 'Asia/Calcutta'
  | 'Asia/Dhaka'
  | 'Asia/Almaty'
  | 'Asia/Jakarta'
  | 'Asia/Bangkok'
  | 'Asia/Shanghai'
  | 'Asia/Singapore'
  | 'Asia/Tokyo'
  | 'Asia/Seoul'
  | 'Australia/Brisbane'
  | 'Australia/Sydney'
  | 'Pacific/Guam'
  | 'Pacific/Noumea'
  | 'Pacific/Auckland'
  | 'Pacific/Fiji';

export interface Config {
  auth: {
    users: UserAuthOperations;
  };
  blocks: {};
  collections: {
    users: User;
    media: Media;
    challenges: Challenge;
    'story-templates': StoryTemplate;
    'educational-resources': EducationalResource;
    'payload-locked-documents': PayloadLockedDocument;
    'payload-preferences': PayloadPreference;
    'payload-migrations': PayloadMigration;
  };
  collectionsJoins: {};
  collectionsSelect: {
    users: UsersSelect<false> | UsersSelect<true>;
    media: MediaSelect<false> | MediaSelect<true>;
    challenges: ChallengesSelect<false> | ChallengesSelect<true>;
    'story-templates': StoryTemplatesSelect<false> | StoryTemplatesSelect<true>;
    'educational-resources': EducationalResourcesSelect<false> | EducationalResourcesSelect<true>;
    'payload-locked-documents': PayloadLockedDocumentsSelect<false> | PayloadLockedDocumentsSelect<true>;
    'payload-preferences': PayloadPreferencesSelect<false> | PayloadPreferencesSelect<true>;
    'payload-migrations': PayloadMigrationsSelect<false> | PayloadMigrationsSelect<true>;
  };
  db: {
    defaultIDType: number;
  };
  globals: {};
  globalsSelect: {};
  locale: null;
  user: User & {
    collection: 'users';
  };
  jobs: {
    tasks: unknown;
    workflows: unknown;
  };
}
export interface UserAuthOperations {
  forgotPassword: {
    email: string;
    password: string;
  };
  login: {
    email: string;
    password: string;
  };
  registerFirstUser: {
    email: string;
    password: string;
  };
  unlock: {
    email: string;
    password: string;
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users".
 */
export interface User {
  id: number;
  firstName: string;
  lastName: string;
  /**
   * User role determines access permissions
   */
  role: 'admin' | 'content-creator' | 'educator' | 'reviewer';
  /**
   * Brief bio about the content creator/educator
   */
  bio?: string | null;
  /**
   * Profile picture
   */
  avatar?: (number | null) | Media;
  /**
   * Areas of expertise for content creators
   */
  specialties?:
    | {
        specialty?: ('art' | 'story' | 'music' | 'coding' | 'video' | 'game') | null;
        id?: string | null;
      }[]
    | null;
  /**
   * Whether this user account is active
   */
  isActive?: boolean | null;
  updatedAt: string;
  createdAt: string;
  email: string;
  resetPasswordToken?: string | null;
  resetPasswordExpiration?: string | null;
  salt?: string | null;
  hash?: string | null;
  loginAttempts?: number | null;
  lockUntil?: string | null;
  sessions?:
    | {
        id: string;
        createdAt?: string | null;
        expiresAt: string;
      }[]
    | null;
  password?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media".
 */
export interface Media {
  id: number;
  alt: string;
  updatedAt: string;
  createdAt: string;
  url?: string | null;
  thumbnailURL?: string | null;
  filename?: string | null;
  mimeType?: string | null;
  filesize?: number | null;
  width?: number | null;
  height?: number | null;
  focalX?: number | null;
  focalY?: number | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "challenges".
 */
export interface Challenge {
  id: number;
  /**
   * The main title of the creative challenge
   */
  title: string;
  /**
   * URL-friendly version of the title
   */
  slug: string;
  /**
   * Brief description of what children will create
   */
  description: string;
  /**
   * Choose the primary category - this determines which tool opens when users click "Start Challenge"
   */
  category: 'art' | 'story' | 'music' | 'game' | 'coding' | 'video';
  /**
   * Age groups this challenge is suitable for
   */
  ageGroup: ('6-8' | '9-11' | '12-14')[];
  /**
   * Difficulty level for children
   */
  difficulty: 'easy' | 'medium' | 'hard';
  /**
   * Estimated completion time in minutes
   */
  estimatedTime: number;
  /**
   * Step-by-step instructions for completing the challenge
   */
  instructions: string;
  /**
   * What children will learn from this challenge
   */
  learningObjectives: {
    objective: string;
    id?: string | null;
  }[];
  /**
   * Materials needed to complete the challenge
   */
  materials?:
    | {
        material: string;
        optional?: boolean | null;
        id?: string | null;
      }[]
    | null;
  /**
   * Images, videos, and other media for this challenge
   */
  media?:
    | {
        file: number | Media;
        caption?: string | null;
        type: 'tutorial' | 'example' | 'reference' | 'step';
        /**
         * Display order (0 = first)
         */
        order?: number | null;
        id?: string | null;
      }[]
    | null;
  /**
   * Whether this challenge requires a subscription
   */
  subscriptionTier: 'free' | 'premium';
  /**
   * Show this challenge prominently on the homepage
   */
  featured?: boolean | null;
  seasonal?: {
    isSeasonalContent?: boolean | null;
    season?: ('spring' | 'summer' | 'fall' | 'winter' | 'halloween' | 'christmas' | 'newyear') | null;
  };
  /**
   * Publication status of this challenge
   */
  status: 'draft' | 'review' | 'published' | 'archived';
  /**
   * When this challenge was published
   */
  publishedAt?: string | null;
  /**
   * Content creator who made this challenge
   */
  createdBy: number | User;
  updatedAt: string;
  createdAt: string;
  _status?: ('draft' | 'published') | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "story-templates".
 */
export interface StoryTemplate {
  id: number;
  /**
   * Title of the story template
   */
  title: string;
  slug: string;
  /**
   * Brief description of the story template
   */
  description: string;
  genre: (
    | 'adventure'
    | 'fantasy'
    | 'sci-fi'
    | 'mystery'
    | 'friendship'
    | 'family'
    | 'animals'
    | 'magic'
    | 'space'
    | 'underwater'
  )[];
  ageGroup: ('6-8' | '9-11' | '12-14')[];
  /**
   * The opening prompt that starts the story
   */
  storyPrompt: string;
  /**
   * Character options children can choose from
   */
  characterOptions: {
    name: string;
    description: string;
    image?: (number | null) | Media;
    id?: string | null;
  }[];
  /**
   * Setting options for the story
   */
  settingOptions: {
    name: string;
    description: string;
    image?: (number | null) | Media;
    id?: string | null;
  }[];
  /**
   * Key plot points to guide the story structure
   */
  plotPoints: {
    title: string;
    description: string;
    order: number;
    /**
     * Whether this plot point is optional
     */
    optional?: boolean | null;
    id?: string | null;
  }[];
  /**
   * Additional writing prompts to help children
   */
  writingPrompts?:
    | {
        prompt: string;
        category?: ('character' | 'setting' | 'action' | 'dialogue' | 'emotion') | null;
        id?: string | null;
      }[]
    | null;
  /**
   * Suggested story length
   */
  estimatedLength?: {
    minWords?: number | null;
    maxWords?: number | null;
  };
  /**
   * What children will learn from this story template
   */
  learningObjectives: {
    objective: string;
    id?: string | null;
  }[];
  subscriptionTier: 'free' | 'premium';
  featured?: boolean | null;
  seasonal?: {
    isSeasonalContent?: boolean | null;
    season?: ('spring' | 'summer' | 'fall' | 'winter' | 'halloween' | 'christmas' | 'newyear') | null;
  };
  status: 'draft' | 'review' | 'published' | 'archived';
  publishedAt?: string | null;
  createdBy?: (number | null) | User;
  updatedAt: string;
  createdAt: string;
  _status?: ('draft' | 'published') | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "educational-resources".
 */
export interface EducationalResource {
  id: number;
  /**
   * Title of the educational resource
   */
  title: string;
  slug: string;
  /**
   * Brief description of the resource
   */
  description: string;
  type: 'tutorial' | 'guide' | 'reference' | 'video' | 'interactive' | 'worksheet' | 'tips';
  subject: (
    | 'art-techniques'
    | 'color-theory'
    | 'drawing-basics'
    | 'creative-writing'
    | 'storytelling'
    | 'music-theory'
    | 'coding-basics'
    | 'game-design'
    | 'video-creation'
    | 'digital-art'
  )[];
  ageGroup: ('6-8' | '9-11' | '12-14')[];
  /**
   * Main content of the educational resource
   */
  content: string;
  media?:
    | {
        file: number | Media;
        caption?: string | null;
        type: 'main' | 'step' | 'example' | 'video' | 'audio' | 'download';
        id?: string | null;
      }[]
    | null;
  /**
   * Files that children/parents can download
   */
  downloadableFiles?:
    | {
        file: number | Media;
        title: string;
        description?: string | null;
        fileType?: ('pdf' | 'template' | 'chart' | 'audio' | 'video') | null;
        id?: string | null;
      }[]
    | null;
  /**
   * Challenges that use this educational resource
   */
  relatedChallenges?: (number | Challenge)[] | null;
  /**
   * Skills children should have before using this resource
   */
  prerequisites?:
    | {
        skill: string;
        level?: ('beginner' | 'intermediate' | 'advanced') | null;
        id?: string | null;
      }[]
    | null;
  /**
   * What children will learn from this resource
   */
  learningOutcomes: {
    outcome: string;
    id?: string | null;
  }[];
  /**
   * Estimated reading/viewing time in minutes
   */
  estimatedReadTime?: number | null;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  subscriptionTier: 'free' | 'premium';
  featured?: boolean | null;
  /**
   * Tags for better searchability
   */
  tags?:
    | {
        tag: string;
        id?: string | null;
      }[]
    | null;
  status: 'draft' | 'review' | 'published' | 'archived';
  publishedAt?: string | null;
  createdBy?: (number | null) | User;
  updatedAt: string;
  createdAt: string;
  _status?: ('draft' | 'published') | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents".
 */
export interface PayloadLockedDocument {
  id: number;
  document?:
    | ({
        relationTo: 'users';
        value: number | User;
      } | null)
    | ({
        relationTo: 'media';
        value: number | Media;
      } | null)
    | ({
        relationTo: 'challenges';
        value: number | Challenge;
      } | null)
    | ({
        relationTo: 'story-templates';
        value: number | StoryTemplate;
      } | null)
    | ({
        relationTo: 'educational-resources';
        value: number | EducationalResource;
      } | null);
  globalSlug?: string | null;
  user: {
    relationTo: 'users';
    value: number | User;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences".
 */
export interface PayloadPreference {
  id: number;
  user: {
    relationTo: 'users';
    value: number | User;
  };
  key?: string | null;
  value?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations".
 */
export interface PayloadMigration {
  id: number;
  name?: string | null;
  batch?: number | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users_select".
 */
export interface UsersSelect<T extends boolean = true> {
  firstName?: T;
  lastName?: T;
  role?: T;
  bio?: T;
  avatar?: T;
  specialties?:
    | T
    | {
        specialty?: T;
        id?: T;
      };
  isActive?: T;
  updatedAt?: T;
  createdAt?: T;
  email?: T;
  resetPasswordToken?: T;
  resetPasswordExpiration?: T;
  salt?: T;
  hash?: T;
  loginAttempts?: T;
  lockUntil?: T;
  sessions?:
    | T
    | {
        id?: T;
        createdAt?: T;
        expiresAt?: T;
      };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media_select".
 */
export interface MediaSelect<T extends boolean = true> {
  alt?: T;
  updatedAt?: T;
  createdAt?: T;
  url?: T;
  thumbnailURL?: T;
  filename?: T;
  mimeType?: T;
  filesize?: T;
  width?: T;
  height?: T;
  focalX?: T;
  focalY?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "challenges_select".
 */
export interface ChallengesSelect<T extends boolean = true> {
  title?: T;
  slug?: T;
  description?: T;
  category?: T;
  ageGroup?: T;
  difficulty?: T;
  estimatedTime?: T;
  instructions?: T;
  learningObjectives?:
    | T
    | {
        objective?: T;
        id?: T;
      };
  materials?:
    | T
    | {
        material?: T;
        optional?: T;
        id?: T;
      };
  media?:
    | T
    | {
        file?: T;
        caption?: T;
        type?: T;
        order?: T;
        id?: T;
      };
  subscriptionTier?: T;
  featured?: T;
  seasonal?:
    | T
    | {
        isSeasonalContent?: T;
        season?: T;
      };
  status?: T;
  publishedAt?: T;
  createdBy?: T;
  updatedAt?: T;
  createdAt?: T;
  _status?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "story-templates_select".
 */
export interface StoryTemplatesSelect<T extends boolean = true> {
  title?: T;
  slug?: T;
  description?: T;
  genre?: T;
  ageGroup?: T;
  storyPrompt?: T;
  characterOptions?:
    | T
    | {
        name?: T;
        description?: T;
        image?: T;
        id?: T;
      };
  settingOptions?:
    | T
    | {
        name?: T;
        description?: T;
        image?: T;
        id?: T;
      };
  plotPoints?:
    | T
    | {
        title?: T;
        description?: T;
        order?: T;
        optional?: T;
        id?: T;
      };
  writingPrompts?:
    | T
    | {
        prompt?: T;
        category?: T;
        id?: T;
      };
  estimatedLength?:
    | T
    | {
        minWords?: T;
        maxWords?: T;
      };
  learningObjectives?:
    | T
    | {
        objective?: T;
        id?: T;
      };
  subscriptionTier?: T;
  featured?: T;
  seasonal?:
    | T
    | {
        isSeasonalContent?: T;
        season?: T;
      };
  status?: T;
  publishedAt?: T;
  createdBy?: T;
  updatedAt?: T;
  createdAt?: T;
  _status?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "educational-resources_select".
 */
export interface EducationalResourcesSelect<T extends boolean = true> {
  title?: T;
  slug?: T;
  description?: T;
  type?: T;
  subject?: T;
  ageGroup?: T;
  content?: T;
  media?:
    | T
    | {
        file?: T;
        caption?: T;
        type?: T;
        id?: T;
      };
  downloadableFiles?:
    | T
    | {
        file?: T;
        title?: T;
        description?: T;
        fileType?: T;
        id?: T;
      };
  relatedChallenges?: T;
  prerequisites?:
    | T
    | {
        skill?: T;
        level?: T;
        id?: T;
      };
  learningOutcomes?:
    | T
    | {
        outcome?: T;
        id?: T;
      };
  estimatedReadTime?: T;
  difficulty?: T;
  subscriptionTier?: T;
  featured?: T;
  tags?:
    | T
    | {
        tag?: T;
        id?: T;
      };
  status?: T;
  publishedAt?: T;
  createdBy?: T;
  updatedAt?: T;
  createdAt?: T;
  _status?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents_select".
 */
export interface PayloadLockedDocumentsSelect<T extends boolean = true> {
  document?: T;
  globalSlug?: T;
  user?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences_select".
 */
export interface PayloadPreferencesSelect<T extends boolean = true> {
  user?: T;
  key?: T;
  value?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations_select".
 */
export interface PayloadMigrationsSelect<T extends boolean = true> {
  name?: T;
  batch?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "auth".
 */
export interface Auth {
  [k: string]: unknown;
}


declare module 'payload' {
  export interface GeneratedTypes extends Config {}
}