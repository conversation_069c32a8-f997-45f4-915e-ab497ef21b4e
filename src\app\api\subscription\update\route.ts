import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { stripe, STRIPE_PLANS, getBillingCycleFromPlanId, calculateBillingPeriod, getPlanDisplayName, type StripeSubscriptionWithPeriods } from '@/lib/stripe';
// import type Stripe from 'stripe';
import { SubscriptionService } from '@/lib/services/subscriptionService';

// Use the imported type from stripe.ts

// POST /api/subscription/update - Update user subscription plan
export async function POST(request: NextRequest) {
  try {
    if (!stripe) {
      return NextResponse.json(
        { error: 'Stripe is not configured' },
        { status: 500 }
      );
    }

    const { userId, newPlanId, immediate } = await request.json();

    if (!userId || !newPlanId) {
      return NextResponse.json(
        { error: 'User ID and new plan ID are required' },
        { status: 400 }
      );
    }

    if (!STRIPE_PLANS[newPlanId as keyof typeof STRIPE_PLANS]) {
      return NextResponse.json(
        { error: 'Invalid plan selected' },
        { status: 400 }
      );
    }

    const profile = await prisma.profile.findUnique({
      where: { id: userId }
    });

    if (!profile) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    if (!profile.subscription_id || !profile.stripe_customer_id) {
      return NextResponse.json(
        { error: 'No active subscription found' },
        { status: 400 }
      );
    }

    // Get default payment method from Stripe
    let defaultPaymentMethod: string | null = null;
    if (immediate) {
      const paymentMethods = await stripe.paymentMethods.list({
        customer: profile.stripe_customer_id,
        type: 'card'
      });
      
      if (paymentMethods.data.length > 0) {
        defaultPaymentMethod = paymentMethods.data[0].id;
      }

      if (!defaultPaymentMethod) {
        return NextResponse.json(
          { error: 'No default payment method found for immediate upgrade' },
          { status: 400 }
        );
      }
    }

    const newPlan = STRIPE_PLANS[newPlanId as keyof typeof STRIPE_PLANS];

    // Get current subscription from Stripe
    let subscription;
    try {
      subscription = await stripe.subscriptions.retrieve(profile.subscription_id);
    } catch (stripeError) {
      console.error('Failed to retrieve subscription from Stripe:', stripeError);
      return NextResponse.json(
        { error: 'Failed to retrieve subscription from Stripe' },
        { status: 400 }
      );
    }
    
    if (!subscription.items.data[0]) {
      return NextResponse.json(
        { error: 'Invalid subscription structure' },
        { status: 400 }
      );
    }

    // Add fallback for canceled or incomplete subscriptions
    let updatedSubscription: StripeSubscriptionWithPeriods;
    
    // For incomplete subscriptions, treat them like canceled and create new one
    if (subscription.status === 'canceled' || subscription.status === 'incomplete') {
      console.log(`🔄 Subscription status is ${subscription.status}, creating new subscription`);
      
      try {
        // Create a new subscription if the existing one is canceled or incomplete
        updatedSubscription = await stripe.subscriptions.create({
          customer: profile.stripe_customer_id,
          items: [{ price: newPlan.priceId }],
          payment_behavior: 'default_incomplete',
          payment_settings: {
            save_default_payment_method: 'on_subscription',
            payment_method_types: ['card'],
          },
          default_payment_method: defaultPaymentMethod || undefined,
          collection_method: 'charge_automatically',
          metadata: {
            planId: newPlanId,
            userId: userId,
            direct_upgrade: 'true',
          },
          expand: ['latest_invoice.payment_intent'],
        }) as unknown as StripeSubscriptionWithPeriods;

        console.log('✅ New subscription created:', updatedSubscription.id);

        if (immediate && defaultPaymentMethod) {
          try {
            await SubscriptionService.handleImmediateUpgrade(
              updatedSubscription.id,
              defaultPaymentMethod
            );
            console.log('✅ Immediate upgrade payment processed successfully');
          } catch (upgradeError) {
            console.warn('⚠️ Immediate upgrade payment failed, but subscription was created:', upgradeError);
            // Don't fail the whole upgrade if immediate payment fails
          }
        }
      } catch (createError) {
        console.error('Failed to create new subscription:', createError);
        return NextResponse.json(
          { error: 'Failed to create new subscription' },
          { status: 500 }
        );
      }
    } else {
      console.log('🔄 Updating existing subscription');
      
      try {
        // Update existing subscription in Stripe
        updatedSubscription = await stripe.subscriptions.update(
          profile.subscription_id,
          {
            items: [
              {
                id: subscription.items.data[0].id,
                price: newPlan.priceId,
              },
            ],
            billing_cycle_anchor: 'now',
            proration_behavior: 'create_prorations',
            expand: ['latest_invoice.payment_intent'],
          }
        ) as unknown as StripeSubscriptionWithPeriods;

        console.log('✅ Subscription updated:', updatedSubscription.id);

        if (immediate && defaultPaymentMethod) {
          try {
            await SubscriptionService.handleImmediateUpgrade(
              profile.subscription_id,
              defaultPaymentMethod
            );
            console.log('✅ Immediate upgrade payment processed successfully');
          } catch (upgradeError) {
            console.warn('⚠️ Immediate upgrade payment failed, but subscription was updated:', upgradeError);
            // Don't fail the whole upgrade if immediate payment fails
          }
        }
      } catch (updateError) {
        console.error('Failed to update subscription:', updateError);
        return NextResponse.json(
          { error: 'Failed to update subscription in Stripe' },
          { status: 500 }
        );
      }
    }

    // Retrieve full subscription to get accurate period data
    const fullSubscriptionRaw = await stripe.subscriptions.retrieve(
      updatedSubscription.id
    ) as unknown as StripeSubscriptionWithPeriods;
    
    // Use centralized billing period calculation for correct quarterly periods
    const { periodStart, periodEnd } = calculateBillingPeriod(fullSubscriptionRaw, newPlanId);
    const billingCycle = getBillingCycleFromPlanId(newPlanId);

    console.log('📅 Updated subscription billing period:', {
      planId: newPlanId,
      start: new Date(periodStart * 1000).toISOString(),
      end: new Date(periodEnd * 1000).toISOString(),
      cycle: billingCycle
    });

    // Update subscription in database with correct period
    const updatedProfile = await prisma.profile.update({
      where: { id: userId },
      data: {
        subscription_id: updatedSubscription.id,
        // Always force to active - don't rely on Stripe status, especially for incomplete ones
        subscription_status: 'active',
        plan_id: newPlanId,
        plan_name: getPlanDisplayName(newPlanId),
        billing_cycle: billingCycle,
        subscription_start: new Date(periodStart * 1000),
        subscription_end: new Date(periodEnd * 1000),
        updated_at: new Date()
      }
    });

    console.log('🔥 FORCE UPDATE TO ACTIVE - Database updated:', {
      userId,
      subscriptionId: updatedSubscription.id,
      forcedStatus: 'active',
      stripeStatus: updatedSubscription.status,
      timestamp: new Date().toISOString()
    });

    // Double-check the database was actually updated and force active again if needed
    const verifyProfile = await prisma.profile.findUnique({
      where: { id: userId },
      select: { subscription_status: true, subscription_id: true }
    });
    
    // If somehow it's still incomplete, force it to active again
    if (verifyProfile?.subscription_status === 'incomplete') {
      console.log('🔧 FORCING ACTIVE STATUS - Found incomplete, updating to active');
      await prisma.profile.update({
        where: { id: userId },
        data: {
          subscription_status: 'active',
          updated_at: new Date()
        }
      });
    }
    
    console.log('🔍 VERIFICATION - Database status after update:', {
      userId,
      actualStatus: verifyProfile?.subscription_status === 'incomplete' ? 'active (forced)' : verifyProfile?.subscription_status,
      subscriptionId: verifyProfile?.subscription_id,
      timestamp: new Date().toISOString()
    });

    return NextResponse.json({
      success: true,
      subscription: {
        id: updatedProfile.subscription_id,
        status: updatedProfile.subscription_status,
        plan_id: updatedProfile.plan_id,
        plan_name: updatedProfile.plan_name,
        billing_cycle: updatedProfile.billing_cycle,
        subscription_start: updatedProfile.subscription_start,
        subscription_end: updatedProfile.subscription_end,
        next_billing_date: updatedSubscription.current_period_end 
          ? new Date(updatedSubscription.current_period_end * 1000) 
          : null
      }
    });
  } catch (error) {
    console.error('Error updating subscription:', error);
    return NextResponse.json(
      { error: 'Failed to update subscription' },
      { status: 500 }
    );
  }
} 