import { notFound } from 'next/navigation';
import { getChallenge, getMediaUrl } from '@/lib/cms-api';
import { ChallengeDetailClient } from '@/components/cms/ChallengeDetailClient';

interface ChallengePageProps {
  params: {
    slug: string;
  };
}

export default async function ChallengePage({ params }: ChallengePageProps) {
  const challenge = await getChallenge(params.slug);

  if (!challenge) {
    notFound();
  }

  return <ChallengeDetailClient challenge={challenge} />;
}

// Generate metadata for SEO
export async function generateMetadata({ params }: ChallengePageProps) {
  const challenge = await getChallenge(params.slug);

  if (!challenge) {
    return {
      title: 'Challenge Not Found',
    };
  }

  return {
    title: `${challenge.title} | Little Spark`,
    description: challenge.description.replace(/<[^>]*>/g, '').substring(0, 160),
    openGraph: {
      title: challenge.title,
      description: challenge.description.replace(/<[^>]*>/g, '').substring(0, 160),
      images: challenge.media?.length > 0 ? [
        {
          url: getMediaUrl(challenge.media[0].file.url),
          alt: challenge.media[0].file.alt || challenge.title,
        }
      ] : [],
    },
  };
}
