
import React from 'react';

interface MusicMoodSelectorProps {
  selectedMood: string;
  setSelectedMood: (mood: string) => void;
}

const MusicMoodSelector = ({ selectedMood, setSelectedMood }: MusicMoodSelectorProps) => {
  const moods = [
    'Happy',
    'Sad', 
    'Epic',
    'Mysterious',
    'Energetic',
    'Relaxing'
  ];
  
  return (
    <div>
      <label className="block text-sm font-medium mb-2">Music Mood</label>
      <div className="grid grid-cols-2 gap-2">
        {moods.map((mood) => (
          <button 
            key={mood}
            className={`px-4 py-1.5 rounded-full text-sm font-semibold transition-all duration-200 border-2 ${
              selectedMood === mood.toLowerCase() 
                ? 'bg-emerald-500 text-white border-emerald-500 hover:bg-blue-400 hover:border-blue-300' 
                : 'bg-transparent text-emerald-500 border-emerald-500 hover:bg-emerald-500 hover:text-white hover:border-emerald-500'
            }`}
            onClick={() => setSelectedMood(mood.toLowerCase())}
          >
            {mood}
          </button>
        ))}
      </div>
    </div>
  );
};

export default MusicMoodSelector;
