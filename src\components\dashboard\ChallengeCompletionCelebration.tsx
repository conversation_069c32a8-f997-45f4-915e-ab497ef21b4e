import React from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { X, Trophy, Star, Sparkles } from 'lucide-react';

interface Badge {
  name: string;
  icon: string;
  earned: boolean;
}

interface ChallengeCompletionCelebrationProps {
  isOpen: boolean;
  onClose: () => void;
  challengeTitle: string;
  challengeType: string;
  contentTitle: string;
  newBadgesEarned: Badge[];
  updatedProgress: {
    completedProjects: number;
    skillLevel: number;
    streakDays: number;
    totalBadges: number;
  };
}

const ChallengeCompletionCelebration: React.FC<ChallengeCompletionCelebrationProps> = ({
  isOpen,
  onClose,
  challengeTitle,
  challengeType,
  contentTitle,
  newBadgesEarned,
  updatedProgress
}) => {
  if (!isOpen) return null;

  const getTypeEmoji = (type: string) => {
    switch (type) {
      case 'story': return '📚';
      case 'art': return '🎨';
      case 'music': return '🎵';
      case 'game': return '🎮';
      default: return '✨';
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <Card className="bg-white rounded-lg shadow-xl max-w-md w-full p-6 relative animate-in fade-in-0 zoom-in-95 duration-300">
        {/* Close button */}
        <Button
          variant="ghost"
          size="sm"
          className="absolute top-2 right-2"
          onClick={onClose}
        >
          <X className="h-4 w-4" />
        </Button>

        {/* Celebration header */}
        <div className="text-center mb-6">
          <div className="text-6xl mb-2">🎉</div>
          <h2 className="text-2xl font-bold text-green-600 mb-2">
            Challenge Completed!
          </h2>
          <div className="flex items-center justify-center gap-2 text-lg">
            <span className="text-2xl">{getTypeEmoji(challengeType)}</span>
            <span className="font-semibold">{challengeTitle}</span>
          </div>
          <p className="text-gray-600 mt-2">
            Created: &quot;{contentTitle}&quot;
          </p>
        </div>

        {/* New badges earned */}
        {newBadgesEarned.length > 0 && (
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
              <Trophy className="h-5 w-5 text-yellow-500" />
              New Badges Earned!
            </h3>
            <div className="grid grid-cols-2 gap-2">
              {newBadgesEarned.map((badge, index) => (
                <div
                  key={index}
                  className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 text-center animate-pulse"
                >
                  <div className="text-2xl mb-1">{badge.icon}</div>
                  <div className="text-sm font-medium">{badge.name}</div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Progress updates */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
            <Star className="h-5 w-5 text-purple-500" />
            Your Progress
          </h3>
          <div className="grid grid-cols-2 gap-4 text-center">
            <div className="bg-blue-50 rounded-lg p-3">
              <div className="text-2xl font-bold text-blue-600">
                {updatedProgress.completedProjects}
              </div>
              <div className="text-sm text-blue-700">Projects Created</div>
            </div>
            <div className="bg-purple-50 rounded-lg p-3">
              <div className="text-2xl font-bold text-purple-600">
                Level {updatedProgress.skillLevel}
              </div>
              <div className="text-sm text-purple-700">Skill Level</div>
            </div>
            <div className="bg-orange-50 rounded-lg p-3">
              <div className="text-2xl font-bold text-orange-600">
                {updatedProgress.streakDays}
              </div>
              <div className="text-sm text-orange-700">Day Streak</div>
            </div>
            <div className="bg-green-50 rounded-lg p-3">
              <div className="text-2xl font-bold text-green-600">
                {updatedProgress.totalBadges}
              </div>
              <div className="text-sm text-green-700">Badges Earned</div>
            </div>
          </div>
        </div>

        {/* Action buttons */}
        <div className="flex gap-3">
          <Button
            onClick={onClose}
            className="flex-1 bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white"
          >
            <Sparkles className="h-4 w-4 mr-2" />
            Awesome!
          </Button>
        </div>
      </Card>
    </div>
  );
};

export default ChallengeCompletionCelebration;
