"use client";

import React from "react";
import { Textarea } from "@/components/ui/textarea";
import { Info } from "lucide-react";
import { useProject } from "./ProjectContext";

const ProjectDescription = () => {
    const { description, setDescription, learningMode } = useProject();

    return (
        <div className="mb-6 text-center flex flex-col items-center">
            <h3 className="text-lg font-medium mb-2">Project Description</h3>
            <Textarea
                placeholder="Describe what your project is about..."
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                className="mb-4 rounded-xl px-4 py-4 placeholder:text-center placeholder:text-gray-400 min-h-[100px] resize-none"
            />
            {learningMode && (
                <div className="flex items-center gap-2 bg-littlespark-light-blue-2 border border-littlespark-light-blue p-4 rounded-full">
                    <Info className="h-5 w-5 text-blue-500" />
                    <div className="">
                        <h4 className="font-semibold text-sm text-blue-600 font-fredoka">
                            Project Planning
                        </h4>
                        <p className="text-sm text-black">
                            A clear description helps you organize your thoughts and makes it easier for others to understand your project.
                        </p>
                    </div>
                </div>
            )}
        </div>
    );
};

export default ProjectDescription;
