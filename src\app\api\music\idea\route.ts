import { NextResponse } from 'next/server';

import { ChatGroq } from "@langchain/groq";
import { moderateContent } from '@/utils/ai/contentModeration';

const llm = new ChatGroq({
  model: 'llama-3.3-70b-versatile',
  apiKey: process.env.GROQ_API_KEY || ''
});

export async function POST(request: Request) {
  try {
    const { style, mood, theme, duration = 30 } = await request.json();

    const userKey = request.headers.get('x-forwarded-for') ?? 'anonymous';
    const inputSummary = `${style} ${mood} ${theme}`;
    const inCheck = await moderateContent(inputSummary, 'prompt', userKey);
    if (!inCheck.isAppropriate) {
      return NextResponse.json({ error: inCheck.reason, blocked: inCheck.blocked ?? false }, { status: 400 });
    }
    const timeOfDay = new Date().toLocaleTimeString(undefined, { hour: '2-digit', minute: '2-digit' });
    const promptParts: string[] = [];
    if (style) promptParts.push(`style: ${style}`);
    if (mood) promptParts.push(`mood: ${mood}`);
    if (theme) promptParts.push(`theme: ${theme}`);
    if (duration) promptParts.push(`duration: ${duration} seconds`);
    promptParts.push(`time of day: ${timeOfDay}`);
    const prompt = `You are an AI music composer. Create a unique music composition that perfectly captures the ${mood} mood in ${style} style${theme ? ` with the theme of ${theme}` : ''}. The composition should be ${duration} seconds long. 

Focus on:
- How the ${mood} mood influences the tempo, dynamics, and emotional progression
- Specific instruments and arrangements typical of ${style} music that enhance the ${mood} feeling
- Musical techniques, chord progressions, and rhythmic patterns that embody both the ${style} and ${mood}
- Sound design elements and production choices that support the overall atmosphere

Respond with ONLY a valid JSON object containing:
- "title": A compelling title (max 5 words) that reflects both ${style} style and ${mood} mood
- "description": Detailed description (3-4 sentences) explaining how the ${style} and ${mood} are expressed through specific instruments, musical arrangements, tempo, dynamics, and overall feel

IMPORTANT: Return ONLY the JSON object, no additional text, explanations, or markdown formatting.`;

    const res = await llm.invoke(prompt);
    let content = res.content as string;

    const outCheck = await moderateContent(content, 'text');
    if (!outCheck.isAppropriate) {
      content = '{"title":"Safe Tune","description":"Let\'s create a friendly and positive composition!"}';
    }
    
    // Clean up the response to extract JSON
    content = content.trim();
    
    // Remove markdown code fences if present
    content = content.replace(/^```(?:json)?\s*/, '').replace(/```\s*$/, '');
    
    // Extract JSON object if wrapped in other text
    const jsonMatch = content.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      content = jsonMatch[0];
    }
    
    // Try to parse and validate the JSON
    try {
      const parsed = JSON.parse(content);
      if (parsed.title && parsed.description) {
        return NextResponse.json({ 
          idea: content,
          title: parsed.title,
          description: parsed.description
        });
      }
    } catch (parseError) {
      console.error('JSON parse error:', parseError);
      console.error('Raw content:', content);
    }
    
    // Fallback: return raw content
    return NextResponse.json({ idea: content });
  } catch (error) {
    console.error('Error generating music idea:', error);
    return NextResponse.json({ error: 'Failed to generate music idea' }, { status: 500 });
  }
} 