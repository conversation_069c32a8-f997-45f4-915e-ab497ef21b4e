import React, { useEffect, useState, useCallback } from "react";
import Achievements from "@/components/dashboard/Achievements";
import ProgressDashboard from "@/components/ProgressDashboard";
import { useAuth } from "@/hooks/useAuth";

interface UserProgressProps {
    isLoading: boolean;
    userData: {
        completedProjects: number;
        skillLevel: number;
        skillProgress?: number;
        streakDays: number;
        badges: Array<{
            name: string;
            icon: string;
            earned: boolean;
        }>;
    };
}

const UserProgress = ({
    isLoading: initialLoading,
    userData: initialUserData,
}: UserProgressProps) => {
    const { user } = useAuth();
    const [isLoading, setIsLoading] = useState(initialLoading);
    const [userData, setUserData] = useState(initialUserData);

    const fetchRealUserData = useCallback(async () => {
        if (!user) return;

        try {
            setIsLoading(true);

            const response = await fetch('/api/user-progress');
            const data = await response.json();

            if (data.success) {
                setUserData({
                    completedProjects: data.progress.completedProjects,
                    skillLevel: data.progress.skillLevel,
                    skillProgress: data.progress.skillProgress,
                    streakDays: data.progress.streakDays,
                    badges: data.progress.badges
                });
            } else {
                console.error('Failed to fetch user progress:', data.error);
                // Fallback to initial data if there's an error
                setUserData(initialUserData);
            }
        } catch (error) {
            console.error("Error fetching user data:", error);
            // Fallback to initial data if there's an error
            setUserData(initialUserData);
        } finally {
            setIsLoading(false);
        }
    }, [user, initialUserData]);

    useEffect(() => {
        fetchRealUserData();
    }, [fetchRealUserData]);

    // Listen for challenge completion and content save events to update progress in real-time
    useEffect(() => {
        const handleChallengeCompleted = (event: CustomEvent) => {
            console.log('Challenge completed, updating progress...', event.detail);
            // Refresh user data when a challenge is completed
            fetchRealUserData();
        };

        const handleContentSaved = (event: CustomEvent) => {
            console.log('Content saved, updating progress...', event.detail);
            // Refresh user data when content is saved
            fetchRealUserData();
        };

        window.addEventListener('challengeCompleted', handleChallengeCompleted as EventListener);
        window.addEventListener('contentSaved', handleContentSaved as EventListener);

        return () => {
            window.removeEventListener('challengeCompleted', handleChallengeCompleted as EventListener);
            window.removeEventListener('contentSaved', handleContentSaved as EventListener);
        };
    }, [fetchRealUserData]);

    return (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 lg:gap-8">
            <Achievements isLoading={isLoading} userData={userData} />
            <ProgressDashboard
                completedProjects={userData.completedProjects}
                skillLevel={userData.skillLevel}
                skillProgress={userData.skillProgress}
                streakDays={userData.streakDays}
                badges={userData.badges}
            />
        </div>
    );
};

export default UserProgress;
