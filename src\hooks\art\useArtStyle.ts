"use client";
import { useState } from 'react';

export type ArtStyleType = 'cartoon' | 'realistic' | 'comic' | 'watercolor' | 'pixel';

export const useArtStyle = () => {
  const [artStyle, setArtStyle] = useState<ArtStyleType>('cartoon');
  
  const getStyleModifier = (style: ArtStyleType): string => {
    switch(style) {
      case 'cartoon':
        return "in a colorful cartoon style with vibrant colors and cute characters";
      case 'realistic':
        return "in a photorealistic style with detailed textures and realistic lighting";
      case 'comic':
        return "in a comic book style with bold outlines, dynamic poses and action lines";
      case 'watercolor':
        return "in a delicate watercolor style with soft colors and gentle brush strokes";
      case 'pixel':
        return "in a retro pixel art style reminiscent of classic video games";
      default:
        return "in a colorful, child-friendly style";
    }
  };
  
  return {
    artStyle,
    setArtStyle,
    getStyleModifier
  };
};
