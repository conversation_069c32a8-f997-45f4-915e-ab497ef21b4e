import { NextResponse } from 'next/server';
import { ChatGroq } from '@langchain/groq';
import { moderateContent } from '@/utils/ai/contentModeration';

const llm = new ChatGroq({
  model: 'llama-3.3-70b-versatile',
  apiKey: process.env.GROQ_API_KEY || ''
});

export async function POST(request: Request) {
  try {
    const { title, description, style, mood, theme, duration = 30 } = await request.json();

    const userKey = request.headers.get('x-forwarded-for') ?? 'anonymous';
    const inCheck = await moderateContent(`${title} ${description} ${style} ${mood} ${theme}`, 'prompt', userKey);
    if (!inCheck.isAppropriate) {
      return NextResponse.json({ error: inCheck.reason, blocked: inCheck.blocked ?? false }, { status: 400 });
    }

    const prompt = `You are an AI music composer. Generate full song lyrics for a composition titled "${title}", described as "${description}", in style "${style}"${mood ? ` with mood "${mood}"` : ''}${theme ? ` on theme "${theme}"` : ''}. Lyrics should fit within a ${duration}-second song performance. Respond with the lyrics only.`;
    const res = await llm.invoke(prompt);
    let lyrics = res.content as string;

    const outCheck = await moderateContent(lyrics, 'text');
    if (!outCheck.isAppropriate) {
      lyrics = "♩ ♪ ♫ Let's try a different song idea that's safe for everyone!";
    }

    return NextResponse.json({ lyrics });
  } catch (error) {
    console.error('Error generating lyrics:', error);
    return NextResponse.json({ error: 'Failed to generate lyrics' }, { status: 500 });
  }
} 