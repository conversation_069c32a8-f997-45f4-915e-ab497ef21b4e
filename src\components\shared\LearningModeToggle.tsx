import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Lightbulb } from "lucide-react";

interface LearningModeToggleProps {
    learningMode: boolean;
    toggleLearningMode: () => void;
}

const LearningModeToggle = ({
    learningMode,
    toggleLearningMode,
}: LearningModeToggleProps) => {
    return (
        <Button
            variant={learningMode ? "default" : "outline"}
            onClick={toggleLearningMode}
            className={`flex items-center gap-2 ${
                learningMode
                    ? "bg-black-200 text-purple-800 hover:bg-purple-700"
                    : ""
            }`}
        >
            <Lightbulb className="h-4 w-4" />
            Learning Mode {learningMode ? "On" : "Off"}
        </Button>
    );
};

export default LearningModeToggle;
