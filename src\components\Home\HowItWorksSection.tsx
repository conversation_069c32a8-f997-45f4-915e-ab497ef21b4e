import React, { useEffect, useRef } from "react";
import { fredoka, nunito } from "@/lib/fonts";
import { howItWorksSteps, howItWorksContent } from "@/lib/constants";

const HowItWorksSection = () => {
    // Ref for the cards container to observe for intersection
    const cardsRef = useRef(null);

    // Add scroll animation effect
    useEffect(() => {
        const observer = new IntersectionObserver(
            (entries) => {
                entries.forEach((entry) => {
                    // When cards container comes into view
                    if (entry.isIntersecting) {
                        // Get all step cards
                        const cards = document.querySelectorAll(".step-card");

                        // Add animation with staggered delay to each card
                        cards.forEach((card, index) => {
                            setTimeout(() => {
                                card.classList.add("animate-fade-in");
                                card.classList.remove("opacity-0");
                            }, index * 200); // 200ms delay between each card animation
                        });
                    }
                });
            },
            { threshold: 0.1 } // Trigger when at least 10% of the element is visible
        );

        // Copy ref value to variable to avoid stale closure in cleanup
        const currentCardsRef = cardsRef.current;

        // Start observing the cards container
        if (currentCardsRef) {
            observer.observe(currentCardsRef);
        }

        // Cleanup observer on component unmount
        return () => {
            if (currentCardsRef) {
                observer.unobserve(currentCardsRef);
            }
        };
    }, []);

    return (
        <section className="py-24 bg-gray-50" id="how-it-works">
            <div className="container mx-auto px-4">
                <div className="text-center mb-16 max-w-3xl mx-auto opacity-0 animate-fade-in-up [animation-delay:0.1s]">
                    <h2
                        className={`text-3xl md:text-4xl font-bold mb-5 text-gray-800 ${fredoka.className}`}
                    >
                        {howItWorksContent.title}
                    </h2>
                    <p className={`text-xl text-gray-600 ${nunito.className}`}>
                        {howItWorksContent.subtitle}
                    </p>
                </div>

                <div className="max-w-4xl mx-auto space-y-12" ref={cardsRef}>
                    {howItWorksSteps.map((step, index) => {
                        const StepIcon = step.icon;
                        return (
                            <div
                                key={step.title}
                                className="flex flex-col md:flex-row items-start gap-6 group step-card opacity-0 transform translate-y-8"
                            >
                                <div className="flex-shrink-0">
                                    <div className="relative">
                                        <div
                                            style={{
                                                boxShadow: `0 10px 25px -5px ${step.colorHex}30`,
                                            }}
                                            className="flex items-center justify-center h-20 w-20 rounded-2xl bg-white shadow-lg transition-transform duration-300 group-hover:scale-110 group-hover:rotate-3"
                                        >
                                            <StepIcon
                                                className="h-9 w-9"
                                                style={{ color: step.colorHex }}
                                            />
                                        </div>
                                        <div
                                            className={`absolute -top-3 -left-3 h-8 w-8 rounded-full bg-white shadow-md border-2 flex items-center justify-center text-sm font-bold ${fredoka.className}`}
                                            style={{
                                                borderColor: step.colorHex,
                                                color: step.colorHex,
                                            }}
                                        >
                                            {index + 1}
                                        </div>
                                    </div>
                                </div>
                                <div className="md:ml-4 flex-grow p-6 rounded-xl bg-gradient-to-r from-white to-gray-50 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300 hover:-translate-y-1">
                                    <h3
                                        className={`text-2xl font-semibold mb-2 text-gray-900 ${fredoka.className}`}
                                    >
                                        {step.title}
                                    </h3>
                                    <p
                                        className={`text-gray-600 ${nunito.className}`}
                                    >
                                        {step.description}
                                    </p>
                                </div>
                            </div>
                        );
                    })}
                </div>
            </div>
        </section>
    );
};

export default HowItWorksSection;
