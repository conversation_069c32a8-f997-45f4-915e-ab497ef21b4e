"use client";
import { useState } from 'react';
import { toast } from 'sonner';
import { saveUserContent } from '@/utils/contentStorage';
import { ArtStyleType } from './useArtStyle';

export const usePortfolioSave = () => {
  const [isSaving, setIsSaving] = useState(false);
  const [isSaved, setIsSaved] = useState(false);
  const [portfolioImage, setPortfolioImage] = useState<string | null>(null);
  
  const handleSaveToPortfolio = async (
    userId: string | undefined,
    generatedImage: string | null,
    title: string,
    prompt: string,
    artStyle: ArtStyleType,
    aspectRatio: string
  ) => {
    // Note: userId can be undefined - saveUser<PERSON>ontent will auto-detect authenticated user

    if (!generatedImage) {
      toast.error('Please create artwork before saving.');
      return;
    }

    if (isSaved) {
      toast.info('This artwork is already saved to your portfolio!');
      return;
    }

    setIsSaving(true);
    
    try {
      // Check if there's an active challenge
      let challengeId: string | undefined;
      try {
        const currentChallenge = localStorage.getItem('currentChallenge');
        if (currentChallenge) {
          const challenge = JSON.parse(currentChallenge);
          // Only link if the challenge type matches the content type
          if (challenge.type === 'art') {
            challengeId = challenge.id;
          }
        }
      } catch (error) {
        console.warn('Failed to parse current challenge from localStorage:', error);
      }

      const savedContent = await saveUserContent({
        user_id: userId, // Can be undefined - saveUserContent will auto-detect
        type: 'art',
        title: title || 'Untitled Artwork',
        content_metadata: {
          prompt,
          artStyle,
          aspectRatio
        },
        preview_url: generatedImage,
        challenge_id: challengeId
      });

      if (savedContent) {
        setPortfolioImage(generatedImage);
        setIsSaved(true);
        toast.success('Artwork saved to portfolio!');

        // Trigger event to update dashboard counters
        window.dispatchEvent(new CustomEvent('contentSaved', {
          detail: {
            type: 'art',
            title: title || 'Untitled Artwork'
          }
        }));
      } else {
        toast.error('Failed to save artwork. Please try again.');
      }
    } catch (error) {
      console.error('Error saving artwork:', error);
      toast.error('An unexpected error occurred while saving.');
    } finally {
      setIsSaving(false);
    }
  };

  const resetSaveState = () => {
    setIsSaved(false);
    setPortfolioImage(null);
  };

  return {
    handleSaveToPortfolio,
    isSaving,
    isSaved,
    portfolioImage,
    resetSaveState
  };
};
