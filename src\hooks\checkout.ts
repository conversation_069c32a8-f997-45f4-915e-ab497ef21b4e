import { useState } from 'react';
import { useRouter } from 'next/navigation';

// interface Plan {
//   planId: string;
//   name: string;
//   price: string;
//   billingInfo: string;
//   savings?: string;
//   popular?: boolean;
// }

export const useCheckout = () => {
  const router = useRouter();
  const [selectedPlanId, setSelectedPlanId] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Sample plans data
  const plans = [
    {
      planId: 'monthly-tier',
      name: 'Monthly',
      price: '$14.99',
      billingInfo: 'Monthly',
    },
    {
      planId: 'quarterly-tier',
      name: 'Quarterly',
      price: '$11.99',
      billingInfo: 'Every Three Months',
      savings: 'Save 20%',
      popular: true,
    },
    {
      planId: 'annual-tier',
      name: 'Annual',
      price: '$9.99',
      billingInfo: 'Annually',
      savings: 'Save 33% - Best Value',
    },
  ];

  const handlePlanChange = (planId: string | null) => {
            // Plan changed to: ${planId}
    setSelectedPlanId(planId);
  };

  return {
    selectedPlanId,
    isSubmitting,
    setIsSubmitting,
    plans,
    handlePlanChange,
    router
  };
}; 