import { NextRequest, NextResponse } from 'next/server';
import { stripe } from '@/lib/stripe';
import { prisma } from '@/lib/prisma';

export async function POST(request: NextRequest) {
  try {
    const { userId } = await request.json();
    console.log('Creating billing portal for user:', userId);
    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    // Fetch user profile to get Stripe customer ID
    const profile = await prisma.profile.findUnique({ where: { id: userId } });
    console.log('Profile fetched for billing portal:', profile);
    if (!profile?.stripe_customer_id) {
      return NextResponse.json({ error: 'Stripe customer not found' }, { status: 404 });
    }

    // Create a Stripe Billing Portal session
    if (!stripe) {
      console.error('Stripe client not configured');
      return NextResponse.json({ error: 'Stripe not configured' }, { status: 500 });
    }
    
    console.log('Creating billing portal session for customer:', profile.stripe_customer_id);
    const session = await stripe.billingPortal.sessions.create({
      customer: profile.stripe_customer_id,
      return_url: `${process.env.NEXT_PUBLIC_SITE_URL || ''}/dashboard`,
    });

    console.log('Billing portal session created:', session.url);
    return NextResponse.json({ url: session.url });
  } catch (error) {
    console.error('Error creating billing portal session:', error);
    const message = error && typeof error === 'object' && 'message' in error ? error.message : String(error);
    return NextResponse.json(
      { error: `Failed to create billing portal session: ${message}` }, { status: 500 }
    );
  }
} 