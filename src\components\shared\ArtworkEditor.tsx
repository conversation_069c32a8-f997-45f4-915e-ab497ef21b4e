import React, { useState, useRef, useEffect } from "react";

interface ArtworkEditorProps {
    initialImage: string;
    onSave: (editedImageUrl: string) => void;
}

const ArtworkEditor = ({ initialImage, onSave }: ArtworkEditorProps) => {
    const [, setImage] = useState<string>(initialImage);
    const [isDrawing, setIsDrawing] = useState(false);
    const [brushColor, setBrushColor] = useState("#000000");
    const [brushSize, setBrushSize] = useState(5);
    const canvasRef = useRef<HTMLCanvasElement>(null);
    const contextRef = useRef<CanvasRenderingContext2D | null>(null);

    // Initialize canvas when component mounts or image changes
    useEffect(() => {
        const canvas = canvasRef.current;
        if (!canvas) return;

        // Set canvas dimensions
        canvas.width = 800;
        canvas.height = 600;

        const context = canvas.getContext("2d");
        if (!context) return;

        // Configure context
        context.lineCap = "round";
        context.lineJoin = "round";
        context.strokeStyle = brushColor;
        context.lineWidth = brushSize;
        contextRef.current = context;

        // Load the initial image
        const img = new Image();
        img.src = initialImage;
        img.onload = () => {
            context.drawImage(img, 0, 0, canvas.width, canvas.height);
        };
        img.onerror = () => {
            // Handle error - fill with a light gray
            context.fillStyle = "#f3f4f6";
            context.fillRect(0, 0, canvas.width, canvas.height);
            context.fillStyle = "#d1d5db";
            context.font = "20px Arial";
            context.fillText("Image could not be loaded", 10, 50);
        };
    }, [initialImage, brushColor, brushSize]);

    // Update brush color and size
    useEffect(() => {
        if (contextRef.current) {
            contextRef.current.strokeStyle = brushColor;
            contextRef.current.lineWidth = brushSize;
        }
    }, [brushColor, brushSize]);

    const startDrawing = (e: React.MouseEvent<HTMLCanvasElement>) => {
        const canvas = canvasRef.current;
        if (!canvas || !contextRef.current) return;

        const rect = canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;

        contextRef.current.beginPath();
        contextRef.current.moveTo(x, y);
        setIsDrawing(true);
    };

    const draw = (e: React.MouseEvent<HTMLCanvasElement>) => {
        if (!isDrawing || !contextRef.current || !canvasRef.current) return;

        const rect = canvasRef.current.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;

        contextRef.current.lineTo(x, y);
        contextRef.current.stroke();
    };

    const stopDrawing = () => {
        if (!contextRef.current) return;

        contextRef.current.closePath();
        setIsDrawing(false);

        // Update the image state with the latest canvas data
        if (canvasRef.current) {
            setImage(canvasRef.current.toDataURL());
        }
    };

    const handleSave = () => {
        if (canvasRef.current) {
            const editedImageUrl = canvasRef.current.toDataURL();
            onSave(editedImageUrl);
        }
    };

    const handleClearCanvas = () => {
        if (!contextRef.current || !canvasRef.current) return;

        const canvas = canvasRef.current;
        const context = contextRef.current;

        context.clearRect(0, 0, canvas.width, canvas.height);

        // Re-draw the original image
        const img = new Image();
        img.src = initialImage;
        img.onload = () => {
            context.drawImage(img, 0, 0, canvas.width, canvas.height);

            // Update the image state
            setImage(canvas.toDataURL());
        };
    };

    return (
        <div className="artwork-editor">
            <div className="mb-4 flex items-center gap-4">
                <div>
                    <label htmlFor="brush-color" className="block text-sm mb-1">
                        Brush Color
                    </label>
                    <input
                        id="brush-color"
                        type="color"
                        value={brushColor}
                        onChange={(e) => setBrushColor(e.target.value)}
                        className="h-8 w-12"
                    />
                </div>
                <div>
                    <label htmlFor="brush-size" className="block text-sm mb-1">
                        Brush Size: {brushSize}px
                    </label>
                    <input
                        id="brush-size"
                        type="range"
                        min="1"
                        max="50"
                        value={brushSize}
                        onChange={(e) => setBrushSize(parseInt(e.target.value))}
                        className="w-36"
                    />
                </div>
                <button
                    onClick={handleClearCanvas}
                    className="ml-auto px-3 py-1 bg-gray-200 hover:bg-gray-300 rounded text-sm"
                >
                    Reset
                </button>
            </div>

            <div className="border rounded overflow-hidden">
                <canvas
                    ref={canvasRef}
                    onMouseDown={startDrawing}
                    onMouseMove={draw}
                    onMouseUp={stopDrawing}
                    onMouseLeave={stopDrawing}
                    className="w-full h-auto cursor-crosshair bg-white"
                    style={{ maxHeight: "500px" }}
                />
            </div>

            <div className="mt-4 flex justify-end">
                <button
                    onClick={handleSave}
                    className="px-4 py-2 bg-spark-blue text-white rounded hover:bg-spark-blue/90"
                >
                    Save Changes
                </button>
            </div>
        </div>
    );
};

export default ArtworkEditor;
