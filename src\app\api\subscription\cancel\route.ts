import { NextRequest, NextResponse } from 'next/server';
import { SubscriptionService } from '@/lib/services/subscriptionService';

// POST /api/subscription/cancel - Cancel user subscription
export async function POST(request: NextRequest) {
  try {
    const { userId, immediate = false } = await request.json();

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    try {
      const cancelResult = await SubscriptionService.cancelSubscription(userId, immediate);

      if (!cancelResult) {
        return NextResponse.json(
          { error: 'No active subscription found or failed to cancel' },
          { status: 400 }
        );
      }

      // Fetch updated subscription details
      const userSubscription = await SubscriptionService.getUserSubscription(userId);

      return NextResponse.json({
        success: true,
        subscription: userSubscription?.subscription || null
      });
    } catch (error) {
      console.error('Subscription cancellation error:', error);
      return NextResponse.json(
        { error: 'Failed to cancel subscription', details: String(error) },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Unexpected error in subscription cancellation:', error);
    return NextResponse.json(
      { error: 'Unexpected error occurred' },
      { status: 500 }
    );
  }
} 