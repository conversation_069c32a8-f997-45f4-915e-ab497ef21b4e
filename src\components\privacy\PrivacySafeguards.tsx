import React from "react";
import { Server, Lock, Ban, FileDown } from "lucide-react";
import { fredoka } from "@/lib/fonts";
import PrivacyFeatureCard from "./PrivacyFeatureCard";

const PrivacySafeguards = () => {
    return (
        <div className="text-gray-900">
            <h2 className={`text-2xl font-bold mb-6 text-center ${fredoka.className}`}>
                Technical Privacy Safeguards
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-12">
                <PrivacyFeatureCard
                    icon={
                        <Server className="h-8 w-8 text-littlespark-lavender" />
                    }
                    title="Secure AI Processing"
                    description="AI processing occurs through secure, encrypted connections to OpenAI and Runware. We use industry-standard HTTPS and WebSocket encryption to protect your data in transit."
                />

                <PrivacyFeatureCard
                    icon={<Lock className="h-8 w-8 text-littlespark-blue" />}
                    title="Minimal Data Transmission"
                    description="We only send the specific content needed to generate creative experiences. No additional personal information is shared with our AI service providers."
                />

                <PrivacyFeatureCard
                    icon={<Ban className="h-8 w-8 text-red-500" />}
                    title="No Persistent Data Storage"
                    description="Generated content is not stored permanently. Temporary processing data is immediately discarded after your creative session."
                />

                <PrivacyFeatureCard
                    icon={
                        <FileDown className="h-8 w-8 text-littlespark-orange" />
                    }
                    title="User-Controlled Downloads"
                    description="All generated content can be immediately downloaded to your device, giving you full control and the option to delete server-side temporary data."
                />
            </div>
        </div>
    );
};

export default PrivacySafeguards;
