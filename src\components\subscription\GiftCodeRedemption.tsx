import React, { useState, useEffect } from "react";
import { But<PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { useToast } from "../../hooks/use-toast";
import { useSubscription } from "../../hooks/subscription";
import { AlertCircle, CheckCircle2 } from "lucide-react";
import { Alert, AlertDescription } from "../ui/alert";
import { fredoka } from "@/lib/fonts";

const GiftCodeRedemption = () => {
    const { toast } = useToast();
    const { redeemGiftCode } = useSubscription();
    const [giftCode, setGiftCode] = useState("");
    const [email, setEmail] = useState("");
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [redeemResult, setRedeemResult] = useState<{
        success: boolean;
        message: string;
    } | null>(null);
    const [isEmailValid, setIsEmailValid] = useState<boolean | null>(null);

    // Email validation function
    const validateEmail = (email: string): boolean => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    };

    // Validate email on change
    useEffect(() => {
        if (email) {
            setIsEmailValid(validateEmail(email));
        } else {
            setIsEmailValid(null);
        }
    }, [email]);

    const handleRedeemCode = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!giftCode.trim()) {
            toast({
                title: "Missing Code",
                description: "Please enter a gift code",
                variant: "destructive",
            });
            return;
        }

        if (!isEmailValid) {
            toast({
                title: "Invalid Email",
                description: "Please enter a valid email address",
                variant: "destructive",
            });
            return;
        }

        setIsSubmitting(true);
        setRedeemResult(null);

        try {
            const result = await redeemGiftCode(
                giftCode.toUpperCase().trim(),
                email
            );
            if (result.success) {
                setRedeemResult({ success: true, message: result.message });
                setGiftCode("");
                setEmail("");
            } else {
                setRedeemResult({ success: false, message: result.message });
            }
        } catch (error: unknown) {
            const errorMessage =
                error instanceof Error
                    ? error.message
                    : "An error occurred while redeeming your code";
            setRedeemResult({ success: false, message: errorMessage });
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <div className="border rounded-lg p-6 bg-white shadow-sm flex flex-col items-center justify-center">
            <h3
                className={`${fredoka.className} font-[900] text-lg text-gray-900 mb-2`}
            >
                Redeem a Gift Subscription
            </h3>
            <p className="text-sm text-gray-600 mb-4">
                Enter your gift code and email to activate your subscription
            </p>

            <form
                onSubmit={handleRedeemCode}
                className="space-y-4 w-full flex flex-col items-center justify-center"
            >
                <div className="w-full">
                    <label
                        htmlFor="gift-code"
                        className="block text-sm text-center font-medium mb-1 text-gray-900"
                    >
                        Gift Code
                    </label>
                    <Input
                        id="gift-code"
                        value={giftCode}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                            setGiftCode(e.target.value)
                        }
                        placeholder="Enter your gift code"
                        className="w-full rounded-full"
                    />
                </div>

                <div className="w-full">
                    <label
                        htmlFor="email"
                        className="block text-sm text-center font-medium mb-1 text-gray-900"
                    >
                        Email Address
                    </label>
                    <Input
                        id="email"
                        type="email"
                        value={email}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                            setEmail(e.target.value)
                        }
                        placeholder="Your email address"
                        className="w-full rounded-full"
                    />
                    {isEmailValid === false && (
                        <p className="text-xs text-red-500 mt-1">
                            Please enter a valid email address
                        </p>
                    )}
                </div>

                <Button
                    type="submit"
                    className="w-fit  bg-littlespark-teal hover:bg-littlespark-teal/90"
                    disabled={
                        isSubmitting || (email.length > 0 && !isEmailValid)
                    }
                >
                    {isSubmitting ? "Redeeming..." : "Redeem Code"}
                </Button>
            </form>

            {redeemResult && (
                <Alert
                    variant={redeemResult.success ? "default" : "destructive"}
                    className="mt-4"
                >
                    {redeemResult.success ? (
                        <CheckCircle2 className="h-4 w-4" />
                    ) : (
                        <AlertCircle className="h-4 w-4" />
                    )}
                    <AlertDescription>{redeemResult.message}</AlertDescription>
                </Alert>
            )}
        </div>
    );
};

export default GiftCodeRedemption;
