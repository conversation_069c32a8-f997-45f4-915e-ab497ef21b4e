import React from "react";
import { Info, Lightbulb, Brain } from "lucide-react";
import { fredoka } from "@/lib/fonts";

interface LearnTipProps {
    tip: string;
    subject?: string;
    type?: "learning" | "creative" | "safety";
}

const LearnTip = ({ tip, subject, type = "learning" }: LearnTipProps) => {
    // Define color and icon based on type with safe fallbacks
    const colors = {
        learning: {
            bgColor: "bg-blue-50",
            textColor: "text-blue-700",
            borderColor: "border-blue-200",
            iconColor: "text-blue-500",
        },
        creative: {
            bgColor: "bg-purple-50",
            textColor: "text-purple-700",
            borderColor: "border-purple-200",
            iconColor: "text-purple-500",
        },
        safety: {
            bgColor: "bg-green-50",
            textColor: "text-green-700",
            borderColor: "border-green-200",
            iconColor: "text-green-500",
        },
    };

    // Use default colors for learning if the type is invalid
    const colorSet = colors[type] || colors.learning;

    // Select icon based on type
    const IconComponent =
        type === "creative" ? Lightbulb : type === "safety" ? Info : Brain;

    return (
        <div
            className={`p-3 rounded-lg border flex items-start gap-3 ${colorSet.bgColor} ${colorSet.borderColor}`}
        >
            <div
                className={`p-1.5 rounded-full bg-white ${colorSet.iconColor}`}
            >
                <IconComponent className="h-4 w-4" />
            </div>
            <div>
                {subject && (
                    <h4
                        className={`text-sm font-medium mb-0.5 ${fredoka.className} ${colorSet.textColor}`}
                    >
                        {subject}
                    </h4>
                )}
                <p className="text-sm text-gray-900 font-light">{tip}</p>
            </div>
        </div>
    );
};

export default LearnTip;
