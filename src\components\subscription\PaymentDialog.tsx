import React, { useState } from "react";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import PaymentForm from "@/components/subscription/PaymentForm";
import { SubscriptionPlan } from "@/components/subscription/SubscriptionPlans";
import {
    PaymentFormValues,
    GiftFormValues,
} from "./payment-form/PaymentFormSchema";

interface PaymentDialogProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    plan: SubscriptionPlan | null;
    isGift: boolean;
    giftData: {
        recipientEmail: string;
        setRecipientEmail: (email: string) => void;
        senderName: string;
        setSenderName: (name: string) => void;
        giftMessage: string;
        setGiftMessage: (message: string) => void;
    };
    onSubmit: (values: PaymentFormValues | GiftFormValues, promotionalData?: {
        code: string;
        originalAmount: number;
        discountAmount: number;
        finalAmount: number;
    }) => void;
    isProcessing?: boolean;
}

const PaymentDialog: React.FC<PaymentDialogProps> = ({
    open,
    onOpenChange,
    plan,
    isGift,
    giftData,
    onSubmit,
    isProcessing = false,
}) => {
    const {
        recipientEmail,
        setRecipientEmail,
        senderName,
        setSenderName,
        giftMessage,
        setGiftMessage,
    } = giftData;

    // Promotional code state
    const [promotionalCode, setPromotionalCode] = useState("");
    const [promotionalData, setPromotionalData] = useState<{
        valid: boolean;
        originalAmount: number;
        discountAmount: number;
        finalAmount: number;
        description?: string;
    } | null>(null);
    const [isValidatingCode, setIsValidatingCode] = useState(false);

    // Validate promotional code
    const validatePromotionalCode = async (code: string) => {
        if (!code.trim() || !plan) return;

        setIsValidatingCode(true);
        try {
            const response = await fetch('/api/promotional-codes/validate', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ code: code.trim(), planId: plan.planId }),
            });

            const data = await response.json();

            if (data.valid) {
                setPromotionalData({
                    valid: true,
                    originalAmount: data.original_amount,
                    discountAmount: data.discount_amount,
                    finalAmount: data.final_amount,
                    description: data.description,
                });
            } else {
                setPromotionalData(null);
                // You might want to show an error message here
            }
        } catch (error) {
            console.error('Error validating promotional code:', error);
            setPromotionalData(null);
        } finally {
            setIsValidatingCode(false);
        }
    };

    // Handle promotional code input
    const handlePromotionalCodeChange = (code: string) => {
        setPromotionalCode(code);
        if (code.trim().length >= 3) {
            validatePromotionalCode(code);
        } else {
            setPromotionalData(null);
        }
    };

    // Handle form submission with promotional data
    const handleSubmit = (values: PaymentFormValues | GiftFormValues) => {
        const promoData = promotionalData?.valid ? {
            code: promotionalCode,
            originalAmount: promotionalData.originalAmount,
            discountAmount: promotionalData.discountAmount,
            finalAmount: promotionalData.finalAmount,
        } : undefined;

        onSubmit(values, promoData);
    };

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="max-w-sm sm:max-w-[425px] mx-4">
                <DialogHeader>
                    <DialogTitle className="font-quicksand">
                        {isGift
                            ? "Gift a Subscription"
                            : `Subscribe to ${plan?.name}`}
                    </DialogTitle>
                    <DialogDescription>
                        {isGift
                            ? "Complete your payment information to gift this subscription."
                            : "Complete your payment information to start your subscription. Enter promotional code for discounts."}
                    </DialogDescription>
                </DialogHeader>
                <PaymentForm
                    plan={plan}
                    onSubmit={handleSubmit}
                    isGift={isGift}
                    recipientEmail={recipientEmail}
                    setRecipientEmail={setRecipientEmail}
                    senderName={senderName}
                    setSenderName={setSenderName}
                    giftMessage={giftMessage}
                    setGiftMessage={setGiftMessage}
                    isProcessing={isProcessing}
                    promotionalCode={promotionalCode}
                    onPromotionalCodeChange={handlePromotionalCodeChange}
                    promotionalData={promotionalData}
                    isValidatingCode={isValidatingCode}
                />
            </DialogContent>
        </Dialog>
    );
};

export default PaymentDialog;
