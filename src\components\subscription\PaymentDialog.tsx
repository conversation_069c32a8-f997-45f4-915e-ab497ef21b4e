import React from "react";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import PaymentForm from "@/components/subscription/PaymentForm";
import { SubscriptionPlan } from "@/components/subscription/SubscriptionPlans";
import {
    PaymentFormValues,
    GiftFormValues,
} from "./payment-form/PaymentFormSchema";

interface PaymentDialogProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    plan: SubscriptionPlan | null;
    isGift: boolean;
    giftData: {
        recipientEmail: string;
        setRecipientEmail: (email: string) => void;
        senderName: string;
        setSenderName: (name: string) => void;
        giftMessage: string;
        setGiftMessage: (message: string) => void;
    };
    onSubmit: (values: PaymentFormValues | GiftFormValues) => void;
    isProcessing?: boolean;
}

const PaymentDialog: React.FC<PaymentDialogProps> = ({
    open,
    onOpenChange,
    plan,
    isGift,
    giftData,
    onSubmit,
    isProcessing = false,
}) => {
    const {
        recipientEmail,
        setRecipientEmail,
        senderName,
        setSenderName,
        giftMessage,
        setGiftMessage,
    } = giftData;

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="max-w-sm sm:max-w-[425px] mx-4">
                <DialogHeader>
                    <DialogTitle className="font-quicksand">
                        {isGift
                            ? "Gift a Subscription"
                            : `Subscribe to ${plan?.name}`}
                    </DialogTitle>
                    <DialogDescription>
                        {isGift
                            ? "Complete your payment information to gift this subscription."
                            : "Complete your payment information to start your subscription immediately."}
                    </DialogDescription>
                </DialogHeader>
                <PaymentForm
                    plan={plan}
                    onSubmit={onSubmit}
                    isGift={isGift}
                    recipientEmail={recipientEmail}
                    setRecipientEmail={setRecipientEmail}
                    senderName={senderName}
                    setSenderName={setSenderName}
                    giftMessage={giftMessage}
                    setGiftMessage={setGiftMessage}
                    isProcessing={isProcessing}
                />
            </DialogContent>
        </Dialog>
    );
};

export default PaymentDialog;
