import React from "react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/subscription-accordion";

const SubscriptionFAQ = () => {
  return (
    <div className="max-w-3xl mx-auto mt-24 mb-16">
      <h2 className="text-3xl font-quicksand font-bold text-center mb-10">Frequently Asked Questions</h2>
      
      <Accordion type="single" collapsible className="w-full">
        <AccordionItem value="item-1">
          <AccordionTrigger>How do I cancel my subscription?</AccordionTrigger>
          <AccordionContent>
            You can cancel your subscription at any time from your account settings. 
            Once canceled, your subscription will remain active until the end of your current billing period.
            We recommend downloading any content you&apos;d like to keep before your subscription ends.
          </AccordionContent>
        </AccordionItem>
        
        <AccordionItem value="item-2">
          <AccordionTrigger>What happens when my subscription ends?</AccordionTrigger>
          <AccordionContent>
            When your subscription ends, you&apos;ll lose access to all premium features and any content created 
            with your Little Spark account. We recommend downloading any projects you want to keep before 
            your subscription expires. You can restore full access at any time by reactivating your subscription.
          </AccordionContent>
        </AccordionItem>
        
        <AccordionItem value="item-3">
          <AccordionTrigger>Can I switch between subscription plans?</AccordionTrigger>
          <AccordionContent>
            Yes, you can change your subscription plan at any time from your account settings. 
            If you upgrade, the change will take effect immediately and you&apos;ll be charged the prorated 
            difference. If you downgrade, the change will take effect at the end of your current billing cycle.
          </AccordionContent>
        </AccordionItem>
        
        <AccordionItem value="item-4">
          <AccordionTrigger>How do gift subscriptions work?</AccordionTrigger>
          <AccordionContent>
            When you purchase a gift subscription, the recipient will receive an email with a redemption 
            code and instructions on how to activate their subscription. You can choose to send this email 
            immediately or schedule it for a future date. The recipient will have full access to all premium 
            features for the duration of the gift subscription.
          </AccordionContent>
        </AccordionItem>
        
        <AccordionItem value="item-5">
          <AccordionTrigger>How does the free trial work?</AccordionTrigger>
          <AccordionContent>
            All new subscriptions include a 3-day free trial. Your credit card is required to start the trial,
            but you won&apos;t be charged until the trial period ends. You can cancel at any time during the trial
            period and you won&apos;t be charged. After the trial period ends, your selected payment method 
            will be automatically charged for the subscription plan you chose.
          </AccordionContent>
        </AccordionItem>
        
        <AccordionItem value="item-6">
          <AccordionTrigger>Can I use Little Spark on multiple devices?</AccordionTrigger>
          <AccordionContent>
            Yes, you can access your Little Spark account on any device with a web browser. Your creations 
            and settings will sync across all your devices as long as you&apos;re logged into the same account.
          </AccordionContent>
        </AccordionItem>
        
        <AccordionItem value="item-7">
          <AccordionTrigger>Are there any geographic restrictions for subscriptions?</AccordionTrigger>
          <AccordionContent>
            Little Spark is available worldwide. All prices are in USD and will be converted to your local currency 
            at the current exchange rate. You&apos;ll always see the total amount in your local currency before completing 
            your purchase.
          </AccordionContent>
        </AccordionItem>
        
        <AccordionItem value="item-8">
          <AccordionTrigger>What happens to my content if I decide to resubscribe later?</AccordionTrigger>
          <AccordionContent>
            If you reactivate your subscription after it has expired, you&apos;ll regain access to all premium features 
            immediately. Your previously created content will still be available in your account, as we retain this 
            data for a period of time after subscription expiration. However, for long-term reliability, we still 
            recommend downloading important projects before your subscription ends.
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
};

export default SubscriptionFAQ; 