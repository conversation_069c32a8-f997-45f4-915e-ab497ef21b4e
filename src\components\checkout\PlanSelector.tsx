import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Check, Flame } from "lucide-react";
import { fredoka } from "@/lib/fonts";

interface PlanSelectorProps {
    plans: Array<{
        planId: string;
        name: string;
        price: string;
        popular?: boolean;
        savings?: string;
        billingInfo?: string;
    }>;
    selectedPlanId: string | null;
    setSelectedPlanId: (id: string | null) => void;
}

const PlanSelector = ({
    plans,
    selectedPlanId,
    setSelectedPlanId,
}: PlanSelectorProps) => {
    // Handler function to set the selected plan
    const handlePlanSelect = (planId: string) => {
        console.log("Plan selected:", planId);
        setSelectedPlanId(planId);
    };

    return (
        <div>
            <h2
                className={`text-2xl font-bold mb-4 ${fredoka.className} text-gray-900 text-center`}
            >
                Choose your plan
            </h2>

            <div className="space-y-4">
                {plans.map((plan) => (
                    <div
                        key={plan.planId}
                        className="cursor-pointer"
                        onClick={() => handlePlanSelect(plan.planId)}
                    >
                        <Card
                            className={`transition-all duration-200 relative ${
                                selectedPlanId === plan.planId
                                    ? "border-4 border-littlespark-primary shadow-lg bg-littlespark-primary/10 transform scale-[1.02]"
                                    : "border-2 border-gray-200 hover:border-littlespark-primary/50 hover:shadow-md hover:transform hover:scale-[1.01]"
                            }`}
                        >
                            {plan.popular && (
                                <div className="absolute -top-4 left-0 w-full flex justify-center">
                                    <div className="bg-green-500 text-white px-6 py-2 rounded-full flex items-center gap-2 text-sm font-bold">
                                        Most Popular{" "}
                                        <Flame className="h-4 w-4" />
                                    </div>
                                </div>
                            )}

                            <CardContent
                                className={`p-6 ${plan.popular ? "pt-8" : ""}`}
                            >
                                <div className="w-full">
                                    {plan.billingInfo && (
                                        <p className="text-lg text-gray-900 font-semibold mb-1 text-left">
                                            {plan.billingInfo.includes(
                                                "Monthly"
                                            ) ? (
                                                "Billed Monthly"
                                            ) : plan.billingInfo.includes(
                                                  "Three"
                                              ) ? (
                                                <span>
                                                    Billed Every Three Months{" "}
                                                    <span className="line-through text-gray-400">
                                                        $44.97
                                                    </span>{" "}
                                                    <span className="text-red-600 font-bold">
                                                        $35.97
                                                    </span>
                                                </span>
                                            ) : (
                                                <span>
                                                    Billed Annually{" "}
                                                    <span className="line-through text-gray-400">
                                                        $179.88
                                                    </span>{" "}
                                                    <span className="text-red-600 font-bold">
                                                        $119.99
                                                    </span>
                                                </span>
                                            )}
                                        </p>
                                    )}

                                    <div className="flex items-baseline mt-1">
                                        <span className="text-3xl font-bold text-[#9b87f5]">
                                            ${plan.price.replace("$", "")}
                                        </span>
                                        <span className="text-lg text-[#9b87f5]/70 ml-2">
                                            / month
                                        </span>
                                    </div>

                                    {plan.savings && (
                                        <p className="text-sm font-medium text-red-600 mt-2 text-left">
                                            {plan.savings}
                                        </p>
                                    )}

                                    {/* Trial period badge */}
                                    <div className="mt-3 inline-block bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-xs font-medium">
                                        7-Day Free Trial
                                    </div>
                                </div>

                                {selectedPlanId === plan.planId && (
                                    <div className="absolute right-6 top-1/2 -translate-y-1/2 bg-littlespark-primary text-white p-3 rounded-full shadow-lg border-2 border-white">
                                        <Check className="h-6 w-6 font-bold" />
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>
                ))}
            </div>

            <div className="mt-6 bg-blue-50 p-4 rounded-lg border border-blue-200">
                <div className="flex items-center gap-2 mb-2">
                    <span className="text-blue-600 text-lg">🎉</span>
                    <h3 className="text-sm font-semibold text-blue-900">
                        Premium Features Included
                    </h3>
                </div>
                <p className="text-sm text-blue-800">
                    Get instant access to all premium features including unlimited creativity tools,
                    AI assistance, and content creation. Cancel anytime.
                </p>
            </div>
        </div>
    );
};

export default PlanSelector;
