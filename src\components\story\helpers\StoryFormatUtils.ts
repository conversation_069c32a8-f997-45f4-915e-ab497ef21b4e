type StoryFormat = 'adventure' | 'mystery' | 'comedy' | 'scifi';

const getReadingLevel = (age: number): string => {
  if (age <= 6) return 'simple';
  if (age <= 9) return 'basic';
  if (age <= 12) return 'intermediate';
  return 'advanced';
};

const getContentGuidance = (age: number): string => {
  if (age <= 6) return 'Keep the content gentle and positive.';
  if (age <= 9) return 'Include mild challenges that are resolved happily.';
  if (age <= 12) return 'Include more complex challenges and character growth.';
  return 'Include deeper themes and character development.';
};

const getVocabularyGuidance = (age: number): string => {
  if (age <= 6) return 'simple, everyday words';
  if (age <= 9) return 'grade-appropriate vocabulary with occasional new words';
  if (age <= 12) return 'varied vocabulary with some challenging words';
  return 'rich, diverse vocabulary';
};

const getThemeGuidance = (age: number): string => {
  if (age <= 6) return 'Focus on friendship, family, and simple life lessons.';
  if (age <= 9) return 'Explore themes of courage, teamwork, and personal growth.';
  if (age <= 12) return 'Include themes of identity, responsibility, and overcoming challenges.';
  return 'Explore complex themes of self-discovery, relationships, and personal values.';
};

export const getFormatGuideline = (format: StoryFormat, readerAge: number): string => {
  const readingLevel = getReadingLevel(readerAge);
  const contentGuidance = getContentGuidance(readerAge);
  const vocabularyGuidance = getVocabularyGuidance(readerAge);
  const themeGuidance = getThemeGuidance(readerAge);

  switch (format) {
    case 'adventure':
      return `Make this an ADVENTURE story with age-appropriate excitement, challenges, exploration, and courage for a ${readerAge}-year-old. Write at a ${readingLevel} reading level. ${contentGuidance} Use ${vocabularyGuidance}. ${themeGuidance}`;
    case 'mystery':
      return `Make this a MYSTERY story with age-appropriate clues, investigation, secrets, and surprising revelations for a ${readerAge}-year-old. Write at a ${readingLevel} reading level. ${contentGuidance} Use ${vocabularyGuidance}. ${themeGuidance}`;
    case 'comedy':
      return `Make this a COMEDY story with age-appropriate humor, funny situations, jokes, and amusing characters for a ${readerAge}-year-old. Write at a ${readingLevel} reading level. ${contentGuidance} Use ${vocabularyGuidance}. ${themeGuidance}`;
    case 'scifi':
      return `Make this a SCIENCE FICTION story with age-appropriate technology, space travel, friendly aliens, robots, or future worlds for a ${readerAge}-year-old. Write at a ${readingLevel} reading level. ${contentGuidance} Use ${vocabularyGuidance}. ${themeGuidance}`;
    default:
      return `Make this a balanced story with engaging narrative elements appropriate for a ${readerAge}-year-old. Write at a ${readingLevel} reading level. ${contentGuidance} Use ${vocabularyGuidance}. ${themeGuidance}`;
  }
}; 