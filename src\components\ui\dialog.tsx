"use client";
import React, { useEffect } from "react";
// import { X } from "lucide-react";

interface DialogProps {
    children: React.ReactNode;
    open: boolean;
    onOpenChange: (open: boolean) => void;
}

interface DialogContentProps {
    children: React.ReactNode;
    className?: string;
}

interface DialogHeaderProps {
    children: React.ReactNode;
    className?: string;
}

interface DialogTitleProps {
    children: React.ReactNode;
    className?: string;
}

interface DialogDescriptionProps {
    children: React.ReactNode;
    className?: string;
}

export const Dialog: React.FC<DialogProps> = ({
    children,
    open,
    onOpenChange,
}) => {
    useEffect(() => {
        const handleEscape = (e: KeyboardEvent) => {
            if (e.key === "Escape") {
                onOpenChange(false);
            }
        };

        if (open) {
            document.addEventListener("keydown", handleEscape);
            document.body.style.overflow = "hidden";
        }

        return () => {
            document.removeEventListener("keydown", handleEscape);
            document.body.style.overflow = "unset";
        };
    }, [open, onOpenChange]);

    if (!open) return null;

    return (
        <div 
            className="fixed inset-0 z-50 flex items-center justify-center p-4"
            onClick={() => onOpenChange(false)}
        >
            {/* Backdrop */}
            <div className="fixed inset-0 bg-black/20" />
            {/* Content */}
            <div className="relative z-50 w-full max-h-[95vh] overflow-auto">
                {children}
            </div>
        </div>
    );
};

export const DialogContent: React.FC<DialogContentProps> = ({
    children,
    className = "",
}) => {
    return (
        <div
            className={`relative bg-white rounded-lg shadow-xl max-w-md w-full mx-auto p-6 ${className}`}
            onClick={(e) => e.stopPropagation()}
        >
            {children}
        </div>
    );
};

export const DialogHeader: React.FC<DialogHeaderProps> = ({
    children,
    className = "",
}) => {
    return <div className={`mb-4 ${className}`}>{children}</div>;
};

export const DialogTitle: React.FC<DialogTitleProps> = ({
    children,
    className = "",
}) => {
    return (
        <h2 className={`text-lg font-semibold text-gray-900 ${className}`}>
            {children}
        </h2>
    );
};

export const DialogDescription: React.FC<DialogDescriptionProps> = ({
    children,
    className = "",
}) => {
    return (
        <p className={`text-sm text-gray-600 mt-1 ${className}`}>{children}</p>
    );
};
