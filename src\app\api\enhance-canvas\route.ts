import { NextRequest, NextResponse } from 'next/server';
import Replicate from 'replicate';

export async function POST(req: NextRequest) {
  const { imageData, prompt } = await req.json();
  console.log('🔥 ENHANCE API: Received request, prompt=', prompt);
  
  if (!imageData) {
    return NextResponse.json({ error: 'Image data is required' }, { status: 400 });
  }

  const subject = prompt ? prompt : 'the artwork';

  const replicate = new Replicate({ auth: process.env.REPLICATE_API_TOKEN });
  
  // Enhance the canvas image with AI coloring into photorealistic result
  const input = {
    image: imageData,
    prompt: `photorealistic full color rendering of this ${subject}, maintain composition and structure, add realistic textures, natural lighting, vibrant and balanced colors, detailed finish`,
    negative_prompt: "blurry, low quality, distorted, messy, ugly, bad anatomy, deformed, artifacts, pixelated, oversaturated",
    strength: 0.8,
    guidance_scale: 8,
    num_inference_steps: 25,
    scheduler: "DPMSolverMultistep"
  };

  console.log('🔥 ENHANCE API: Input to Replicate:', input);

  try {
    const output = await replicate.run(
      'stability-ai/stable-diffusion:ac732df83cea7fff18b8472768c88ad041fa750ff7682a21affe81863cbe77e4',
      { input }
    ) as string[];

    console.log('🔥 ENHANCE API: Replicate output:', output);

    if (output && Array.isArray(output) && output.length > 0) {
      const imageResult = output[0];
      console.log('🔥 ENHANCE API: Image result type:', typeof imageResult);
      
      // Check if it's a ReadableStream
      if (imageResult && typeof imageResult === 'object' && 'locked' in imageResult) {
        console.log('🔥 ENHANCE API: Converting ReadableStream to blob');
        const response = new Response(imageResult as ReadableStream);
        const blob = await response.blob();
        const buffer = await blob.arrayBuffer();
        const base64 = Buffer.from(buffer).toString('base64');
        const dataUrl = `data:image/png;base64,${base64}`;
        
        console.log('🔥 ENHANCE API: Success - returning data URL');
        return NextResponse.json({ image: dataUrl });
      } else {
        // If it's already a URL string
        console.log('🔥 ENHANCE API: Success - returning image URL:', imageResult);
        return NextResponse.json({ image: imageResult });
      }
    } else {
      console.log('🔥 ENHANCE API: Error - no image in output');
      return NextResponse.json({ error: 'No enhanced image generated' }, { status: 500 });
    }
  } catch (err) {
    console.error('🔥 ENHANCE API: Replicate API error:', err);
    return NextResponse.json({
      error: 'Failed to enhance image',
      details: err instanceof Error ? err.message : 'Unknown error'
    }, { status: 500 });
  }
} 