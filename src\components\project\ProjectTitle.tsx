"use client";

import React from "react";
import { Input } from "@/components/ui/input";
import { Info } from "lucide-react";
import { useProject } from "./ProjectContext";

const ProjectTitle = () => {
    const { title, setTitle, learningMode } = useProject();

    return (
        <div className="mb-6 text-center flex flex-col items-center">
            <h3 className="text-lg font-medium mb-2">Project Title</h3>
            <Input
                type="text"
                placeholder="Enter a creative title for your project"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                className="mb-4 rounded-full px-4 py-4 placeholder:text-center placeholder:text-gray-400"
            />
            {learningMode && (
                <div className="flex items-center gap-2 bg-littlespark-light-blue-2 border border-littlespark-light-blue p-4 rounded-full">
                    <Info className="h-5 w-5 text-blue-500" />
                    <div className="">
                        <h4 className="font-semibold text-sm text-blue-600 font-fredoka">
                            Title Creation
                        </h4>
                        <p className="text-sm text-black">
                            A good project title should be clear, creative, and give others an idea of what your project is about.
                        </p>
                    </div>
                </div>
            )}
        </div>
    );
};

export default ProjectTitle;
