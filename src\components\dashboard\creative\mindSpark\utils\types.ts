
// Define the ChatMessage interface to fix the type error
export interface ChatMessage {
  id: string;
  role: string;
  content: string;
  timestamp: string;
}

// Make ChatMessage a proper JSON object
export type JsonValue = string | number | boolean | null | { [key: string]: JsonValue } | JsonValue[];
export type Json = JsonValue;

// Helper function to convert any data to JSON-safe format
export const convertToJson = <T>(data: T): Json => {
  return JSON.parse(JSON.stringify(data)) as Json;
};
