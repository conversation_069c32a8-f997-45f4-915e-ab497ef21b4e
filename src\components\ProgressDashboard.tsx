import React from "react";
import { Trophy } from "lucide-react";
import {
    ProgressMetrics,
    SkillProgress,
    Recommendations,
} from "./progress-dashboard";

interface ProgressDashboardProps {
    completedProjects?: number;
    skillLevel?: number;
    skillProgress?: number;
    streakDays?: number;
    badges?: { name: string; icon: string; earned: boolean }[];
}

const ProgressDashboard = ({
    completedProjects = 0,
    skillLevel = 1,
    skillProgress: propSkillProgress,
    streakDays = 0,
    badges = [
        { name: "Storyteller", icon: "📝", earned: false },
        { name: "Artist", icon: "🎨", earned: false },
        { name: "<PERSON><PERSON>", icon: "🎵", earned: false },
        { name: "Game Designer", icon: "🎮", earned: false },
        { name: "Creative Star", icon: "⭐", earned: false },
    ],
}: ProgressDashboardProps) => {
    // Use provided skill progress or calculate fallback
    const skillProgress = propSkillProgress ?? (skillLevel / 10) * 100;
    const badgeCount = badges.filter((b) => b.earned).length;

    return (
        <div className="bg-white rounded-2xl shadow-md p-6">
            <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold flex items-center gap-2">
                    <Trophy className="h-6 w-6 text-spark-yellow" />
                    My Creative Journey
                </h2>
                <div className="bg-spark-purple/10 text-spark-purple px-3 py-1 rounded-full font-semibold text-sm">
                    Level {skillLevel}
                </div>
            </div>

            <ProgressMetrics
                completedProjects={completedProjects}
                streakDays={streakDays}
                badgeCount={badgeCount}
            />

            <SkillProgress
                skillLevel={skillLevel}
                skillProgress={skillProgress}
            />

            <Recommendations
                badges={badges}
                completedProjects={completedProjects}
            />
        </div>
    );
};

export default ProgressDashboard;
