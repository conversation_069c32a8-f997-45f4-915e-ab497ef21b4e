
/**
 * Draws a unicorn shape on the canvas
 */
export const drawUnicornShape = (ctx: CanvasRenderingContext2D, x: number, y: number, size: number) => {
  // Head
  ctx.ellipse(x, y - size*0.2, size*0.3, size*0.2, 0, 0, 2 * Math.PI);
  
  // Horn
  ctx.moveTo(x, y - size*0.4);
  ctx.lineTo(x + size*0.3, y - size*0.8);
  
  // Mane
  ctx.moveTo(x - size*0.2, y - size*0.3);
  for (let i = 0; i < 5; i++) {
    ctx.quadraticCurveTo(
      x - size*0.4, y - size*0.4 + i*size*0.1,
      x - size*0.2, y - size*0.2 + i*size*0.1
    );
  }
  
  // Body
  ctx.moveTo(x - size*0.2, y);
  ctx.ellipse(x - size*0.5, y + size*0.1, size*0.6, size*0.3, 0, 0, 2 * Math.PI);
  
  // Legs
  ctx.moveTo(x - size*0.7, y + size*0.3);
  ctx.lineTo(x - size*0.7, y + size*0.7);
  
  ctx.moveTo(x - size*0.3, y + size*0.3);
  ctx.lineTo(x - size*0.3, y + size*0.7);
  
  ctx.moveTo(x - size*0.8, y);
  ctx.lineTo(x - size*0.8, y + size*0.4);
  
  ctx.moveTo(x - size*0.2, y);
  ctx.lineTo(x - size*0.2, y + size*0.4);
  
  // Tail
  ctx.moveTo(x - size*1.1, y);
  ctx.quadraticCurveTo(
    x - size*1.3, y - size*0.3,
    x - size*1.1, y - size*0.4
  );
};
