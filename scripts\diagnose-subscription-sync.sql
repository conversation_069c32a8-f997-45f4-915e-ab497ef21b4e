-- Diagnostic script to identify subscription synchronization issues
-- This script helps identify accounts with payment/status mismatches

-- 1. Check all accounts with trialing status and their payment records
SELECT 
    p.email,
    p.subscription_status,
    p.trial_start,
    p.trial_end,
    p.trial_used,
    p.plan_id,
    p.plan_name,
    p.subscription_id,
    p.stripe_customer_id,
    CASE 
        WHEN p.trial_end < NOW() THEN 'EXPIRED'
        ELSE 'ACTIVE'
    END as trial_status,
    COUNT(pay.id) as payment_count,
    MAX(pay.payment_date) as latest_payment,
    SUM(CASE WHEN pay.status = 'succeeded' THEN 1 ELSE 0 END) as successful_payments
FROM profiles p
LEFT JOIN payments pay ON p.id = pay.profile_id
WHERE p.subscription_status = 'trialing'
GROUP BY p.id, p.email, p.subscription_status, p.trial_start, p.trial_end, 
         p.trial_used, p.plan_id, p.plan_name, p.subscription_id, p.stripe_customer_id
ORDER BY p.email;

-- 2. Identify accounts that should be active but are still trialing
SELECT 
    p.email,
    p.subscription_status,
    p.trial_end,
    p.trial_used,
    COUNT(pay.id) as total_payments,
    SUM(CASE WHEN pay.status = 'succeeded' THEN 1 ELSE 0 END) as successful_payments,
    MAX(pay.payment_date) as latest_payment_date,
    STRING_AGG(DISTINCT pay.status, ', ') as payment_statuses
FROM profiles p
LEFT JOIN payments pay ON p.id = pay.profile_id
WHERE p.subscription_status = 'trialing'
    AND p.trial_end < NOW()
    AND EXISTS (
        SELECT 1 FROM payments pay2 
        WHERE pay2.profile_id = p.id 
        AND pay2.status = 'succeeded'
    )
GROUP BY p.id, p.email, p.subscription_status, p.trial_end, p.trial_used
ORDER BY p.email;

-- 3. Check payment details for problematic accounts
SELECT 
    p.email,
    p.subscription_status,
    pay.stripe_payment_id,
    pay.amount,
    pay.currency,
    pay.status,
    pay.plan_id as payment_plan_id,
    pay.plan_name as payment_plan_name,
    pay.payment_date,
    pay.created_at
FROM profiles p
JOIN payments pay ON p.id = pay.profile_id
WHERE p.subscription_status = 'trialing'
    AND p.trial_end < NOW()
    AND pay.status = 'succeeded'
ORDER BY p.email, pay.payment_date DESC;

-- 4. Compare subscription status vs payment records
SELECT 
    'MISMATCH SUMMARY' as report_type,
    COUNT(*) as accounts_with_mismatch
FROM profiles p
WHERE p.subscription_status = 'trialing'
    AND p.trial_end < NOW()
    AND EXISTS (
        SELECT 1 FROM payments pay 
        WHERE pay.profile_id = p.id 
        AND pay.status = 'succeeded'
        AND pay.created_at >= p.trial_end - INTERVAL '7 days'
    );

-- 5. Show all subscription statuses and their counts
SELECT 
    subscription_status,
    COUNT(*) as count,
    COUNT(CASE WHEN trial_end < NOW() THEN 1 END) as expired_trials
FROM profiles 
WHERE subscription_status IS NOT NULL
GROUP BY subscription_status
ORDER BY count DESC;

-- 6. Detailed view of the 3 test accounts mentioned
-- (Replace with actual email addresses if known)
SELECT 
    p.email,
    p.subscription_status,
    p.trial_start,
    p.trial_end,
    p.trial_used,
    p.plan_id,
    p.subscription_id,
    p.stripe_customer_id,
    p.updated_at as profile_last_updated,
    CASE 
        WHEN p.trial_end < NOW() THEN 'TRIAL_EXPIRED'
        WHEN p.trial_end > NOW() THEN 'TRIAL_ACTIVE'
        ELSE 'NO_TRIAL_END'
    END as trial_status,
    (
        SELECT COUNT(*) 
        FROM payments pay 
        WHERE pay.profile_id = p.id 
        AND pay.status = 'succeeded'
    ) as successful_payment_count,
    (
        SELECT MAX(pay.payment_date) 
        FROM payments pay 
        WHERE pay.profile_id = p.id 
        AND pay.status = 'succeeded'
    ) as latest_successful_payment
FROM profiles p
WHERE p.subscription_status IN ('trialing', 'active')
    AND p.trial_end IS NOT NULL
ORDER BY 
    CASE WHEN p.subscription_status = 'trialing' THEN 1 ELSE 2 END,
    p.email;
