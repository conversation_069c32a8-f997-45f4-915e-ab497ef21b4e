
import React from 'react';
import { Bookmark, Clock, Award } from 'lucide-react';

interface ProgressMetricsProps {
  completedProjects: number;
  streakDays: number;
  badgeCount: number;
}

const ProgressMetrics = ({ completedProjects, streakDays, badgeCount }: ProgressMetricsProps) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
      <div className="bg-spark-purple/5 rounded-xl p-4 text-center">
        <div className="text-3xl font-bold text-spark-purple mb-1">{completedProjects}</div>
        <div className="text-sm text-gray-600 flex items-center justify-center gap-1">
          <Bookmark className="h-4 w-4" /> Projects Created
        </div>
      </div>
      
      <div className="bg-spark-green/5 rounded-xl p-4 text-center">
        <div className="text-3xl font-bold text-spark-green mb-1">{streakDays}</div>
        <div className="text-sm text-gray-600 flex items-center justify-center gap-1">
          <Clock className="h-4 w-4" /> Day Streak
        </div>
      </div>
      
      <div className="bg-spark-blue/5 rounded-xl p-4 text-center">
        <div className="text-3xl font-bold text-spark-blue mb-1">{badgeCount}</div>
        <div className="text-sm text-gray-600 flex items-center justify-center gap-1">
          <Award className="h-4 w-4" /> Badges Earned
        </div>
      </div>
    </div>
  );
};

export default ProgressMetrics;
