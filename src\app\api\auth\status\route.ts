import { NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase/server';
import { prisma } from '@/lib/prisma';

export async function GET() {
  try {
    const supabase = await createServerSupabaseClient();

    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError) {
      return NextResponse.json({
        authenticated: false,
        error: authError.message
      });
    }

    if (!user) {
      return NextResponse.json({
        authenticated: false,
        user: null
      });
    }

    // Get user profile if authenticated using Prisma
    try {
      const profile = await prisma.profile.findUnique({
        where: { id: user.id }
      });

      return NextResponse.json({
        authenticated: true,
        user: {
          id: user.id,
          email: user.email,
          created_at: user.created_at,
          user_metadata: user.user_metadata
        },
        profile: profile || null,
        profileError: null
      });
    } catch (profileError) {
      return NextResponse.json({
        authenticated: true,
        user: {
          id: user.id,
          email: user.email,
          created_at: user.created_at,
          user_metadata: user.user_metadata
        },
        profile: null,
        profileError: profileError instanceof Error ? profileError.message : 'Unknown profile error'
      });
    }

  } catch (error) {
    console.error('Auth status API error:', error);
    return NextResponse.json(
      { 
        authenticated: false,
        error: 'Internal server error' 
      },
      { status: 500 }
    );
  }
} 