"use client";
import React from "react";

interface AICreatorPanelProps {
  aiPrompt: string;
  setAiPrompt: (v: string) => void;
  suggestions: string[];
  onSuggestionClick: (s: string) => void;
  onRefresh: () => void;
  onCreate: () => void;
  generating?: boolean;
}

const AICreatorPanel: React.FC<AICreatorPanelProps> = ({ aiPrompt, setAiPrompt, suggestions, onSuggestionClick, onRefresh, onCreate, generating }) => {
  return (
    <div className="w-full max-w-3xl bg-[#f6fcfd] rounded-2xl shadow-lg mt-4 p-4 lg:p-8 border border-spark-teal flex flex-col items-center">
      <h2 className="text-xl lg:text-2xl font-extrabold text-spark-blue mb-2 text-center">AI Coloring Page Creator</h2>
      <p className="text-gray-600 text-center mb-4 lg:mb-6 text-sm lg:text-base">Generate line art outlines to color - perfect when you need structure!</p>
      <div className="w-full flex flex-col items-center mb-4">
        <span className="font-semibold text-base lg:text-lg mb-2">What line art do you want to color?</span>
        <form
          className="flex w-full max-w-4xl items-center gap-2 lg:gap-3 flex-col sm:flex-row"
          onSubmit={e => { 
            e.preventDefault(); 
            onCreate(); 
          }}
        >
          <input
            className="flex-1 rounded-full border border-[#00BFA5] px-4 lg:px-5 py-2 lg:py-3 text-sm lg:text-base outline-none focus:border-[#00BFA5]"
            placeholder="Describe what you want to color (e.g., dog, car, princess, dragon)..."
            value={aiPrompt}
            onChange={e => setAiPrompt(e.target.value)}
          />
          <button
            type="submit"
            className={`rounded-full bg-[#00BFA5] text-white px-4 lg:px-6 py-2 lg:py-3 font-semibold flex items-center gap-2 text-sm lg:text-base flex-shrink-0 border-2 border-[#00BFA5] hover:bg-[#00BFA5] hover:text-white transition w-full sm:w-auto ${(!aiPrompt.trim() || generating) ? 'opacity-60 cursor-not-allowed' : ''}`}
            disabled={!aiPrompt.trim() || generating}
            onClick={e => {
              e.preventDefault();
              e.stopPropagation();
              if (aiPrompt.trim() && !generating) {
                onCreate();
              }
            }}
          >
            {generating ? (
              <span className="flex items-center gap-2">
                <svg className="animate-spin h-4 w-4 lg:h-5 lg:w-5" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z" />
                </svg>
                <span className="hidden sm:inline">Generating Line Art...</span>
                <span className="sm:hidden">Generating...</span>
              </span>
            ) : (
              <>
                <svg width="16" height="16" className="lg:w-5 lg:h-5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                  <path d="M12 19V6m0 0l-7 7m7-7l7 7" />
                </svg>
                <span className="hidden sm:inline">Generate Line Art</span>
                <span className="sm:hidden">Generate</span>
              </>
            )}
          </button>
        </form>
      </div>
      <div className="w-full flex flex-col items-start mt-4 lg:mt-6">
        <div className="flex flex-col sm:flex-row items-start sm:items-center mb-2 gap-2">
          <span className="font-semibold text-sm lg:text-base mr-0 sm:mr-4">Need inspiration? Try these or generate new:</span>
          <button className="rounded-full border-2 border-[#00BFA5] px-3 lg:px-4 py-1 lg:py-2 text-[#00BFA5] font-semibold bg-white hover:bg-[#00BFA5] hover:text-white transition text-xs lg:text-sm" onClick={onRefresh}>Refresh</button>
          {/* <button className="ml-4 rounded-full border-2 border-[#00BFA5] px-4 py-2 text-[#00BFA5] font-semibold bg-white hover:bg-[#00BFA5] hover:text-white transition" onClick={onGeneratePrompt}>Generate Prompt</button> */}
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 lg:gap-3 w-full">
          {suggestions.map((s, i) => (
            <button
              key={i}
              className="rounded-full border-2 border-[#00BFA5] px-3 lg:px-6 py-1 lg:py-2 text-[#00BFA5] font-semibold bg-white hover:bg-[#00BFA5] hover:text-white transition text-xs lg:text-sm"
              onClick={() => onSuggestionClick(s)}
            >
              {s}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default AICreatorPanel; 