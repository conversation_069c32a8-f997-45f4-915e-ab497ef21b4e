# Subscription Flow Testing Guide

This guide outlines how to test the complete subscription flow from trial signup to paid conversion.

## Test Scenarios

### 1. Trial Signup Flow
1. **Navigate to pricing page** (`/pricing`)
2. **Select a plan** (Monthly/Quarterly/Annual)
3. **Complete payment form** with test card
4. **Verify trial creation**:
   - Check database: `subscription_status = 'trialing'`
   - Check trial dates are set correctly
   - Verify Stripe subscription is created with trial

### 2. Trial Period Access
1. **During trial period**:
   - User should have full access to dashboard
   - Can create content (stories, art, music)
   - Can complete challenges
   - Subscription status shows "trialing"

2. **Trial expiration warning**:
   - 3 days before expiry: Show renewal banner
   - 1 day before expiry: Show urgent warning
   - Day of expiry: Show final warning

### 3. Trial Expiration Scenarios

#### Scenario A: Successful Auto-Payment
1. **Setup**: Trial ends with valid payment method
2. **Expected behavior**:
   - Stripe automatically charges the customer
   - Webhook `customer.subscription.updated` fires
   - Status changes from `trialing` to `active`
   - Payment record created in database
   - User retains access

#### Scenario B: Failed Auto-Payment
1. **Setup**: Trial ends with invalid/expired payment method
2. **Expected behavior**:
   - <PERSON><PERSON> fails to charge customer
   - Webhook `invoice.payment_failed` fires
   - Status changes to `past_due` or `incomplete`
   - User loses access to protected features
   - Redirect to pricing page with trial expired message

#### Scenario C: No Payment Method
1. **Setup**: Trial ends without payment method
2. **Expected behavior**:
   - Webhook `invoice.payment_action_required` fires
   - Status changes to `incomplete`
   - User loses access immediately
   - Redirect to pricing page

### 4. Access Control Testing

#### Protected Routes
Test that these routes redirect to pricing when trial expires:
- `/dashboard`
- `/create/story`
- `/create/art`
- `/create/music`
- `/my-projects`

#### Protected API Endpoints
Test that these APIs return 403 when trial expires:
- `/api/story/generate`
- `/api/art/generate`
- `/api/music/generate`
- `/api/user-content`
- `/api/challenges/mark-complete`

### 5. Database State Verification

#### During Trial
```sql
SELECT 
  email,
  subscription_status,
  trial_start,
  trial_end,
  trial_used,
  plan_id,
  plan_name
FROM profiles 
WHERE subscription_status = 'trialing';
```

#### After Successful Conversion
```sql
SELECT 
  p.email,
  p.subscription_status,
  p.trial_used,
  p.plan_id,
  pay.amount,
  pay.status,
  pay.payment_date
FROM profiles p
LEFT JOIN payments pay ON p.id = pay.profile_id
WHERE p.subscription_status = 'active'
AND p.trial_used = true;
```

#### After Failed Conversion
```sql
SELECT 
  email,
  subscription_status,
  trial_end,
  trial_used
FROM profiles 
WHERE subscription_status IN ('past_due', 'incomplete')
AND trial_end < NOW();
```

## Test Data Setup

### Test Credit Cards (Stripe)
- **Success**: `****************`
- **Decline**: `****************`
- **Insufficient funds**: `****************`
- **Expired card**: `****************`

### Manual Testing Steps

1. **Create test user account**
2. **Start trial with test card**
3. **Manually expire trial** (update `trial_end` in database)
4. **Trigger webhook manually** or wait for Stripe to process
5. **Verify access control** by trying to access protected routes
6. **Check database state** matches expected scenario

## Automated Testing

### Webhook Testing
Use Stripe CLI to forward webhooks locally:
```bash
stripe listen --forward-to localhost:3000/api/stripe/webhook
```

### Database Testing
```sql
-- Simulate trial expiration
UPDATE profiles 
SET trial_end = NOW() - INTERVAL '1 day'
WHERE email = '<EMAIL>';

-- Check access control logic
SELECT 
  email,
  subscription_status,
  trial_end,
  CASE 
    WHEN trial_end < NOW() AND subscription_status = 'trialing' THEN 'EXPIRED'
    WHEN subscription_status IN ('active', 'trialing', 'cancel_at_period_end') THEN 'ACTIVE'
    ELSE 'INACTIVE'
  END as access_status
FROM profiles;
```

## Expected Results

### Successful Flow
1. ✅ Trial created with correct dates
2. ✅ User has access during trial
3. ✅ Auto-payment succeeds at trial end
4. ✅ Status updates to 'active'
5. ✅ Payment record created
6. ✅ User retains access

### Failed Flow
1. ✅ Trial created with correct dates
2. ✅ User has access during trial
3. ❌ Auto-payment fails at trial end
4. ✅ Status updates to 'past_due'/'incomplete'
5. ✅ User loses access
6. ✅ Redirected to pricing page

## Troubleshooting

### Common Issues
1. **Webhook not firing**: Check Stripe webhook configuration
2. **Status not updating**: Check webhook handler logs
3. **Access still granted**: Check middleware logic
4. **Payment not recorded**: Check webhook payment handler

### Debug Commands
```bash
# Check webhook logs
tail -f logs/webhook.log

# Check subscription status
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:3000/api/subscription/status

# Test access control
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:3000/api/story/generate
```
