import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { SubscriptionPlan } from "./SubscriptionPlans";
import {
    PaymentFormValues,
    GiftFormValues,
} from "./payment-form/PaymentFormSchema";

interface PaymentFormProps {
    plan: SubscriptionPlan | null;
    onSubmit: (values: PaymentFormValues | GiftFormValues) => void;
    isGift: boolean;
    recipientEmail: string;
    setRecipientEmail: (email: string) => void;
    senderName: string;
    setSenderName: (name: string) => void;
    giftMessage: string;
    setGiftMessage: (message: string) => void;
    isProcessing: boolean;
}

const PaymentForm: React.FC<PaymentFormProps> = ({
    plan,
    onSubmit,
    isGift,
    recipientEmail,
    setRecipientEmail,
    senderName,
    setSenderName,
    giftMessage,
    setGiftMessage,
    isProcessing,
}) => {
    const [formData, setFormData] = useState({
        email: "",
        fullName: "",
        cardNumber: "",
        expiryDate: "",
        cvv: "",
        billingAddress: {
            street: "",
            city: "",
            state: "",
            zipCode: "",
            country: "",
        },
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        if (isGift) {
            const giftFormData: GiftFormValues = {
                ...formData,
                recipientEmail,
                senderName,
                giftMessage,
            };
            onSubmit(giftFormData);
        } else {
            onSubmit(formData);
        }
    };

    const handleInputChange = (field: string, value: string) => {
        if (field.startsWith("billingAddress.")) {
            const addressField = field.replace("billingAddress.", "");
            setFormData((prev) => ({
                ...prev,
                billingAddress: {
                    ...prev.billingAddress,
                    [addressField]: value,
                },
            }));
        } else {
            setFormData((prev) => ({
                ...prev,
                [field]: value,
            }));
        }
    };

    return (
        <form onSubmit={handleSubmit} className="space-y-4">
            {plan && (
                <div className="bg-gray-50 p-3 rounded-md mb-4">
                    <h3 className="font-semibold">{plan.name}</h3>
                    <p className="text-sm text-gray-600">
                        {plan.price}
                        {plan.priceSubtext}
                    </p>
                </div>
            )}

            {isGift && (
                <div className="space-y-3 border-b pb-4 mb-4">
                    <h4 className="font-medium">Gift Details</h4>
                    <Input
                        placeholder="Recipient Email"
                        value={recipientEmail}
                        onChange={(e) => setRecipientEmail(e.target.value)}
                        type="email"
                        required
                    />
                    <Input
                        placeholder="Your Name"
                        value={senderName}
                        onChange={(e) => setSenderName(e.target.value)}
                        required
                    />
                    <textarea
                        placeholder="Gift Message (optional)"
                        value={giftMessage}
                        onChange={(e) => setGiftMessage(e.target.value)}
                        className="w-full p-2 border border-gray-300 rounded-md text-sm"
                        rows={3}
                    />
                </div>
            )}

            <div className="space-y-3">
                <h4 className="font-medium">Payment Information</h4>
                <Input
                    placeholder="Email"
                    value={formData.email}
                    onChange={(e) => handleInputChange("email", e.target.value)}
                    type="email"
                    required
                />
                <Input
                    placeholder="Full Name"
                    value={formData.fullName}
                    onChange={(e) =>
                        handleInputChange("fullName", e.target.value)
                    }
                    required
                />
                <Input
                    placeholder="Card Number"
                    value={formData.cardNumber}
                    onChange={(e) =>
                        handleInputChange("cardNumber", e.target.value)
                    }
                    required
                />
                <div className="grid grid-cols-2 gap-2">
                    <Input
                        placeholder="MM/YY"
                        value={formData.expiryDate}
                        onChange={(e) =>
                            handleInputChange("expiryDate", e.target.value)
                        }
                        required
                    />
                    <Input
                        placeholder="CVV"
                        value={formData.cvv}
                        onChange={(e) =>
                            handleInputChange("cvv", e.target.value)
                        }
                        required
                    />
                </div>
            </div>

            <Button type="submit" className="w-full" disabled={isProcessing}>
                {isProcessing
                    ? "Processing..."
                    : isGift
                    ? "Purchase Gift"
                    : "Start Subscription"}
            </Button>
        </form>
    );
};

export default PaymentForm;
