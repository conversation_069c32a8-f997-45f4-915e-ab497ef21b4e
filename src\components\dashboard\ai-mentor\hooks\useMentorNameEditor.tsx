
import { useState } from 'react';
import { toast } from 'sonner';
import { Mentor<PERSON>haracter } from '../types';

export const useMentorNameEditor = (
  mentor<PERSON><PERSON>cter: <PERSON>tor<PERSON><PERSON><PERSON>,
  updateMentorName: (character: <PERSON><PERSON><PERSON><PERSON><PERSON>, newName: string) => void
) => {
  const [isEditingName, setIsEditingName] = useState(false);
  const [mentorName, setMentorName] = useState('');
  
  const handleSaveName = () => {
    if (mentorName) {
      updateMentorName(mentorCharacter, mentorName);
      toast.success(`Renamed to ${mentorName}!`);
    }
    
    setIsEditingName(false);
    setMentorName('');
  };
  
  return {
    isEditingName,
    mentorName,
    setMentorName,
    setIsEditingName,
    handleSaveName
  };
};
