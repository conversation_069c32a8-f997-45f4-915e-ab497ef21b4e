import { supabase } from '@/lib/supabase/client';
import { ChatMessage } from '../types/chatTypes';

// Default conversation with just welcome message
const getDefaultConversation = (): ChatMessage[] => {
  const now = Date.now();

  // Simple welcome message for all characters
  const welcomeMessage: ChatMessage = {
    id: now.toString(),
    role: 'assistant',
    content: 'Hi! I\'m your creative AI mentor 👋\n\nHow can I help you today?',
    timestamp: new Date(now).toISOString()
  };

  return [welcomeMessage];
};

/**
 * Safely transforms ChatMessage objects to JSON-compatible format
 */
const prepareChatMessagesForStorage = (messages: ChatMessage[]): Record<string, unknown>[] => {
  return messages.map(msg => ({
    id: msg.id,
    role: msg.role,
    content: msg.content,
    timestamp: msg.timestamp
  }));
};

/**
 * Saves chat history to localStorage (primary) and attempts Supabase (secondary)
 */
export const saveChatHistory = async (
  updatedMessages: ChatMessage[],
  userId?: string
): Promise<void> => {
  try {
    // Create user-specific localStorage key
    const storageKey = userId ? `mentorChatHistory_${userId}` : 'mentorChatHistory_anonymous';
    localStorage.setItem(storageKey, JSON.stringify(updatedMessages));
    console.log('Chat history saved to localStorage successfully for user:', userId || 'anonymous');
  } catch (localStorageError) {
    console.error('Error saving to localStorage:', localStorageError);
    throw new Error('Failed to save chat history locally');
  }

  // If user is not authenticated, we're done
  if (!userId) {
    console.log('No user ID provided, localStorage save complete');
    return;
  }

  // Try to save to database as secondary storage (non-blocking)
  // Skip database operations for now to avoid permission errors
  try {
    // Database operations disabled to prevent permission errors
    // All chat history is stored in localStorage only
    console.log('Database storage disabled - using localStorage only for chat history');
    return; // Skip database operations

    /* Commented out database operations
    console.log('Attempting to save chat history to database for user:', userId);

    // Prepare the content metadata
    const contentMetadata = {
      messages: prepareChatMessagesForStorage(updatedMessages.slice(-100))
    };

    // Check if user_content table exists and if user has a record
    const { data: existingData, error: fetchError } = await supabase
      .from('user_content')
      .select('id')
      .eq('user_id', userId)
      .eq('type', 'ai_chat')
      .maybeSingle(); // Use maybeSingle to handle no results gracefully

    if (fetchError) {
      console.warn('Database table might not exist or user not authorized:', fetchError);
      return; // Gracefully continue with localStorage only
    }
    */

    if (existingData?.id) {
      console.log('Updating existing chat record:', existingData.id);
      const { error } = await supabase
        .from('user_content')
        .update({
          content_metadata: contentMetadata,
          updated_at: new Date().toISOString()
        })
        .eq('id', existingData.id);

      if (error) {
        console.warn('Error updating chat history, continuing with localStorage:', error);
        return;
      }
      console.log('Chat history updated in database successfully');
    } else {
      console.log('Creating new chat record');
      const { error } = await supabase
        .from('user_content')
        .insert({
          user_id: userId,
          type: 'ai_chat',
          title: 'AI Mentor Chat',
          content_metadata: contentMetadata,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      if (error) {
        console.warn('Error creating chat history, continuing with localStorage:', error);
        return;
      }
      console.log('Chat history created in database successfully');
    }
  } catch (err) {
    console.warn('Database operation failed, continuing with localStorage only:', err);
    // Don't throw error - localStorage save was successful
  }
};

/**
 * Loads chat history from localStorage (primary) with database fallback
 */
export const loadChatHistory = async (
  userId?: string
): Promise<ChatMessage[]> => {
  console.log('Loading chat history for user:', userId || 'anonymous');

  // Try localStorage first (primary storage) with user-specific key
  try {
    const storageKey = userId ? `mentorChatHistory_${userId}` : 'mentorChatHistory_anonymous';
    const savedMessages = localStorage.getItem(storageKey);
    if (savedMessages) {
      const parsedMessages = JSON.parse(savedMessages);
      console.log('Loaded chat history from localStorage:', parsedMessages.length, 'messages for user:', userId || 'anonymous');
      return parsedMessages;
    }
  } catch (err) {
    console.error('Error parsing localStorage chat history:', err);
    const storageKey = userId ? `mentorChatHistory_${userId}` : 'mentorChatHistory_anonymous';
    localStorage.removeItem(storageKey);
  }

  // Database operations disabled to prevent permission errors
  // All chat history is loaded from localStorage only
  if (userId) {
    console.log('Database loading disabled - using localStorage only for chat history');

    /* Commented out database operations
    try {
      console.log('Attempting to load from database...');
      const { data, error } = await supabase
        .from('user_content')
        .select('content_metadata')
        .eq('user_id', userId)
        .eq('type', 'ai_chat')
        .maybeSingle();

      if (error) {
        console.warn('Database query failed, using default messages:', error);
      } else if (data?.content_metadata?.messages) {
        console.log('Loaded chat history from database:', data.content_metadata.messages.length, 'messages');
        // Also save to localStorage for future use with user-specific key
        const storageKey = userId ? `mentorChatHistory_${userId}` : 'mentorChatHistory_anonymous';
        localStorage.setItem(storageKey, JSON.stringify(data.content_metadata.messages));
        return data.content_metadata.messages as ChatMessage[];
      }
    } catch (dbError) {
      console.warn('Database error, using default messages:', dbError);
    }
    */
  }

  console.log('No chat history found, returning default messages');
  return getDefaultConversation();
};

/**
 * Clears chat history for a specific user
 */
export const clearChatHistory = async (userId?: string): Promise<void> => {
  try {
    // Clear localStorage
    const storageKey = userId ? `mentorChatHistory_${userId}` : 'mentorChatHistory_anonymous';
    localStorage.removeItem(storageKey);
    console.log('Chat history cleared from localStorage for user:', userId || 'anonymous');

    // Database operations disabled to prevent permission errors
    if (userId) {
      console.log('Database clearing disabled - localStorage cleared only');

      /* Commented out database operations
      try {
        const { error } = await supabase
          .from('user_content')
          .delete()
          .eq('user_id', userId)
          .eq('type', 'ai_chat');

        if (error) {
          console.warn('Error clearing chat history from database:', error);
        } else {
          console.log('Chat history cleared from database for user:', userId);
        }
      } catch (dbError) {
        console.warn('Database operation failed while clearing chat:', dbError);
      }
      */
    }
  } catch (error) {
    console.error('Error clearing chat history:', error);
    throw new Error('Failed to clear chat history');
  }
};

/**
 * Clears all chat history (for cleanup purposes)
 */
export const clearAllChatHistory = (): void => {
  try {
    // Get all localStorage keys that match our pattern
    const localKeysToRemove: string[] = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (key.startsWith('mentorChatHistory_') || key === 'mentorChatHistory')) {
        localKeysToRemove.push(key);
      }
    }

    // Get all sessionStorage keys that match our pattern
    const sessionKeysToRemove: string[] = [];
    for (let i = 0; i < sessionStorage.length; i++) {
      const key = sessionStorage.key(i);
      if (key && key.startsWith('chatSession_')) {
        sessionKeysToRemove.push(key);
      }
    }

    // Remove all matching keys from localStorage
    localKeysToRemove.forEach(key => {
      localStorage.removeItem(key);
    });

    // Remove all matching keys from sessionStorage
    sessionKeysToRemove.forEach(key => {
      sessionStorage.removeItem(key);
    });

    console.log('All chat history and sessions cleared from storage');
  } catch (error) {
    console.error('Error clearing all chat history:', error);
  }
};
