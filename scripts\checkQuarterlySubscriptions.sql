-- Check current quarterly subscriptions
SELECT 
    email,
    plan_id,
    billing_cycle,
    subscription_status,
    subscription_start,
    subscription_end,
    EXTRACT(DAY FROM (subscription_end - subscription_start)) as duration_days,
    CASE 
        WHEN EXTRACT(DAY FROM (subscription_end - subscription_start)) >= 80 AND 
             EXTRACT(DAY FROM (subscription_end - subscription_start)) <= 100 THEN 'CORRECT'
        ELSE 'INCORRECT'
    END as period_status
FROM profiles 
WHERE plan_id = 'quarterly-tier' 
    AND subscription_status IN ('active', 'trialing', 'cancel_at_period_end')
    AND subscription_start IS NOT NULL 
    AND subscription_end IS NOT NULL
ORDER BY email; 