import React from "react";
import { Sparkles } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import RecommendationItem from "./RecommendationItem";

export interface Badge {
    name: string;
    icon: string;
    earned: boolean;
}

export interface Recommendation {
    text: string;
    icon: React.ReactNode;
    path: string;
    colorClass: string;
}

interface RecommendationsProps {
    badges: Badge[];
    completedProjects: number;
}

const Recommendations = ({
    badges,
    completedProjects,
}: RecommendationsProps) => {
    const router = useRouter();

    // Generate truly dynamic recommendations based on badges and activity
    const generateRecommendations = () => {
        const recommendations: Recommendation[] = [];
        const earnedBadges = badges.filter((b) => b.earned);

        // Logic for users with no earned badges (beginners)
        if (earnedBadges.length === 0) {
            recommendations.push({
                text: "Start your creative journey with a story",
                icon: <Sparkles className="h-5 w-5 text-spark-purple" />,
                path: "/create/story",
                colorClass: "bg-spark-purple/5 hover:bg-spark-purple/10",
            });

            recommendations.push({
                text: "Create your first artwork",
                icon: <Sparkles className="h-5 w-5 text-spark-yellow" />,
                path: "/create/art",
                colorClass: "bg-spark-yellow/5 hover:bg-spark-yellow/10",
            });

            return recommendations;
        }

        // Get badges that aren't earned yet
        const notEarnedBadges = badges.filter((b) => !b.earned);

        // If there are badges not yet earned, recommend one of those activities
        if (notEarnedBadges.length > 0) {
            const nextBadge = notEarnedBadges[0];

            let recommendation: Recommendation = {
                text: "",
                icon: <Sparkles className="h-5 w-5" />,
                path: "",
                colorClass: "",
            };

            switch (nextBadge.name) {
                case "Storyteller":
                    recommendation = {
                        text: "Create your first story adventure",
                        icon: (
                            <Sparkles className="h-5 w-5 text-spark-purple" />
                        ),
                        path: "/create/story",
                        colorClass:
                            "bg-spark-purple/5 hover:bg-spark-purple/10",
                    };
                    break;
                case "Artist":
                    recommendation = {
                        text: "Draw your first picture in the Art Studio",
                        icon: (
                            <Sparkles className="h-5 w-5 text-spark-yellow" />
                        ),
                        path: "/create/art",
                        colorClass:
                            "bg-spark-yellow/5 hover:bg-spark-yellow/10",
                    };
                    break;
                case "Musician":
                    recommendation = {
                        text: "Compose your first musical piece",
                        icon: (
                            <Sparkles className="h-5 w-5 text-spark-lavender" />
                        ),
                        path: "/create/music",
                        colorClass:
                            "bg-spark-lavender/5 hover:bg-spark-lavender/10",
                    };
                    break;
                case "Game Designer":
                    recommendation = {
                        text: "Design your first game world",
                        icon: <Sparkles className="h-5 w-5 text-spark-green" />,
                        path: "/create/game",
                        colorClass: "bg-spark-green/5 hover:bg-spark-green/10",
                    };
                    break;
            }

            recommendations.push(recommendation);
        }

        // If user has earned Story and Art badges, recommend the coloring feature
        if (
            badges.find((b) => b.name === "Storyteller")?.earned &&
            badges.find((b) => b.name === "Artist")?.earned
        ) {
            recommendations.push({
                text: "Try the new AI Coloring Pages feature",
                icon: <Sparkles className="h-5 w-5 text-spark-coral" />,
                path: "/create/coloring",
                colorClass: "bg-spark-coral/5 hover:bg-spark-coral/10",
            });
        }

        // If user has created multiple projects, recommend creating a video
        if (completedProjects >= 3) {
            recommendations.push({
                text: "Create a video from your best story",
                icon: <Sparkles className="h-5 w-5 text-spark-blue" />,
                path: "/create/video",
                colorClass: "bg-spark-blue/5 hover:bg-spark-blue/10",
            });
        }

        // If user has all creative badges, recommend viewing portfolio
        if (earnedBadges.length >= 4) {
            recommendations.push({
                text: "View and share your creative portfolio",
                icon: <Sparkles className="h-5 w-5 text-spark-teal" />,
                path: "/portfolio",
                colorClass: "bg-spark-teal/5 hover:bg-spark-teal/10",
            });
        }

        // If no specific recommendations were found
        if (recommendations.length === 0) {
            recommendations.push({
                text: "Try a new creative challenge",
                icon: <Sparkles className="h-5 w-5 text-spark-yellow" />,
                path: "/dashboard",
                colorClass: "bg-spark-yellow/5 hover:bg-spark-yellow/10",
            });
        }

        // Return up to 2 recommendations
        return recommendations.slice(0, 2);
    };

    const recommendations = generateRecommendations();

    const handleNavigate = (path: string) => {
        router.push(path);
    };

    return (
        <div>
            <h3 className="font-semibold mb-3">Recommended Next Steps</h3>
            {recommendations.length > 0 ? (
                <div className="space-y-2">
                    {recommendations.map((rec, index) => (
                        <RecommendationItem
                            key={index}
                            text={rec.text}
                            icon={rec.icon}
                            path={rec.path}
                            colorClass={rec.colorClass}
                            onClick={handleNavigate}
                        />
                    ))}
                </div>
            ) : (
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                    <p className="text-gray-600">
                        Great job! You&apos;ve earned all the basic badges!
                    </p>
                    <Button
                        variant="ghost"
                        className="mt-2"
                        onClick={() => router.push("/portfolio")}
                    >
                        View your portfolio
                    </Button>
                </div>
            )}
        </div>
    );
};

export default Recommendations;
