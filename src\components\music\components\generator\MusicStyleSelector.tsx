
import React from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface MusicStyleSelectorProps {
  selectedStyle: string;
  setSelectedStyle: (style: string) => void;
}

const MusicStyleSelector = ({ selectedStyle, setSelectedStyle }: MusicStyleSelectorProps) => {
  return (
    <div>
      <label className="block text-sm text-center font-medium mb-2">Music Style</label>
      <Select 
        value={selectedStyle} 
        onValueChange={setSelectedStyle}
      >
        <SelectTrigger>
          <SelectValue placeholder="Select style" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="orchestral">Orchestral</SelectItem>
          <SelectItem value="electronic">Electronic</SelectItem>
          <SelectItem value="rock">Rock</SelectItem>
          <SelectItem value="jazz">Jazz</SelectItem>
          <SelectItem value="pop">Pop</SelectItem>
          <SelectItem value="ambient">Ambient</SelectItem>
          <SelectItem value="classical">Classical</SelectItem>
          <SelectItem value="hip-hop">Hip-Hop</SelectItem>
          <SelectItem value="folk">Folk</SelectItem>
          <SelectItem value="acoustic">Acoustic</SelectItem>
          <SelectItem value="bhojpuri">Bhojpuri</SelectItem>
        </SelectContent>
      </Select>
    </div>
  );
};

export default MusicStyleSelector;
