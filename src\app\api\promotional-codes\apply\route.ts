import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { createServerSupabaseClient } from '@/lib/supabase/server';

export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerSupabaseClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { 
      code, 
      planId, 
      originalAmount, 
      discountAmount, 
      finalAmount,
      paymentIntentId 
    } = await request.json();

    if (!code || !planId || originalAmount === undefined || discountAmount === undefined || finalAmount === undefined) {
      return NextResponse.json({ 
        error: 'Missing required fields' 
      }, { status: 400 });
    }

    // Find the promotional code
    const promotionalCode = await prisma.promotionalCode.findUnique({
      where: { code: code.toUpperCase() },
    });

    if (!promotionalCode) {
      return NextResponse.json({ 
        error: 'Invalid promotional code' 
      }, { status: 400 });
    }

    // Double-check that user hasn't already used this code
    const existingUsage = await prisma.promotionalCodeUsage.findUnique({
      where: {
        promotional_code_id_profile_id: {
          promotional_code_id: promotionalCode.id,
          profile_id: user.id,
        },
      },
    });

    if (existingUsage) {
      return NextResponse.json({ 
        error: 'Promotional code already used' 
      }, { status: 400 });
    }

    // Start a transaction to apply the promotional code
    const result = await prisma.$transaction(async (tx) => {
      // Record the promotional code usage
      const usage = await tx.promotionalCodeUsage.create({
        data: {
          promotional_code_id: promotionalCode.id,
          profile_id: user.id,
          discount_applied: discountAmount,
          original_amount: originalAmount,
          final_amount: finalAmount,
        },
      });

      // Update the promotional code usage count
      await tx.promotionalCode.update({
        where: { id: promotionalCode.id },
        data: {
          current_uses: {
            increment: 1,
          },
        },
      });

      // Update user profile with promotional code info
      await tx.profile.update({
        where: { id: user.id },
        data: {
          promotional_code_used: promotionalCode.code,
          promotional_discount: promotionalCode.discount_value,
        },
      });

      return usage;
    });

    return NextResponse.json({
      success: true,
      usage_id: result.id,
      message: 'Promotional code applied successfully',
    });

  } catch (error) {
    console.error('Error applying promotional code:', error);
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 });
  }
}
