# 🧪 Testing Guide for Creative Challenges

## ✅ Database Setup Complete!

The database has been successfully populated with:
- **8 Challenges** across 4 types (story, art, music, game)
- **3 Difficulty levels** (easy, medium, hard)
- **1 Month expiry** for all challenges (until August 14, 2025)

## 🚀 How to Test the Application

### 1. **Start the Application**
```bash
npm run dev
```
- Main app: http://localhost:3000
- Prisma Studio: http://localhost:5556 (for database inspection)

### 2. **Create a User Account**
1. Go to http://localhost:3000
2. Click "Sign Up" or "Login"
3. Create a new account or use existing credentials
4. Complete the profile setup

### 3. **Test Challenge Flow**

#### **Step 1: View Challenges**
- Go to Dashboard
- You should see 8 challenges in different categories
- Challenges should show: title, description, difficulty, type

#### **Step 2: Start a Challenge**
1. Click "Start Challenge" on any challenge
2. System records `challengeStartedAt` timestamp
3. You'll be redirected to the appropriate tool:
   - **Art challenges** → `/create/art`
   - **Story challenges** → `/create/story`
   - **Music challenges** → `/create/music`
   - **Game challenges** → `/create/game`

#### **Step 3: Create Content**
1. Use the creation tool to make content
2. Give it a title and description
3. Click "Save to Portfolio"
4. Content gets saved with current timestamp

#### **Step 4: Complete Challenge**
1. Return to Dashboard
2. Click "Mark as Complete" on the challenge you started
3. System validates:
   - ✅ Content exists with correct type
   - ✅ Content created after challenge start
   - ✅ User hasn't completed this challenge before

### 4. **Test Different Scenarios**

#### **✅ Valid Completion**
1. Start "Friendly Dragon Art" challenge
2. Create artwork in Art Studio
3. Save to portfolio
4. Mark challenge complete → Should succeed

#### **❌ Invalid Completion (Using Old Content)**
1. Create artwork first
2. Then start "Space Explorer Portrait" challenge
3. Try to mark complete → Should fail (content created before challenge start)

#### **❌ Invalid Completion (Wrong Type)**
1. Start "Magical Forest Story" challenge
2. Create artwork instead of story
3. Try to mark complete → Should fail (wrong content type)

## 🔍 Database Inspection

### Using Prisma Studio (http://localhost:5556)
- **Challenges**: View all 8 seeded challenges
- **Profile**: See user accounts
- **UserContent**: View saved content with timestamps
- **ChallengeCompletion**: See completed challenges with linked content

### Using Test Scripts
```bash
# Check database status
node scripts/testChallenges.js

# Test API endpoints
node scripts/testAPI.js
```

## 📊 Available Challenges

| ID | Title | Type | Difficulty |
|---|---|---|---|
| 1 | Magical Forest Story | story | easy |
| 2 | Space Explorer Portrait | art | medium |
| 3 | Heroes Journey Music | music | medium |
| 4 | Puzzle Game Design | game | hard |
| weekly-robot | Creative Challenge of the Week | story | medium |
| ocean-adventure | Ocean Adventure Story | story | easy |
| dragon-friend | Friendly Dragon Art | art | easy |
| celebration-song | Celebration Music | music | easy |

## 🛡️ Security Features to Test

1. **Time Validation**: Can't use old content for new challenges
2. **Type Validation**: Must use correct content type
3. **Unique Completion**: Can't complete same challenge twice
4. **Authentication**: Protected endpoints require login
5. **Content Ownership**: Can only use your own content

## 🐛 Common Issues & Solutions

### Issue: "Failed to save artwork"
- **Cause**: Database migration not applied
- **Solution**: Run `npx prisma migrate deploy`

### Issue: API returns HTML instead of JSON
- **Cause**: Middleware redirecting to auth
- **Solution**: Ensure you're logged in or API route is public

### Issue: No challenges showing
- **Cause**: Challenges not seeded
- **Solution**: Run `npx prisma db execute --file scripts/seedChallenges.sql`

### Issue: Challenge completion fails
- **Cause**: Content created before challenge start
- **Solution**: Create new content after starting challenge

## 📈 Success Metrics

A successful test should show:
- ✅ User can see all 8 challenges
- ✅ Can start challenges and get redirected to tools
- ✅ Can create and save content
- ✅ Can complete challenges with valid content
- ✅ Cannot complete with invalid content
- ✅ Cannot complete same challenge twice
- ✅ Completion celebration shows with badges/progress

## 🎯 Next Steps After Testing

1. **Add more challenges** using the admin interface
2. **Test different user accounts** to verify isolation
3. **Test challenge expiry** by setting short expiry dates
4. **Test content quality validation** if implemented
5. **Test badge system** and progress tracking
