/**
 * Utility function to download an image from a URL
 */
export const downloadImage = async (imageUrl: string, filename: string = 'spark-artwork.png') => {
  if (!imageUrl) return;
  try {
    // Fetch the image data as a blob
    const response = await fetch(imageUrl);
    const blob = await response.blob();
    // Create a temporary object URL for the blob
    const url = URL.createObjectURL(blob);
    // Create a temporary link element to trigger the download
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    // Clean up
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Error downloading image:', error);
  }
};
