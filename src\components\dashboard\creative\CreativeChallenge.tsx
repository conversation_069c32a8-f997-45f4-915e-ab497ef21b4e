import React from "react";
import { <PERSON>, <PERSON>R<PERSON> } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useRouter } from "next/navigation";

const CreativeChallenge = () => {
    const router = useRouter();

    const weeklyChallenge = {
        title: "Creative Challenge of the Week",
        description:
            "First create a story about what the robot does, then draw it in the Art Studio. What special powers does it have?",
        tags: ["Story", "Art", "Robots"],
        path: "/create/story",
    };

    const handleStartChallenge = () => {
        // Store challenge information
        const challenge = {
            id: "weekly-robot",
            title: weeklyChallenge.title,
            description: weeklyChallenge.description,
            type: "story",
            path: weeklyChallenge.path,
        };

        localStorage.setItem("currentChallenge", JSON.stringify(challenge));
        router.push(weeklyChallenge.path);
    };

    return (
        <div className="mb-8">
            <div className="bg-gradient-to-r from-yellow-100 via-green-50 to-blue-100 rounded-xl p-4 sm:p-6 border border-yellow-200">
                <h3 className="text-lg sm:text-xl font-bold mb-3 flex items-center font-fredoka text-gray-800">
                    <Brain className="h-5 w-5 text-orange-500 mr-2" />
                    Creative Challenge of the Week
                </h3>
                <p className="mb-4 font-nunito text-gray-700 text-sm sm:text-base">
                    {weeklyChallenge.description}
                </p>
                <div className="flex flex-col sm:flex-row flex-wrap justify-between items-center gap-4">
                    <div className="flex flex-wrap gap-2">
                        {weeklyChallenge.tags.map((tag, index) => (
                            <span
                                key={index}
                                className={`px-3 py-1 rounded-full text-xs sm:text-sm font-medium ${
                                    index === 0
                                        ? "bg-orange-100 text-orange-600"
                                        : index === 1
                                        ? "bg-cyan-100 text-cyan-600"
                                        : "bg-purple-100 text-purple-600"
                                }`}
                            >
                                {tag}
                            </span>
                        ))}
                    </div>

                    <Button
                        className="gap-2 bg-orange-500 hover:bg-orange-600 text-white font-nunito min-h-[44px] w-full sm:w-auto text-sm sm:text-base"
                        onClick={handleStartChallenge}
                    >
                        Start Challenge <ArrowRight className="h-4 w-4" />
                    </Button>
                </div>
            </div>
        </div>
    );
};

export default CreativeChallenge;
