// Little Spark Color System
export const colors = {
  // Brand Colors
  littlesparkPrimary: '#00c69e',
  littlesparkPrimaryHover: '#00b389',
  littlesparkSecondary: '#00c78f',
  littlesparkLavender: '#9333ea',
  littlesparkYellow: '#F5E45F',
  littlesparkBlue: '#3b82f6',
  
  // Gray Scale
  gray900: '#1f2937',
  gray600: '#4b5563',
  gray500: '#6b7280',
  gray100: '#f3f4f6',
  gray50: '#f9fafb',
  
  // Semantic Colors
  success: '#10b981',
  warning: '#f59e0b',
  error: '#ef4444',
  
  // Background Variants
  purple50: '#faf5ff',
  yellowLight: 'rgba(245, 228, 95, 0.1)',
} as const;

// CSS Custom Property Names
export const cssVars = {
  littlesparkPrimary: 'var(--color-littlespark-primary)',
  littlesparkPrimaryHover: 'var(--color-littlespark-primary-hover)',
  littlesparkSecondary: 'var(--color-littlespark-secondary)',
  littlesparkLavender: 'var(--color-littlespark-lavender)',
  littlesparkYellow: 'var(--color-littlespark-yellow)',
  littlesparkBlue: 'var(--color-littlespark-blue)',
  gray900: 'var(--color-gray-900)',
  gray600: 'var(--color-gray-600)',
  gray500: 'var(--color-gray-500)',
  gray100: 'var(--color-gray-100)',
  gray50: 'var(--color-gray-50)',
  success: 'var(--color-success)',
  warning: 'var(--color-warning)',
  error: 'var(--color-error)',
  purple50: 'var(--color-purple-50)',
  yellowLight: 'var(--color-yellow-light)',
} as const;

export type ColorKey = keyof typeof colors;
export type CSSVarKey = keyof typeof cssVars; 