"use client";
import React, { useRef } from "react";
import { Textarea } from "@/components/ui/textarea";
import VoiceInput from "./VoiceInput";

interface VoiceEnabledTextareaProps
    extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
    onVoiceInput?: (text: string) => void;
}

const VoiceEnabledTextarea = React.forwardRef<
    HTMLTextAreaElement,
    VoiceEnabledTextareaProps
>(({ className, onVoiceInput, onChange, value, ...props }, ref) => {
    const textareaRef = useRef<HTMLTextAreaElement>(null);
    const mergedRef =
        (ref as React.RefObject<HTMLTextAreaElement>) || textareaRef;

    const handleTranscript = (text: string) => {
        if (mergedRef.current) {
            // If there's already text, append the new text
            const currentValue = mergedRef.current.value || "";
            const newValue = currentValue ? `${currentValue} ${text}` : text;

            // Create a synthetic event that mimics a textarea event
            const syntheticEvent = {
                target: {
                    value: newValue,
                },
            } as React.ChangeEvent<HTMLTextAreaElement>;

            // Call the onChange handler if it exists
            if (onChange) {
                onChange(syntheticEvent);
            }

            // Call the additional callback if provided
            if (onVoiceInput) {
                onVoiceInput(newValue);
            }
        }
    };

    return (
        <div className="relative w-full">
            <Textarea
                ref={mergedRef}
                className={className}
                value={value}
                onChange={onChange}
                {...props}
            />
            <div className="absolute right-2 top-2">
                <VoiceInput
                    onTranscriptReceived={handleTranscript}
                    buttonVariant="ghost"
                    buttonSize="sm"
                    tooltipSide="left"
                />
            </div>
        </div>
    );
});

VoiceEnabledTextarea.displayName = "VoiceEnabledTextarea";

export default VoiceEnabledTextarea;
