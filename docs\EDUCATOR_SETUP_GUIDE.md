# Educator Setup Guide

## Quick Start: Adding New Educators

### Step 1: Admin Creates Educator Account

1. **Access CMS Admin Panel:**
   - Go to `http://localhost:3001/admin`
   - Login with admin credentials

2. **Create New User:**
   - Navigate to **Users** → **Create New**
   - Fill in the form:
     ```
     First Name: [Ed<PERSON>tor's first name]
     Last Name: [<PERSON><PERSON><PERSON>'s last name]
     Email: [<EMAIL>]
     Password: [Secure password]
     Role: "Educator" or "Content Creator"
     Bio: [Brief description of their expertise]
     Specialties: [Select relevant areas: Art, Story, Music, etc.]
     Is Active: ✅ (checked)
     ```

3. **Save and Share Credentials:**
   - Click **Save**
   - Share login credentials with the educator

### Step 2: Educator First Login

1. **Access CMS:**
   - Go to `http://localhost:3001/admin`
   - Use provided credentials to login

2. **Familiarize with Interface:**
   - **Dashboard**: Overview of content and activity
   - **Challenges**: Create and manage educational challenges
   - **Media**: Upload images, videos, and other assets
   - **Profile**: Update personal information

### Step 3: Creating First Challenge

1. **Navigate to Challenges:**
   - Click **Challenges** in the sidebar
   - Click **Create New**

2. **Fill Required Fields:**
   ```
   Title: "My First Creative Challenge"
   Slug: "my-first-creative-challenge" (auto-generated)
   Description: "A brief description of what students will create"
   Category: [Select: story, art, music, game, etc.]
   Age Group: [Select appropriate age ranges]
   Difficulty: [easy, medium, hard]
   Estimated Time: [Time in minutes]
   ```

3. **Add Detailed Instructions:**
   ```
   Instructions: 
   "Step-by-step guide for students:
   1. First, think about...
   2. Then, create...
   3. Finally, share..."
   ```

4. **Set Learning Objectives:**
   - Click **Add Learning Objective**
   - Add 2-3 specific learning goals

5. **Add Required Materials:**
   - Click **Add Material**
   - List what students need (paper, pencils, etc.)
   - Mark if materials are optional

6. **Configure Settings:**
   ```
   Subscription Tier: "free" or "premium"
   Featured: ✅ (if you want it highlighted)
   Status: "published" (to make it live)
   Published At: [Current date/time]
   ```

7. **Save Challenge:**
   - Click **Save**
   - Challenge appears in main app immediately!

## Role Permissions

### What Educators Can Do:
- ✅ Create new challenges
- ✅ Edit their own challenges
- ✅ View all published challenges
- ✅ Upload media files
- ✅ Update their profile

### What Educators Cannot Do:
- ❌ Create or manage other users
- ❌ Edit challenges created by others
- ❌ Delete any challenges
- ❌ Access system settings
- ❌ View unpublished challenges by others

## Best Practices for Educators

### Content Creation Tips:

1. **Clear Instructions:**
   - Use numbered steps
   - Include examples
   - Specify expected outcomes

2. **Age-Appropriate Content:**
   - Match difficulty to age group
   - Use appropriate vocabulary
   - Consider attention spans

3. **Engaging Descriptions:**
   - Start with a hook
   - Explain the fun aspect
   - Mention what they'll learn

4. **Realistic Time Estimates:**
   - Test the activity yourself
   - Add buffer time for setup
   - Consider different skill levels

### Quality Guidelines:

1. **Learning Objectives:**
   - Be specific and measurable
   - Align with educational standards
   - Focus on creativity and critical thinking

2. **Materials List:**
   - Include everything needed
   - Suggest alternatives for expensive items
   - Mark optional items clearly

3. **Safety Considerations:**
   - Mention any safety requirements
   - Suggest adult supervision when needed
   - Avoid potentially harmful activities

## Troubleshooting

### Common Issues:

1. **Challenge Not Appearing in Main App:**
   - Check that Status is set to "published"
   - Verify all required fields are filled
   - Refresh the main app dashboard

2. **Cannot Edit Challenge:**
   - Ensure you're the creator of the challenge
   - Check that you're logged in with correct account
   - Contact admin if you need to edit someone else's content

3. **Media Upload Issues:**
   - Check file size (max 10MB)
   - Use supported formats (JPG, PNG, MP4, etc.)
   - Ensure stable internet connection

4. **Login Problems:**
   - Verify credentials with admin
   - Check that account is marked as "Active"
   - Clear browser cache and try again

### Getting Help:

1. **Contact Admin:**
   - For account issues
   - For permission problems
   - For technical difficulties

2. **Documentation:**
   - Refer to this guide
   - Check the main architecture document
   - Review CMS interface tooltips

## Content Workflow

### Typical Educator Workflow:

```
1. Login to CMS → 
2. Plan Challenge → 
3. Create Draft → 
4. Test Instructions → 
5. Add Media/Materials → 
6. Set to Published → 
7. Monitor in Main App → 
8. Iterate Based on Feedback
```

### Content Lifecycle:

1. **Draft**: Work in progress, not visible to students
2. **Published**: Live in main app, visible to all users
3. **Updates**: Edit published content as needed
4. **Archive**: Contact admin to remove outdated content

## Success Metrics

### Track Your Impact:
- Monitor which challenges are popular
- See how students engage with your content
- Get feedback from main app users
- Collaborate with other educators

### Continuous Improvement:
- Update challenges based on student feedback
- Create series of related challenges
- Experiment with different formats
- Share best practices with other educators

---

**Need Help?** Contact the admin or refer to the main User Management Architecture document for more detailed information about the system.
