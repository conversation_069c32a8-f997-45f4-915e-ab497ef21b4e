import Head from "next/head";

interface PageSEOProps {
    title: string;
    description: string;
    path: string;
}

const PageSEO = ({ title, description, path }: PageSEOProps) => {
    const siteURL = "https://littlespark.ai";
    const fullURL = `${siteURL}${path}`;

    return (
        <Head>
            <title>{title} | Little Spark</title>
            <meta name="description" content={description} />
            <meta property="og:title" content={`${title} | Little Spark`} />
            <meta property="og:description" content={description} />
            <meta property="og:url" content={fullURL} />
            <meta property="og:type" content="website" />
            <meta name="twitter:card" content="summary_large_image" />
            <meta name="twitter:title" content={`${title} | Little Spark`} />
            <meta name="twitter:description" content={description} />
            <link rel="canonical" href={fullURL} />
        </Head>
    );
};

export default PageSEO;
