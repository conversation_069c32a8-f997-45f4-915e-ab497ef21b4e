"use client";
import React, { useState } from 'react';
import { AspectRatio } from '@/components/ui/aspect-ratio';
import ArtPlaceholder from './ArtPlaceholder';
import ArtGenerating from './ArtGenerating';
import Image from 'next/image';

interface ArtPreviewProps {
  generatedImage: string | null;
  isGenerating: boolean;
  aspectRatio: string;
}

const ArtPreview = ({ generatedImage, isGenerating, aspectRatio }: ArtPreviewProps) => {
  const [imageError, setImageError] = useState(false);
  
  // Convert aspect ratio string to number (e.g. "16:9" to 16/9)
  const getRatioValue = (ratio: string): number => {
    const [width, height] = ratio.split(':').map(Number);
    return width / height;
  };

  // Reset error state when a new image is generated
  React.useEffect(() => {
    if (generatedImage) {
      setImageError(false);
    }
  }, [generatedImage]);

  return (
    <div className="bg-gray-100 rounded-lg overflow-hidden">
      <AspectRatio ratio={getRatioValue(aspectRatio)} className="w-full">
        {isGenerating ? (
          <ArtGenerating />
        ) : generatedImage && generatedImage.length > 0 && !imageError ? (
          <Image 
            src={generatedImage}
            alt="Generated artwork"
            fill
            sizes="100vw"
            className="object-cover"
            onError={() => {
              console.log("Image failed to load, using fallback image");
              setImageError(true);
            }}
          />
        ) : imageError ? (
          <div className="flex flex-col items-center justify-center h-full bg-gray-200 p-4">
            <Image
              src={exampleImages[0]}
              alt="Placeholder artwork"
              fill
              sizes="100vw"
              className="object-cover rounded-lg"
            />
            <p className="text-sm text-gray-500 mt-2">Using placeholder image</p>
          </div>
        ) : (
          <ArtPlaceholder />
        )}
      </AspectRatio>
    </div>
  );
};

// Fallback images if the generated image fails to load
const exampleImages = [
  "https://images.unsplash.com/photo-1579546929518-9e396f3cc809?q=80&w=1470&auto=format&fit=crop",
  "https://images.unsplash.com/photo-1557672172-298e090bd0f1?q=80&w=1587&auto=format&fit=crop"
];

export default ArtPreview;
