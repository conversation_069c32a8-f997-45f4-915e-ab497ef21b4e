# Supabase Setup Guide

## 1. Create Supabase Project

1. Go to [supabase.com](https://supabase.com)
2. Create a new account or sign in
3. Create a new project
4. Wait for the project to be set up

## 2. Get Your Credentials

1. Go to your project dashboard
2. Navigate to Settings > API
3. Copy your Project URL and anon/public key

## 3. Configure Environment Variables

Update your `.env.local` file with your Supabase credentials:

```env
NEXT_PUBLIC_SUPABASE_URL=your_project_url_here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
```

## 4. Set Up Database Schema

1. Go to your Supabase dashboard
2. Navigate to SQL Editor
3. Run the SQL script from `supabase-schema.sql`

This will create:
- `profiles` table for user data
- Row Level Security policies
- Triggers for automatic profile creation
- Updated timestamp handling

## 5. Configure Authentication

1. Go to Authentication > Settings in your Supabase dashboard
2. Configure your site URL (e.g., `http://localhost:3000`)
3. Add redirect URLs:
   - `http://localhost:3000/auth/callback` (for development)
   - Your production URL with `/auth/callback` (for production)
4. Configure email templates if desired
5. Make sure "Enable email confirmations" is enabled for magic link authentication

## 6. Test the Setup

1. Start your development server: `npm run dev`
2. Navigate to `/auth` 
3. Try the magic link sign up with your email
4. Check your email for the magic link
5. Click the link to authenticate
6. Test the magic link sign in for existing users

## Project Structure

```
src/
├── lib/
│   ├── supabase/
│   │   └── client.ts          # Supabase client configuration
│   └── auth/
│       └── auth-service.ts    # Authentication service
├── hooks/
│   └── useAuth.ts             # Authentication hook
├── types/
│   └── supabase.ts            # TypeScript types
├── middleware.ts              # Route protection
└── app/
    ├── auth/
    │   ├── page.tsx           # Unified auth page with tabs
    │   └── callback/
    │       └── route.ts       # Auth callback handler
    └── page.tsx               # Home page with auth state
```

## Features Included

- ✅ Magic link authentication (passwordless)
- ✅ Tabbed auth interface (Sign In/Sign Up)
- ✅ User registration and login
- ✅ Protected routes with middleware
- ✅ Automatic profile creation
- ✅ TypeScript support throughout
- ✅ Row Level Security policies
- ✅ Real-time auth state management
- ✅ Clean error handling and loading states
- ✅ Modern UI matching the design specifications

## Next Steps

You can now extend this setup by:
- Adding password reset functionality
- Creating protected dashboard pages
- Adding user profile management
- Implementing social authentication
- Adding more database tables and relationships 