import React from 'react';
import { Accordion } from '@/components/ui/Accordion';
import FAQItem from './FAQItem';
import { faqData, faqContent } from '@/lib/constants';
import { fredoka } from '@/lib/fonts';

const FAQSection = () => {
  return (
    <section id="faq-section" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12 max-w-3xl mx-auto">
          <h2 className={`text-3xl md:text-4xl font-bold mb-5 text-gray-800 ${fredoka.className}`}>
            {faqContent.title}
          </h2>
          <p className="text-gray-600 text-lg">
            {faqContent.subtitle}
          </p>
        </div>
        
        <div className="max-w-3xl mx-auto">
          <Accordion type="single" collapsible className="w-full">
            {faqData.map((faq, index) => (
              <FAQItem 
                key={index}
                question={faq.question}
                answer={faq.answer}
                index={index}
              />
            ))}
          </Accordion>
        </div>
      </div>
    </section>
  );
};

export default FAQSection; 