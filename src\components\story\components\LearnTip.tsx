import React from "react";
import { Lightbulb } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";

interface LearnTipProps {
    tip: string;
    subject?: string;
}

const LearnTip = ({ tip, subject }: LearnTipProps) => {
    return (
        <Card className="w-full max-w-2xl bg-blue-50 border-blue-200">
            <CardContent className="p-4">
                <div className="flex items-start gap-3">
                    <Lightbulb className="h-5 w-5 text-blue-500 mt-1 flex-shrink-0" />
                    <div>
                        {subject && (
                            <h4 className="font-semibold text-blue-700 mb-1">
                                {subject}
                            </h4>
                        )}
                        <p className="text-blue-600 text-sm">{tip}</p>
                    </div>
                </div>
            </CardContent>
        </Card>
    );
};

export default LearnTip;
