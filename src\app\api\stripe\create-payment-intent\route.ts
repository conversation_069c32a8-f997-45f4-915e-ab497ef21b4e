import { NextRequest, NextResponse } from 'next/server';
import { stripe, STRIPE_PLANS } from '@/lib/stripe';
import { prisma } from '@/lib/prisma';

export async function POST(request: NextRequest) {
  try {
    if (!stripe) {
      return NextResponse.json({ error: 'Stripe not configured' }, { status: 500 });
    }

    const { planId, email, customerName, userId, promotionalCode, discountAmount, finalAmount } = await request.json();

    if (!planId || !email) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    const plan = STRIPE_PLANS[planId as keyof typeof STRIPE_PLANS];
    if (!plan) {
      return NextResponse.json({ error: 'Invalid plan ID' }, { status: 400 });
    }

    // Check if Stripe is properly configured for this plan
    if (!plan.priceId) {
      // For development/testing, create a simple payment intent
      if (process.env.NODE_ENV === 'development') {
        // Use promotional code discount if provided, otherwise use plan amount
        const paymentAmount = finalAmount !== undefined ? Math.round(finalAmount * 100) : plan.amount;

        const paymentIntent = await stripe.paymentIntents.create({
          amount: paymentAmount,
          currency: 'usd',
          metadata: {
            planId: planId,
            email: email,
            customerName: customerName || '',
            userId: userId || '',
            testMode: 'true',
            promotional_code: promotionalCode || '',
            original_amount: plan.amount.toString(),
            discount_amount: discountAmount ? Math.round(discountAmount * 100).toString() : '0',
            final_amount: paymentAmount.toString()
          }
        });

        return NextResponse.json({
          clientSecret: paymentIntent.client_secret,
          testMode: true,
        });
      }

      return NextResponse.json(
        { error: `Price ID not configured for plan: ${planId}. Please set STRIPE_${planId.toUpperCase().replace('-', '_')}_PRICE_ID in your environment variables.` },
        { status: 500 }
      );
    }

    // Find or create profile to check trial history
    let profile = await prisma.profile.findUnique({
      where: { email }
    });

    if (!profile && userId) {
      // If we have a userId but no profile by email, try to find by userId
      profile = await prisma.profile.findUnique({
        where: { id: userId }
      });
    }

    // Create profile if it doesn't exist
    if (!profile) {
      const profileId = userId || crypto.randomUUID();
      profile = await prisma.profile.create({
        data: {
          id: profileId,
          email,
          full_name: customerName,
          trial_used: false, // New users start with trial available
          created_at: new Date(),
          updated_at: new Date()
        }
      });
    } else if (!profile.full_name && customerName) {
      // Update name if not set
      await prisma.profile.update({
        where: { id: profile.id },
        data: { 
          full_name: customerName,
          updated_at: new Date()
        }
      });
    }

    // TRIAL SYSTEM DISABLED: Always create direct subscription
    console.log('Creating direct subscription for user:', {
      email,
      profileExists: !!profile,
      planId,
      promotionalCode: promotionalCode || 'none',
      finalAmount: finalAmount || plan.amount
    });

    // Create or retrieve Stripe customer
    let customer;
    const existingCustomers = await stripe.customers.list({
      email: email,
      limit: 1,
    });

    if (existingCustomers.data.length > 0) {
      customer = existingCustomers.data[0];
    } else {
      customer = await stripe.customers.create({
        email: email,
        name: customerName || undefined,
      });
    }

    // Update profile with Stripe customer ID
    if (profile && !profile.stripe_customer_id) {
      await prisma.profile.update({
        where: { id: profile.id },
        data: { stripe_customer_id: customer.id }
      });
    }

    // TRIAL SYSTEM DISABLED: Always create direct payment intent
    {
      // Create direct paid subscription (no trial)
      console.log('Creating DIRECT PAID subscription for returning user:', { 
        customer: customer.id, 
        priceId: plan.priceId, 
        planId,
        reason: hasUsedTrial ? 'trial_already_used' : 'has_payment_history'
      });

      const paymentIntent = await stripe.paymentIntents.create({
        amount: plan.amount,
        currency: 'usd',
        customer: customer.id,
        setup_future_usage: 'off_session',
        metadata: {
          planId: planId,
          email: email,
          customerName: customerName || '',
          userId: profile?.id || '',
          direct_subscription: 'true'
        }
      });

      console.log('Direct payment intent created:', paymentIntent.id);
      console.log('Amount:', plan.amount / 100, 'USD');

      return NextResponse.json({
        clientSecret: paymentIntent.client_secret,
        paymentIntentId: paymentIntent.id,
        customerId: customer.id,
        setupIntent: false,
        requiresPaymentMethod: true,
        isTrialSubscription: false,
        amount: plan.amount / 100
      });
    }
  } catch (error) {
    console.error('Error creating payment intent:', error);
    return NextResponse.json(
      { error: 'Failed to create payment intent' },
      { status: 500 }
    );
  }
} 