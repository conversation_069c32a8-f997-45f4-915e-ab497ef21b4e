# CMS Setup Guide

## Current Status
- ✅ CMS is running on http://localhost:3001
- ✅ Main app API is working with fallback data
- ⚠️ CMS database needs initialization

## To Set Up CMS Properly:

### Option 1: Create First User via Admin Interface
1. Go to http://localhost:3001/admin
2. Click "Create First User" 
3. Fill in details:
   - Email: <EMAIL>
   - Password: admin123
   - First Name: Admin
   - Last Name: User
   - Role: Super Admin

### Option 2: Reset Database (if needed)
1. Stop CMS server
2. Delete `cms.db` file
3. Restart CMS server
4. Go to admin interface and create first user

### Option 3: Use Payload CLI
```bash
cd littlespark-cms
npx payload create-first-user
```

## After CMS is Set Up:
1. Login to admin interface
2. Go to Collections > Challenges
3. Create new challenges
4. Set status to "Published"
5. Main app will automatically fetch from CMS

## Current Fallback Challenges:
The main app shows 6 fallback challenges when C<PERSON> is unavailable:
- Magical Forest Story (Story)
- Ocean Adventure Story (Story) 
- Friendly Dragon Art (Art)
- Celebration Music (Music)
- Adventure Quest Game (Game)
- Space Explorer Story (Story)

## Testing:
- Dashboard: http://localhost:3000/dashboard
- CMS Admin: http://localhost:3001/admin
- API Test: curl http://localhost:3000/api/cms/challenges
