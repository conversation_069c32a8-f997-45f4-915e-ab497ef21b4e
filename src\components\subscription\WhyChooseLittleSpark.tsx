import { fredoka } from "@/lib/fonts";
import React from "react";

const WhyChooseLittleSpark = () => {
  const benefits = [
    {
      title: "Safe & Kid-friendly",
      description: "Our AI is built with safety as the top priority, with content filters and parental controls.",
      icon: "✓"
    },
    {
      title: "Educational Value",
      description: "All our creative tools are designed to boost imagination, problem-solving, and literacy skills.",
      icon: "✓"
    },
    {
      title: "Cancel Anytime",
      description: "No long-term commitments. You can cancel your subscription anytime without any penalties.",
      icon: "✓"
    }
  ];

  return (
    <div className="max-w-6xl mx-auto mt-24 mb-16">
      <h2 className="text-3xl font-quicksand font-bold text-center text-gray-900 mb-12">
        Why Choose Little Spark?
      </h2>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {benefits.map((benefit, index) => (
          <div key={index} className="text-center p-6 border border-gray-200 rounded-2xl">
            <div className="w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-emerald-600 text-2xl font-bold">{benefit.icon}</span>
            </div>
            <h3 className={`text-xl font-bold mb-3 text-gray-900 ${fredoka.className}`}>{benefit.title}</h3>
            <p className="text-gray-600">{benefit.description}</p>
          </div>
        ))}
      </div>
    </div>
  );
};

export default WhyChooseLittleSpark; 