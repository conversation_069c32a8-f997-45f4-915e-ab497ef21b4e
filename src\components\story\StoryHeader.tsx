"use client";
import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft, HelpCircle, Shield, BookOpen } from "lucide-react";
import Link from "next/link";
import { useStory } from "./StoryContext";
import { fredoka } from "@/lib/fonts";
import LearningModeToggle from "../shared/LearningModeToggle";

const StoryHeader = () => {
    const { learningMode, toggleLearningMode, setHelpDialogOpen } = useStory();

    return (
        <div className="mb-8">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 sm:gap-0 mb-4">
                <Link href="/dashboard">
                    <Button
                        variant="ghost"
                        className={`${fredoka.className} text-gray-600 hover:text-gray-900 font-bold text-lg sm:text-xl px-0 sm:px-4`}
                    >
                        <ArrowLeft className="h-4 w-4 mr-2" />
                        <span className="hidden sm:inline">Back to</span>{" "}
                        Dashboard
                    </Button>
                </Link>

                <div className="flex gap-2 w-full sm:w-auto justify-end">
                    

                    <Button
                        variant="outline"
                        className="!border-[#00BFA5] !text-[#00BFA5] hover:!bg-littlespark-primary hover:!text-white rounded-full"
                        onClick={() => setHelpDialogOpen(true)}
                    >
                        <HelpCircle className="h-4 w-4" />
                        <span className="hidden sm:inline ml-2">
                            How to Use
                        </span>
                    </Button>

                    <LearningModeToggle
                        learningMode={learningMode}
                        toggleLearningMode={toggleLearningMode}
                    />
                </div>
            </div>

            <div className="flex items-center gap-2 mb-4">
                <BookOpen className="h-5 w-5 sm:h-6 sm:w-6 text-[#9747FF]" />
                <h1
                    className={`${fredoka.className} text-xl sm:text-2xl md:text-3xl font-bold`}
                >
                    Interactive Story Builder
                </h1>
            </div>

            <div className="flex items-center gap-2 text-gray-600">
                <Shield className="h-4 w-4 flex-shrink-0" />
                <span className="text-xs sm:text-sm">
                    Safe Mode is active - all stories will be kid-friendly
                </span>
            </div>
        </div>
    );
};

export default StoryHeader;
