import React from "react";
import { fredoka } from "@/lib/fonts";
import PrivacyQuestion from "./PrivacyQuestion";

const PrivacyFAQ = () => {
    return (
        <div className="text-left text-gray-900">
            <h2 className={`text-2xl font-bold mb-6 ${fredoka.className}`}>
                Frequently Asked Privacy Questions
            </h2>

            <div className="space-y-6 mb-12">
                <PrivacyQuestion
                    question="What happens to the content my child creates?"
                    answer="Content created in Little Spark is saved to your child's personal dashboard and remains accessible there as long as you have an active subscription. We recommend downloading and saving content to your device as a backup. Your data is handled according to the privacy and data retention policies of our AI service providers (OpenAI and Runware)."
                />

                <PrivacyQuestion
                    question="Which AI services do you use and why?"
                    answer="We use OpenAI (for story, art, and coloring page generation) and Runware (for video creation). These services were carefully selected for their high-quality AI models, robust privacy controls, and commitment to responsible AI development."
                />

                <PrivacyQuestion
                    question="Is my child's data used to train AI models?"
                    answer="Absolutely not. We have strict contractual agreements with OpenAI and Runware to ensure that your child's uploads, prompts, and interactions are never used for training their AI models. This is a core part of our privacy commitment."
                />

                <PrivacyQuestion
                    question="Can I use this tool offline?"
                    answer="Most creative features require an internet connection to access our AI services. However, once content is generated and downloaded, you can view, share, and enjoy it entirely offline."
                />

                <PrivacyQuestion
                    question="Is an account required to use the app?"
                    answer="Yes, an account is required to access and use our creative tools. Our subscription-based model ensures that you can create, save, and manage your child's creative projects securely. An account provides access to features like project saving, creation history, and personalized experiences."
                />
            </div>
        </div>
    );
};

export default PrivacyFAQ;
