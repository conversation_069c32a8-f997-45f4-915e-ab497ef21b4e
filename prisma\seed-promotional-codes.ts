import { PrismaClient } from '../src/generated/prisma';

const prisma = new PrismaClient();

async function seedPromotionalCodes() {
  console.log('🌱 Seeding promotional codes...');

  // Create ALPHA100 promotional code
  const alpha100Code = await prisma.promotionalCode.upsert({
    where: { code: 'ALPHA100' },
    update: {
      discount_type: 'percentage',
      discount_value: 100, // 100% discount
      max_uses: null, // Unlimited uses for now
      is_active: true,
      description: 'Alpha launch promotional code - 100% discount on all subscription plans',
      valid_until: null, // No expiration for now
    },
    create: {
      code: 'ALPHA100',
      discount_type: 'percentage',
      discount_value: 100, // 100% discount
      max_uses: null, // Unlimited uses for now
      is_active: true,
      description: 'Alpha launch promotional code - 100% discount on all subscription plans',
      valid_until: null, // No expiration for now
    },
  });

  console.log('✅ Created/Updated promotional code:', alpha100Code);

  // Create additional promotional codes for testing
  const testCodes = [
    {
      code: 'SAVE50',
      discount_type: 'percentage',
      discount_value: 50,
      max_uses: 100,
      description: '50% discount promotional code',
    },
    {
      code: 'WELCOME25',
      discount_type: 'percentage', 
      discount_value: 25,
      max_uses: 500,
      description: 'Welcome discount - 25% off',
    },
    {
      code: 'FIXED10',
      discount_type: 'fixed_amount',
      discount_value: 10.00,
      max_uses: 50,
      description: '$10 off any subscription',
    }
  ];

  for (const codeData of testCodes) {
    const code = await prisma.promotionalCode.upsert({
      where: { code: codeData.code },
      update: codeData,
      create: codeData,
    });
    console.log('✅ Created/Updated promotional code:', code.code);
  }

  console.log('🎉 Promotional codes seeding completed!');
}

seedPromotionalCodes()
  .catch((e) => {
    console.error('❌ Error seeding promotional codes:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
