import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from './useAuth';
import { UserApiService, type Profile } from '@/lib/api';

// Re-export Profile type for convenience
export type { Profile } from '@/lib/api';

export function useProfile() {
  const { user } = useAuth();
  const router = useRouter();
  const [profile, setProfile] = useState<Profile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch profile data
  const fetchProfile = useCallback(async () => {
    if (!user) {
      setProfile(null);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const profile = await UserApiService.getProfile();
      setProfile(profile);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      router.push('/auth'); // Redirect to auth page on error
    } finally {
      setLoading(false);
    }
  }, [user, router]);

  // Update profile data
  const updateProfile = async (updates: { full_name?: string; avatar_url?: string }) => {
    try {
      setError(null);

      const profile = await UserApiService.updateProfile(updates);
      setProfile(profile);
      return { success: true, profile };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    }
  };

  // Fetch profile when user changes
  useEffect(() => {
    fetchProfile();
  }, [fetchProfile]);

  return {
    profile,
    loading,
    error,
    updateProfile,
    refetch: fetchProfile,
  };
} 