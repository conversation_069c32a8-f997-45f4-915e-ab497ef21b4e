"use client";
import React, { createContext, useState, useContext, ReactNode } from "react";

export type StoryStage = "beginning" | "middle" | "end";

interface ChallengeData {
    id: string;
    title: string;
    description: string;
    instructions: string;
    category: string;
    difficulty: string;
    estimatedTime: number;
    learningObjectives: Array<{ objective: string }>;
    materials: Array<{ material: string }>;
}

interface StoryContextType {
    title: string;
    setTitle: (title: string) => void;
    content: string;
    setContent: (content: string) => void;
    isGenerating: boolean;
    setIsGenerating: (isGenerating: boolean) => void;
    learningMode: boolean;
    toggleLearningMode: () => void;
    storyGenre: string;
    setStoryGenre: (genre: string) => void;
    characterIdea: string;
    setCharacterIdea: (idea: string) => void;
    storyTheme: string;
    setStoryTheme: (theme: string) => void;
    showOptions: boolean;
    setShowOptions: (show: boolean) => void;
    storyOptions: string[];
    setStoryOptions: (options: string[]) => void;
    storyStage: StoryStage;
    setStoryStage: (stage: StoryStage) => void;
    hasAskedForHelp: boolean;
    setHasAskedForHelp: (asked: boolean) => void;
    lastFormat: string;
    challengeData: ChallengeData | null;
    setChallengeData: (data: ChallengeData | null) => void;
    setLastFormat: (format: string) => void;
    helpDialogOpen: boolean;
    setHelpDialogOpen: (open: boolean) => void;
    readerAge: number;
    setReaderAge: (age: number) => void;
}

const StoryContext = createContext<StoryContextType | undefined>(undefined);

export const useStory = () => {
    const context = useContext(StoryContext);
    if (!context) {
        throw new Error("useStory must be used within a StoryProvider");
    }
    return context;
};

interface StoryProviderProps {
    children: ReactNode;
}

export const StoryProvider = ({ children }: StoryProviderProps) => {
    const [title, setTitle] = useState("");
    const [content, setContent] = useState("");
    const [isGenerating, setIsGenerating] = useState(false);
    const [learningMode, setLearningMode] = useState(true);
    const [storyGenre, setStoryGenre] = useState("");
    const [characterIdea, setCharacterIdea] = useState("");
    const [storyTheme, setStoryTheme] = useState("");
    const [showOptions, setShowOptions] = useState(false);
    const [storyOptions, setStoryOptions] = useState<string[]>([]);
    const [storyStage, setStoryStage] = useState<StoryStage>("beginning");
    const [hasAskedForHelp, setHasAskedForHelp] = useState(false);
    const [lastFormat, setLastFormat] = useState("");
    const [helpDialogOpen, setHelpDialogOpen] = useState(false);
    const [readerAge, setReaderAge] = useState(8);
    const [challengeData, setChallengeData] = useState<ChallengeData | null>(null);

    const toggleLearningMode = () => {
        setLearningMode(!learningMode);
    };

    const value = {
        title,
        setTitle,
        content,
        setContent,
        isGenerating,
        setIsGenerating,
        learningMode,
        toggleLearningMode,
        storyGenre,
        setStoryGenre,
        characterIdea,
        setCharacterIdea,
        storyTheme,
        setStoryTheme,
        showOptions,
        setShowOptions,
        storyOptions,
        setStoryOptions,
        storyStage,
        setStoryStage,
        hasAskedForHelp,
        setHasAskedForHelp,
        lastFormat,
        setLastFormat,
        helpDialogOpen,
        setHelpDialogOpen,
        readerAge,
        setReaderAge,
        challengeData,
        setChallengeData,
    };

    return (
        <StoryContext.Provider value={value}>{children}</StoryContext.Provider>
    );
};
