import { <PERSON>tor<PERSON>haracter } from '@/components/dashboard/ai-mentor/types';

export interface CreativeToolSuggestion {
  type: 'story' | 'art' | 'music' | 'general';
  title: string;
  suggestion: string;
  actionText: string;
  actionUrl: string;
}

/**
 * Get creative tool suggestions based on user message and character
 */
export const getCreativeToolSuggestions = (
  userMessage: string,
  character: <PERSON><PERSON><PERSON>hara<PERSON>
): CreativeToolSuggestion[] => {
  const suggestions: CreativeToolSuggestion[] = [];
  const message = userMessage.toLowerCase();

  // Story suggestions
  if (message.includes('story') || message.includes('tale') || message.includes('character')) {
    suggestions.push({
      type: 'story',
      title: 'Story Creator',
      suggestion: getCharacterSpecificStorySuggestion(character),
      actionText: 'Start Writing',
      actionUrl: '/create/story'
    });
  }

  // Art suggestions
  if (message.includes('art') || message.includes('draw') || message.includes('paint') || message.includes('color')) {
    suggestions.push({
      type: 'art',
      title: 'Art Studio',
      suggestion: getCharacterSpecificArtSuggestion(character),
      actionText: 'Create Art',
      actionUrl: '/create/art'
    });
  }

  // Music suggestions
  if (message.includes('music') || message.includes('song') || message.includes('melody') || message.includes('sound')) {
    suggestions.push({
      type: 'music',
      title: 'Music Composer',
      suggestion: getCharacterSpecificMusicSuggestion(character),
      actionText: 'Make Music',
      actionUrl: '/create/music'
    });
  }

  // If no specific tool matches, suggest the most popular one
  if (suggestions.length === 0) {
    suggestions.push({
      type: 'general',
      title: 'Creative Dashboard',
      suggestion: getCharacterSpecificGeneralSuggestion(character),
      actionText: 'Explore Tools',
      actionUrl: '/dashboard'
    });
  }

  return suggestions;
};

function getCharacterSpecificStorySuggestion(character: MentorCharacter): string {
  switch (character) {
    case 'robot':
      return "My circuits are sparking with story ideas! Let's build an amazing adventure together.";
    case 'owl':
      return "Hoot! Stories are wonderful for learning and imagination. What tale shall we weave?";
    case 'explorer':
      return "Every great adventure starts with a story! Ready to chart new creative territories?";
    default:
      return "Let's create an amazing story together!";
  }
}

function getCharacterSpecificArtSuggestion(character: MentorCharacter): string {
  switch (character) {
    case 'robot':
      return "My visual processors are ready! Let's design something spectacular with code and creativity.";
    case 'owl':
      return "Art is a wonderful way to express knowledge and beauty. What shall we create?";
    case 'explorer':
      return "Art lets us capture the wonders we discover! Ready to paint new worlds?";
    default:
      return "Let's create some beautiful art together!";
  }
}

function getCharacterSpecificMusicSuggestion(character: MentorCharacter): string {
  switch (character) {
    case 'robot':
      return "My audio synthesizers are humming! Let's compose some electronic magic.";
    case 'owl':
      return "Music is the mathematics of emotion! What harmonious creation calls to you?";
    case 'explorer':
      return "Music is the universal language of adventure! Let's compose our journey's soundtrack.";
    default:
      return "Let's make some beautiful music together!";
  }
}

function getCharacterSpecificGeneralSuggestion(character: MentorCharacter): string {
  switch (character) {
    case 'robot':
      return "My creative databases are full of possibilities! Let's explore all the amazing tools we have.";
    case 'owl':
      return "Wisdom comes from exploring many paths. Shall we discover what creative tools await?";
    case 'explorer':
      return "Every tool is a new territory to explore! Ready for a creative expedition?";
    default:
      return "Let's explore all the creative tools available!";
  }
}

/**
 * Get context-aware creative suggestions based on conversation history
 */
export const getContextualSuggestions = (
  chatHistory: { role: 'user' | 'ai'; content: string }[],
  character: MentorCharacter
): CreativeToolSuggestion[] => {
  const recentMessages = chatHistory.slice(-5);
  const allText = recentMessages.map(m => m.content).join(' ').toLowerCase();
  
  // Analyze conversation for creative themes
  const themes = {
    story: ['story', 'character', 'adventure', 'tale', 'plot', 'hero', 'princess', 'dragon'],
    art: ['draw', 'paint', 'color', 'picture', 'art', 'sketch', 'design', 'beautiful'],
    music: ['sing', 'song', 'music', 'melody', 'dance', 'rhythm', 'instrument', 'sound']
  };
  
  const suggestions: CreativeToolSuggestion[] = [];
  
  Object.entries(themes).forEach(([type, keywords]) => {
    const matchCount = keywords.filter(keyword => allText.includes(keyword)).length;
    if (matchCount >= 2) { // If multiple related keywords found
      const suggestion = getCreativeToolSuggestions(allText, character)
        .find(s => s.type === type);
      if (suggestion) {
        suggestions.push(suggestion);
      }
    }
  });
  
  return suggestions.slice(0, 2); // Return top 2 contextual suggestions
};

/**
 * Character-specific encouragement phrases for tool suggestions
 */
export const getCharacterEncouragement = (character: MentorCharacter): string[] => {
  const encouragements = {
    robot: [
      "My creativity circuits are at full power!",
      "System scan complete - you're amazing!",
      "Processing... wow, you're so creative!",
      "My databases show you're incredibly talented!"
    ],
    owl: [
      "Hoot! Your wisdom and creativity shine bright!",
      "Your imagination is a treasure to behold!",
      "What wonderful curiosity you possess!",
      "Your creative spirit soars like an owl in flight!"
    ],
    explorer: [
      "Your creative courage is stellar!",
      "What boldness in your artistic vision!",
      "You're charting new creative galaxies!",
      "Your imagination knows no boundaries!"
    ]
  };
  
  return encouragements[character] || [
    "You're doing great!",
    "Keep up the creative work!",
    "Your imagination is wonderful!"
  ];
}; 