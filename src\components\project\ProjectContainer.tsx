"use client";
import React from "react";
import { ProjectProvider, useProject } from "./ProjectContext";
import ProjectHeader from "./ProjectHeader";
import ProjectTitle from "./ProjectTitle";
import ProjectDescription from "./ProjectDescription";
import ProjectTypeSelector from "./ProjectTypeSelector";
import ProjectContent from "./ProjectContent";
import ProjectActionButtons from "./ProjectActionButtons";
import ProjectHelpDialog from "./ProjectHelpDialog";

const ProjectPageContent = () => {
    const { helpDialogOpen, setHelpDialogOpen } = useProject();

    return (
        <div className="container mx-auto px-4 py-8">
            <div className="max-w-6xl mx-auto">
                <ProjectHeader />
                <div className="mb-6" />
                <ProjectTitle />
                <div className="mb-3" />
                <ProjectDescription />
                <div className="mb-2" />
                <ProjectTypeSelector />
                <div className="mb-6">
                    <h3 className="text-lg font-medium text-center mb-3">
                        Project Content
                    </h3>
                    <ProjectContent />
                </div>
                <ProjectActionButtons />
                <ProjectHelpDialog
                    open={helpDialogOpen}
                    onOpenChange={setHelpDialogOpen}
                />
            </div>
        </div>
    );
};

const ProjectContainer = () => {
    return (
        <ProjectProvider>
            <ProjectPageContent />
        </ProjectProvider>
    );
};

export default ProjectContainer;
