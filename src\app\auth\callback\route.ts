import { createServerSupabaseClient } from '@/lib/supabase/server';
import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url);
  const code = requestUrl.searchParams.get('code');

  if (code) {
    const supabase = await createServerSupabaseClient();

    // Exchange the code for a session
    const { data, error } = await supabase.auth.exchangeCodeForSession(code);

    if (error) {
      console.error('Auth callback error:', error);
      return NextResponse.redirect(`${requestUrl.origin}/auth?error=auth_callback_error`);
    }

    if (data.user) {
      try {
        // Check if profile exists, create if not using Prisma
        const existingProfile = await prisma.profile.findUnique({
          where: { id: data.user.id }
        });

        if (!existingProfile) {
          // Profile doesn't exist, create it with trial available
          await prisma.profile.create({
            data: {
              id: data.user.id,
              email: data.user.email!,
              full_name: data.user.user_metadata?.full_name || null,
              trial_used: false, // Ensure new users can access trial
            }
          });
          console.log(`Created new profile for user ${data.user.id} with trial available`);
        }
      } catch (error) {
        console.error('Error managing profile with Prisma:', error);
        // Continue anyway - user can still be authenticated
      }

      // Redirect to dashboard after successful authentication
      return NextResponse.redirect(`${requestUrl.origin}/dashboard`);
    }
  }

  // If no code or auth failed, redirect to auth page
  return NextResponse.redirect(`${requestUrl.origin}/auth?error=auth_callback_error`);
} 