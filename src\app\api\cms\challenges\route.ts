import { NextRequest, NextResponse } from 'next/server';

const CMS_BASE_URL = process.env.CMS_BASE_URL || 'http://localhost:3001';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    // Build CMS API URL with filters
    const cmsParams = new URLSearchParams();
    cmsParams.append('where[status][equals]', 'published');

    // Forward all query parameters to CMS
    searchParams.forEach((value, key) => {
      if (key !== 'status') { // Don't override status filter
        cmsParams.append(`where[${key}][equals]`, value);
      }
    });

    // Use direct CMS route that bypasses authentication issues
    const cmsUrl = `${CMS_BASE_URL}/my-route`;
    console.log('🔍 [CMS API] Fetching challenges from direct route:', cmsUrl);
    console.log('🔍 [CMS API] Request filters:', Object.fromEntries(searchParams));
    
    const response = await fetch(cmsUrl, {
      headers: {
        'Content-Type': 'application/json',
      },
      // Add timeout
      signal: AbortSignal.timeout(10000), // 10 seconds
    });

    console.log('📡 [CMS API] Response status:', response.status);

    if (!response.ok) {
      console.log('❌ [CMS API] Response not OK:', response.status, response.statusText);
      throw new Error(`CMS API error: ${response.status}`);
    }

    const data = await response.json();
    console.log('📦 [CMS API] Raw response data:', JSON.stringify(data, null, 2));

    // Check if CMS returned an error
    if (data.errors && data.errors.length > 0) {
      console.log('❌ [CMS API] Errors in response:', data.errors);
      throw new Error('CMS returned errors');
    }
    
    console.log('📊 [CMS API] Total challenges found:', data.total);
    console.log('📊 [CMS API] Challenges array length:', data.challenges?.length || 0);

    if (data.challenges && data.challenges.length > 0) {
      console.log('✅ [CMS API] Found challenges from CMS:', data.challenges.map((c: any) => ({ id: c.id, title: c.title, status: c.status })));
    } else {
      console.log('⚠️ [CMS API] No challenges found in CMS response');
    }

    // Transform CMS data for frontend consumption
    const transformedChallenges = data.challenges?.map((challenge: any) => ({
      id: challenge.id,
      title: challenge.title,
      slug: challenge.slug,
      description: challenge.description,
      category: challenge.category,
      type: challenge.category, // Map category to type for compatibility
      ageGroup: challenge.ageGroup,
      difficulty: challenge.difficulty,
      estimatedTime: challenge.estimatedTime,
      instructions: challenge.instructions,
      learningObjectives: challenge.learningObjectives,
      materials: challenge.materials,
      media: challenge.media?.map((m: any) => ({
        ...m,
        file: {
          ...m.file,
          url: m.file.url.startsWith('http') ? m.file.url : `${CMS_BASE_URL}${m.file.url}`
        }
      })),
      subscriptionTier: challenge.subscriptionTier,
      featured: challenge.featured,
      seasonal: challenge.seasonal,
      publishedAt: challenge.publishedAt,
    })) || [];

    console.log('🔄 [CMS API] Transformed challenges count:', transformedChallenges.length);
    console.log('🚀 [CMS API] Returning CMS challenges to frontend');

    return NextResponse.json({
      success: true,
      challenges: transformedChallenges,
      total: data.total || 0,
      page: 1,
      totalPages: 1,
    });
    
  } catch (error) {
    console.log('❌ [CMS API] CMS unavailable, using fallback challenges. Error:', error);

    // Fallback to static challenges if CMS is unavailable
    const fallbackChallenges = [
      {
        id: 'fallback-story-1',
        title: 'Magical Forest Story',
        slug: 'magical-forest-story',
        description: 'Write a short story about a child who discovers a magical forest.',
        category: 'story',
        type: 'story',
        ageGroup: ['6-8'],
        difficulty: 'easy',
        estimatedTime: 30,
        instructions: 'Start with "Once upon a time, in a forest unlike any other..." and describe what magical creatures and adventures await.',
        learningObjectives: [
          { objective: 'Develop creative writing skills' },
          { objective: 'Practice storytelling structure' },
          { objective: 'Expand vocabulary and imagination' }
        ],
        materials: [
          { material: 'Paper and pencil', optional: false },
          { material: 'Colored pencils for illustrations', optional: true }
        ],
        subscriptionTier: 'free',
        featured: true,
        publishedAt: new Date().toISOString(),
      },
      {
        id: 'fallback-story-2',
        title: 'Ocean Adventure Story',
        slug: 'ocean-adventure-story',
        description: 'Write about an underwater adventure with sea creatures.',
        category: 'story',
        type: 'story',
        ageGroup: ['9-11'],
        difficulty: 'easy',
        estimatedTime: 45,
        instructions: 'Create a story about diving deep into the ocean and meeting friendly sea creatures. What treasures will you find?',
        learningObjectives: [
          { objective: 'Learn about marine life' },
          { objective: 'Practice descriptive writing' },
          { objective: 'Develop plot structure' }
        ],
        materials: [
          { material: 'Writing materials', optional: false }
        ],
        subscriptionTier: 'free',
        featured: true,
        publishedAt: new Date().toISOString(),
      },
      {
        id: 'fallback-art-1',
        title: 'Friendly Dragon Art',
        slug: 'friendly-dragon-art',
        description: 'Create artwork of a kind dragon who helps people.',
        category: 'art',
        type: 'art',
        ageGroup: ['6-8'],
        difficulty: 'easy',
        estimatedTime: 40,
        instructions: 'Draw a gentle dragon with sparkling scales and a warm smile. What makes your dragon special and helpful?',
        learningObjectives: [
          { objective: 'Practice drawing techniques' },
          { objective: 'Explore color and creativity' },
          { objective: 'Express imagination through art' }
        ],
        materials: [
          { material: 'Drawing paper', optional: false },
          { material: 'Colored pencils or crayons', optional: false },
          { material: 'Eraser', optional: true }
        ],
        subscriptionTier: 'free',
        featured: true,
        publishedAt: new Date().toISOString(),
      },
      {
        id: 'fallback-music-1',
        title: 'Celebration Music',
        slug: 'celebration-music',
        description: 'Compose a joyful song for a special celebration.',
        category: 'music',
        type: 'music',
        ageGroup: ['9-11'],
        difficulty: 'easy',
        estimatedTime: 35,
        instructions: 'Create a happy melody that makes everyone want to dance and smile. Think about what sounds make you feel joyful!',
        learningObjectives: [
          { objective: 'Understand rhythm and melody' },
          { objective: 'Express emotions through music' },
          { objective: 'Learn basic music composition' }
        ],
        materials: [
          { material: 'Voice or simple instrument', optional: false },
          { material: 'Recording device (optional)', optional: true }
        ],
        subscriptionTier: 'free',
        featured: true,
        publishedAt: new Date().toISOString(),
      },
      {
        id: 'fallback-game-1',
        title: 'Adventure Quest Game',
        slug: 'adventure-quest-game',
        description: 'Design a fun adventure game with challenges and rewards.',
        category: 'game',
        type: 'game',
        ageGroup: ['12-14'],
        difficulty: 'hard',
        estimatedTime: 60,
        instructions: 'Create a game where players go on an exciting quest. Design the characters, challenges, and rewards they will encounter.',
        learningObjectives: [
          { objective: 'Learn game design principles' },
          { objective: 'Practice logical thinking' },
          { objective: 'Develop problem-solving skills' }
        ],
        materials: [
          { material: 'Paper for planning', optional: false },
          { material: 'Computer or tablet', optional: true }
        ],
        subscriptionTier: 'premium',
        featured: false,
        publishedAt: new Date().toISOString(),
      },
      {
        id: 'fallback-story-3',
        title: 'Space Explorer Story',
        slug: 'space-explorer-story',
        description: 'Write about an exciting journey to explore distant planets.',
        category: 'story',
        type: 'story',
        ageGroup: ['12-14'],
        difficulty: 'medium',
        estimatedTime: 50,
        instructions: 'Imagine you are an astronaut discovering new worlds. What alien creatures will you meet? What amazing sights will you see?',
        learningObjectives: [
          { objective: 'Learn about space and science' },
          { objective: 'Practice advanced storytelling' },
          { objective: 'Develop character development skills' }
        ],
        materials: [
          { material: 'Writing materials', optional: false },
          { material: 'Space reference books', optional: true }
        ],
        subscriptionTier: 'premium',
        featured: false,
        publishedAt: new Date().toISOString(),
      }
    ];

    console.log('🔄 [FALLBACK] Returning fallback challenges count:', fallbackChallenges.length);
    console.log('🔄 [FALLBACK] Fallback challenge titles:', fallbackChallenges.map(c => c.title));

    return NextResponse.json({
      success: false,
      challenges: fallbackChallenges,
      error: 'CMS unavailable, showing fallback content',
      total: fallbackChallenges.length,
    });
  }
}
