
import { useState } from 'react';
import { TestimonialFormData } from '../types';

/**
 * Hook for managing testimonial form state
 */
export const useTestimonialForm = () => {
  const [testimonialData, setTestimonialData] = useState<TestimonialFormData>({
    content: '',
    author: '',
    role: '',
    rating: 5
  });
  
  const resetForm = () => {
    setTestimonialData({
      content: '',
      author: '',
      role: '',
      rating: 5
    });
  };

  const updateField = (field: keyof TestimonialFormData, value: string | number) => {
    setTestimonialData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return {
    testimonialData,
    updateField,
    resetForm
  };
};
