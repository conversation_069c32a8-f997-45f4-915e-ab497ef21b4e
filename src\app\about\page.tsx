"use client";

import React from "react";
import { User, Presentation } from "lucide-react";
import SmoothScrolling from "@/components/common/SmoothScrolling";
import LearnTip from "@/components/shared/LearnTip";
import { fredoka } from "@/lib/fonts";
import Image from "next/image";

const About = () => {
    return (
        <div className="min-h-screen bg-white relative">
            {/* Smooth Scrolling */}
            <SmoothScrolling />

            <section className="py-10 bg-white relative z-10">
                <div className="container mx-auto px-4">
                    {/* <PERSON>'s Bio Section */}
                    <div className="flex flex-col lg:flex-row items-center lg:items-start gap-8 lg:gap-12 mb-16 lg:mb-20">
                        <div className="w-full lg:w-1/3 flex justify-center lg:justify-start">
                            <Image
                                src="/images/about1.png"
                                alt="<PERSON> - Founder of Little Spark"
                                className="rounded-2xl shadow-lg w-full max-w-sm lg:max-w-none h-auto sm:h-[400px] lg:h-[500px] object-cover"
                                width={500}
                                height={500}
                            />
                        </div>

                        <div className="w-full lg:w-2/3 text-center lg:text-left">
                            <div className="flex flex-col sm:flex-row items-center gap-3 mb-4">
                                <User className="h-8 w-8 text-littlespark-yellow flex-shrink-0" />
                                <h2
                                    className={`text-2xl sm:text-3xl lg:text-4xl text-gray-900 font-bold ${fredoka.className}`}
                                >
                                    Meet Joyce, Little Spark&apos;s Founder
                                </h2>
                            </div>
                            <h3
                                className={`text-lg sm:text-xl lg:text-2xl font-semibold mb-4 text-littlespark-blue ${fredoka.className}`}
                            >
                                Empowering Kids to Imagine, Create, and Explore
                                with AI
                            </h3>

                            <div className="space-y-4 text-gray-700 mb-8">
                                <p>
                                    Hi, I&apos;m Joyce—founder of Little Spark,
                                    tech strategist, and mom of two.
                                </p>

                                <p>
                                    With over 15 years of experience working in
                                    the tech and SaaS space, I&apos;ve seen
                                    firsthand how powerful technology can be
                                    when it&apos;s used intentionally.
                                </p>

                                <p>
                                    I started my first business back in 2016 as
                                    a creative outlet (hello, calligraphy days!)
                                    and have been helping online business owners
                                    ever since. But everything changed when I
                                    discovered AI. What started as a
                                    productivity tool turned into a passion—and
                                    then a pivot.
                                </p>

                                <p>
                                    As I saw how powerful AI could be for
                                    adults, I started wondering: what could it
                                    look like for kids?
                                </p>

                                <p>
                                    I wanted to create something that would
                                    empower kids to explore technology in a
                                    safe, thoughtful, and creative way—something
                                    that gave them agency, sparked their
                                    imagination, and helped them build
                                    confidence in a digital world.
                                </p>

                                <p>That&apos;s how Little Spark was born.</p>

                                <p>
                                    It&apos;s more than just screen
                                    time—it&apos;s a space where kids ages 5–13
                                    can explore storytelling, design, music, and
                                    more with the gentle guidance of AI. Built
                                    for curiosity. Designed for safety. Rooted
                                    in creativity.
                                </p>

                                <p>
                                    Because I believe tech can support
                                    creativity—not replace it. And when kids are
                                    empowered to use it responsibly, the
                                    possibilities are endless.
                                </p>

                                <p className="font-medium text-littlespark-orange">
                                    Thanks for being here. I hope your little
                                    ones love what we&apos;re building.
                                </p>
                            </div>

                            <LearnTip
                                tip="All creative tools on Little Spark are designed with educational value in mind, helping children develop critical thinking, problem-solving, and digital literacy skills."
                                subject="Education Through Creativity"
                            />
                        </div>
                    </div>

                    {/* Michelle's Bio Section */}
                    <div className="flex flex-col lg:flex-row-reverse items-center lg:items-start gap-8 lg:gap-12">
                        <div className="w-full lg:w-1/3 flex justify-center lg:justify-start">
                            <Image
                                src="/images/about2.png"
                                alt="Michelle - Education Advisor at Little Spark"
                                className="rounded-2xl shadow-lg w-full max-w-sm lg:max-w-none h-auto sm:h-[400px] lg:h-[500px] object-cover"
                                width={500}
                                height={500}
                            />
                        </div>

                        <div className="w-full lg:w-2/3 text-center lg:text-left">
                            <div className="flex flex-col sm:flex-row items-center gap-3 mb-4">
                                <Presentation className="h-8 w-8 text-littlespark-yellow flex-shrink-0" />
                                <h2
                                    className={`text-2xl sm:text-3xl lg:text-4xl text-gray-900 font-bold ${fredoka.className}`}
                                >
                                    Meet Michelle, Education Advisor at Little
                                    Spark
                                </h2>
                            </div>
                            <h3
                                className={`text-lg sm:text-xl lg:text-2xl font-semibold mb-4 text-littlespark-blue ${fredoka.className}`}
                            >
                                Educator, Equity Advocate, and Educational
                                Excellence
                            </h3>

                            <div className="space-y-4 text-gray-700 mb-8">
                                <p>
                                    Hi, I&apos;m Michelle—educator, equity
                                    advocate, and proud daughter of Filipino
                                    immigrant parents.
                                </p>

                                <p>
                                    Born and raised in Stockton, CA, I&apos;ve
                                    spent the last decade in public
                                    education—first as a 2nd and 3rd grade
                                    teacher, now as an Assistant Principal at a
                                    public charter school rooted in joy and
                                    academic excellence. Along the way,
                                    I&apos;ve led TK–5 science, coached fellow
                                    teachers, supported schoolwide strategy, and
                                    earned credentials in teaching, leadership,
                                    and administration.
                                </p>

                                <p>
                                    At Little Spark, I advise on all things
                                    education to make sure our resources are fun
                                    and grounded in how kids actually learn.
                                </p>

                                <p>
                                    And yes, I&apos;m a proud Potterhead—my
                                    wedding was Harry Potter-themed.
                                </p>
                            </div>

                            <LearnTip
                                tip="Our education advisor makes sure everything inside Little Spark isn't just fun—it's rooted in how kids actually learn and grow."
                                subject="Expert-Guided Learning"
                            />
                        </div>
                    </div>
                </div>
            </section>
        </div>
    );
};

export default About;
