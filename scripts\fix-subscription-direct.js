const { PrismaClient } = require('../src/generated/prisma');

const prisma = new PrismaClient();

async function fixSubscriptionStatuses() {
  try {
    console.log('🔧 Starting direct subscription status fix...');
    
    // Find all accounts with trialing status that have successful payments
    const trialingAccountsWithPayments = await prisma.profile.findMany({
      where: {
        subscription_status: 'trialing'
      },
      include: {
        payments: {
          where: {
            status: 'succeeded'
          },
          orderBy: {
            created_at: 'desc'
          },
          take: 1
        }
      }
    });

    console.log(`Found ${trialingAccountsWithPayments.length} accounts with trialing status`);

    const accountsNeedingFix = trialingAccountsWithPayments.filter(account => account.payments.length > 0);
    console.log(`${accountsNeedingFix.length} accounts have successful payments and need status update`);

    if (accountsNeedingFix.length === 0) {
      console.log('✅ No accounts need fixing');
      return;
    }

    // Show accounts that will be fixed
    console.log('\n📋 Accounts to be fixed:');
    accountsNeedingFix.forEach((account, index) => {
      console.log(`${index + 1}. ${account.email} - Trial End: ${account.trial_end} - Latest Payment: ${account.payments[0]?.payment_date}`);
    });

    console.log('\n🔄 Updating subscription statuses...');

    const updateResults = [];
    for (const account of accountsNeedingFix) {
      try {
        const latestPayment = account.payments[0];
        
        // Calculate subscription end date (30 days from payment)
        const subscriptionEnd = new Date(latestPayment.payment_date);
        subscriptionEnd.setDate(subscriptionEnd.getDate() + 30);

        const updateData = {
          subscription_status: 'active',
          subscription_start: latestPayment.payment_date,
          subscription_end: subscriptionEnd,
          trial_used: true, // Mark trial as used since they've moved to active subscription
          updated_at: new Date()
        };

        await prisma.profile.update({
          where: { id: account.id },
          data: updateData
        });

        updateResults.push({
          email: account.email,
          success: true,
          newStatus: 'active',
          subscriptionEnd: subscriptionEnd
        });

        console.log(`✅ Updated ${account.email}: trialing → active (expires: ${subscriptionEnd.toISOString().split('T')[0]})`);

      } catch (error) {
        console.error(`❌ Failed to update ${account.email}:`, error.message);
        updateResults.push({
          email: account.email,
          success: false,
          error: error.message
        });
      }
    }

    // Also check for incomplete status accounts with payments
    console.log('\n🔍 Checking incomplete status accounts...');
    
    const incompleteAccountsWithPayments = await prisma.profile.findMany({
      where: {
        subscription_status: 'incomplete'
      },
      include: {
        payments: {
          where: {
            status: 'succeeded',
            created_at: {
              gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Within last 30 days
            }
          },
          orderBy: {
            created_at: 'desc'
          },
          take: 1
        }
      }
    });

    const incompleteNeedingFix = incompleteAccountsWithPayments.filter(account => account.payments.length > 0);
    console.log(`Found ${incompleteNeedingFix.length} incomplete accounts with recent payments`);

    for (const account of incompleteNeedingFix) {
      try {
        const latestPayment = account.payments[0];
        
        // Calculate subscription end date (30 days from payment)
        const subscriptionEnd = new Date(latestPayment.payment_date);
        subscriptionEnd.setDate(subscriptionEnd.getDate() + 30);

        const updateData = {
          subscription_status: 'active',
          subscription_start: latestPayment.payment_date,
          subscription_end: subscriptionEnd,
          trial_used: true, // Mark trial as used since they've moved to active subscription
          updated_at: new Date()
        };

        await prisma.profile.update({
          where: { id: account.id },
          data: updateData
        });

        updateResults.push({
          email: account.email,
          success: true,
          newStatus: 'active',
          subscriptionEnd: subscriptionEnd
        });

        console.log(`✅ Updated ${account.email}: incomplete → active (expires: ${subscriptionEnd.toISOString().split('T')[0]})`);

      } catch (error) {
        console.error(`❌ Failed to update ${account.email}:`, error.message);
        updateResults.push({
          email: account.email,
          success: false,
          error: error.message
        });
      }
    }

    // Summary
    const successCount = updateResults.filter(r => r.success).length;
    const failureCount = updateResults.filter(r => !r.success).length;

    console.log('\n📊 Summary:');
    console.log(`✅ Successfully updated: ${successCount} accounts`);
    console.log(`❌ Failed to update: ${failureCount} accounts`);
    
    if (successCount > 0) {
      console.log('\n🎉 Subscription status sync completed successfully!');
      console.log('Users should now see their active subscription status.');
    }

  } catch (error) {
    console.error('❌ Error during subscription fix:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the fix
fixSubscriptionStatuses();
