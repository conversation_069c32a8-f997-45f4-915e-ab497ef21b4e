import React, { useState } from 'react';
import { ChevronDown } from 'lucide-react';
import { fredoka } from '@/lib/fonts';

interface FAQItemProps {
  question: string;
  answer: string;
  index: number;
}

const FAQItem: React.FC<FAQItemProps> = ({ question, answer, index }) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div 
      className="border-b border-gray-200 rounded-lg overflow-hidden animate-fadeInUp"
      style={{ animationDelay: `${0.1 + (index * 0.05)}s` }}
    >
      <button
        className="w-full px-6 py-2 text-left bg-white  transition-colors duration-200 
                   flex items-center justify-between group"
        onClick={() => setIsOpen(!isOpen)}
        aria-expanded={isOpen}
      >
        <h3 className={`text-md font-bold text-gray-800 hover:underline ${fredoka.className} pr-4`}>
          {question}
        </h3>
        <ChevronDown 
          className={`h-5 w-5 text-gray-500 transition-transform duration-200 flex-shrink-0 
                     ${isOpen ? 'rotate-180' : ''} group-hover:text-littlespark-primary`}
        />
      </button>
      
      <div 
        className={`overflow-hidden transition-all duration-300 ease-in-out ${
          isOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
        }`}
      >
        <div className="px-6 pb-5 pt-2">
          <p className="text-gray-700 text-sm font-light leading-relaxed">
            {answer}
          </p>
        </div>
      </div>
    </div>
  );
};

export default FAQItem; 