
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON><PERSON><PERSON>, Brush, HelpCircle } from 'lucide-react';
import LearningModeToggle from '@/components/shared/LearningModeToggle';
import CreativeToolHelpDialog from '@/components/shared/CreativeToolHelpDialog';
import { artHelpSteps } from './helpers/artHelpSteps';
import Link from 'next/link';

interface ArtHeaderProps {
  learningMode: boolean;
  toggleLearningMode: () => void;
}

const ArtHeader = ({
  learningMode,
  toggleLearningMode
}: ArtHeaderProps) => {
  const [helpDialogOpen, setHelpDialogOpen] = React.useState(false);

  return (
    <>
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 sm:gap-0 mb-6">
        <Link href="/dashboard">
          <Button variant="ghost" className="gap-1 min-h-[44px] text-sm sm:text-base">
            <ArrowLeft className="h-4 w-4" />
            <span className="sm:hidden">Dashboard</span>
            <span className="hidden sm:inline">Back to Dashboard</span>
          </Button>
        </Link>
        
        <div className="flex items-center gap-2 sm:gap-3 justify-end sm:justify-start">
          <Button 
            variant="outline" 
            className="!border-[#00BFA5] !text-[#00BFA5] hover:!bg-littlespark-primary hover:!text-white rounded-full min-h-[44px] text-sm sm:text-base"
            onClick={() => setHelpDialogOpen(true)}
          >
            <HelpCircle className="h-4 w-4" /> 
            <span className="hidden sm:inline ml-2">
                How to Use
            </span>
          </Button>
          
          <LearningModeToggle 
            learningMode={learningMode}
            toggleLearningMode={toggleLearningMode}
          />
        </div>
      </div>
      
      <div className="flex items-center justify-center sm:justify-start mb-6">
        <div>
          <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold flex items-center">
            <Brush className="h-5 w-5 sm:h-6 sm:w-6 lg:h-7 lg:w-7 text-spark-blue mr-2" />
            Art Studio
          </h1>
        </div>
      </div>

      <CreativeToolHelpDialog 
        open={helpDialogOpen} 
        onOpenChange={setHelpDialogOpen}
        title="Art Studio"
        description="Create amazing artwork with AI-powered image generation"
        steps={artHelpSteps}
        aiMentorMessage="Let me guide you through creating awesome art with just a few clicks!"
      />
    </>
  );
};

export default ArtHeader;
