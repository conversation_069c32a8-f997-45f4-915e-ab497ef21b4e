import { NextResponse } from 'next/server';
import { ChatGroq } from '@langchain/groq';
import { moderateContent } from '@/utils/ai/contentModeration';

const llm = new ChatGroq({
  model: 'llama-3.3-70b-versatile',
  apiKey: process.env.GROQ_API_KEY || ''
});

export async function POST(request: Request) {
  try {
    const { existingDescription, style, mood, theme, duration = 30 } = await request.json();

    const userKey = request.headers.get('x-forwarded-for') ?? 'anonymous';
    const inCheck = await moderateContent(existingDescription || '', 'prompt', userKey);
    if (!inCheck.isAppropriate) {
      return NextResponse.json({ error: inCheck.reason, blocked: inCheck.blocked ?? false }, { status: 400 });
    }

    const prompt = `Enhance this music composition to better capture the ${mood} mood in ${style} style${theme ? ` with theme ${theme}` : ''} for ${duration} seconds:

Current idea: ${existingDescription}

Improve by:
- Strengthening how the ${mood} mood is expressed through musical elements
- Adding more specific ${style} characteristics and instrumentation
- Enhancing the arrangement to better convey the ${mood} feeling
- Including production techniques and sound design that support both ${style} and ${mood}
- Refining tempo, dynamics, and musical progression for maximum emotional impact

Respond with ONLY a valid JSON object containing:
- "title": An improved title (max 5 words) that better captures ${style} + ${mood}
- "description": Enhanced description (3-4 sentences) with specific details about how ${style} and ${mood} are expressed through instruments, arrangement, and musical techniques

IMPORTANT: Return ONLY the JSON object, no additional text, explanations, or markdown formatting.`;

    const res = await llm.invoke(prompt);
    let content = res.content as string;

    const outCheck = await moderateContent(content, 'text');
    if (!outCheck.isAppropriate) {
      content = '{"title":"Safe Tune","description":"Let\'s compose a friendly and positive piece instead!"}';
    }
    
    // Clean up the response to extract JSON
    content = content.trim();
    
    // Remove markdown code fences if present
    content = content.replace(/^```(?:json)?\s*/, '').replace(/```\s*$/, '');
    
    // Extract JSON object if wrapped in other text
    const jsonMatch = content.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      content = jsonMatch[0];
    }
    
    // Try to parse and validate the JSON
    try {
      const parsed = JSON.parse(content);
      if (parsed.title && parsed.description) {
        return NextResponse.json({ 
          idea: content,
          title: parsed.title,
          description: parsed.description
        });
      }
    } catch (parseError) {
      console.error('JSON parse error:', parseError);
      console.error('Raw content:', content);
    }
    
    // Fallback: return raw content
    return NextResponse.json({ idea: content });
  } catch (error) {
    console.error('Error improving music idea:', error);
    return NextResponse.json({ error: 'Failed to improve music idea' }, { status: 500 });
  }
} 