import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { createMiddlewareSupabaseClient } from '@/lib/supabase/server';

export async function middleware(req: NextRequest) {
  const { supabase, response } = createMiddlewareSupabaseClient(req);

  const {
    data: { session },
  } = await supabase.auth.getSession();

  // Public routes that don't require authentication
  const publicPaths = ['/'];
  // Any page under /auth (including callback) is public
  const isAuthPageRoute = req.nextUrl.pathname.startsWith('/auth');
  // Supabase auth API endpoints
  const isAuthApiRoute = req.nextUrl.pathname.startsWith('/api/auth');
  // Stripe webhook endpoint must be public so Stripe can reach it
  const isStripeWebhookRoute = req.nextUrl.pathname.startsWith('/api/stripe/webhook');
  // Challenges API should be public to show available challenges
  const isChallengesApiRoute = req.nextUrl.pathname === '/api/challenges';
  // CMS API routes should be public for content fetching
  const isCMSApiRoute = req.nextUrl.pathname.startsWith('/api/cms/');
  // Determine if request should bypass auth
  const isPublicPath = publicPaths.includes(req.nextUrl.pathname) || isAuthPageRoute || isAuthApiRoute || isStripeWebhookRoute || isChallengesApiRoute || isCMSApiRoute;

  // Redirect authenticated users away from public or auth pages (but not API routes)
  if (session && isPublicPath && !req.nextUrl.pathname.startsWith('/api/')) {
    return NextResponse.redirect(new URL('/dashboard', req.url));
  }

  // Redirect unauthenticated users to auth for all other routes
  if (!session && !isPublicPath) {
    return NextResponse.redirect(new URL('/auth', req.url));
  }

  // For authenticated users, check subscription access for protected routes
  if (session && !isPublicPath) {
    const protectedRoutes = ['/dashboard', '/create', '/my-projects'];
    const isProtectedRoute = protectedRoutes.some(route => req.nextUrl.pathname.startsWith(route));

    if (isProtectedRoute) {
      try {
        // Simple subscription check via API call
        const subscriptionResponse = await fetch(`${req.nextUrl.origin}/api/subscription/status`, {
          headers: {
            'Authorization': `Bearer ${session.access_token}`,
            'Cookie': req.headers.get('cookie') || ''
          }
        });

        if (subscriptionResponse.ok) {
          const subscriptionData = await subscriptionResponse.json();

          // TRIAL SYSTEM DISABLED: No trial expiration checks
          // if (subscriptionData.subscription_status === 'trialing' && subscriptionData.trial_end) {
          //   const trialEnd = new Date(subscriptionData.trial_end);
          //   const now = new Date();

          //   if (now > trialEnd) {
          //     // Trial expired - redirect to pricing
          //     const redirectUrl = new URL('/pricing', req.url);
          //     redirectUrl.searchParams.set('reason', 'trial_expired');
          //     redirectUrl.searchParams.set('trial_expired', 'true');
          //     return NextResponse.redirect(redirectUrl);
          //   }
          // }

          // Check if subscription is inactive - but don't redirect dashboard (removed 'trialing' status)
          const activeStatuses = ['active', 'cancel_at_period_end', 'incomplete'];
          if (!activeStatuses.includes(subscriptionData.subscription_status ?? '')) {
            // Only redirect non-dashboard protected routes to pricing
            if (!req.nextUrl.pathname.startsWith('/dashboard')) {
              const redirectUrl = new URL('/pricing', req.url);
              redirectUrl.searchParams.set('reason', 'subscription_required');
              return NextResponse.redirect(redirectUrl);
            }
            // For dashboard, allow access but features will be locked
          }
        }
      } catch (error) {
        console.error('Error checking subscription access in middleware:', error);
        // On error, allow access but log the issue
      }
    }
  }

  return response;
}

export const config = {
  matcher: ['/api/:path*', '/((?!_next/static|_next/image|favicon.ico).*)'],
}; 