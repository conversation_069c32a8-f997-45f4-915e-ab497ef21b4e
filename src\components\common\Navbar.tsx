import React, { useState, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import { Menu, X } from "lucide-react";
import { nunito } from "@/lib/fonts";
import { navigationLinks, brandAssets } from "@/lib/constants";

interface User {
    email?: string;
    user_metadata?: {
        full_name?: string;
    };
}

interface NavbarProps {
    user?: User | null;
    loading?: boolean;
    isAuthenticated?: boolean;
    signOut?: () => void;
}

const Navbar: React.FC<NavbarProps> = ({
    user,
    loading,
    isAuthenticated,
    signOut,
}) => {
    const [isScrolled, setIsScrolled] = useState(false);
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

    useEffect(() => {
        const handleLenisScroll = (e: CustomEvent) => {
            const scrollPosition = e.detail.scroll;
            setIsScrolled(scrollPosition > 50);
        };

        // Listen to <PERSON><PERSON> scroll events
        window.addEventListener(
            "lenisScroll",
            handle<PERSON>enisScroll as EventListener
        );

        // Fallback to regular scroll events if <PERSON><PERSON> hasn't loaded yet
        const handleScroll = () => {
            const scrollPosition = window.scrollY;
            setIsScrolled(scrollPosition > 50);
        };

        window.addEventListener("scroll", handleScroll);

        // Clean up event listeners
        return () => {
            window.removeEventListener(
                "lenisScroll",
                handleLenisScroll as EventListener
            );
            window.removeEventListener("scroll", handleScroll);
        };
    }, []);

    // Close mobile menu when screen size changes to desktop
    useEffect(() => {
        const handleResize = () => {
            if (window.innerWidth >= 768) {
                setIsMobileMenuOpen(false);
            }
        };

        window.addEventListener("resize", handleResize);
        return () => window.removeEventListener("resize", handleResize);
    }, []);

    return (
        <nav
            className={`fixed top-0 left-0 right-0 z-50 border-b border-gray-100 py-5 transition-all duration-500 ease-in-out ${
                isScrolled
                    ? "bg-white/95 shadow-lg border-gray-200"
                    : "bg-white border-gray-100"
            }`}
        >
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="flex justify-between items-center h-16">
                    {/* Logo */}
                    <div className="flex items-center">
                        <Link href="/">
                            <Image
                                src={brandAssets.logo.src}
                                alt={brandAssets.logo.alt}
                                width={brandAssets.logo.width}
                                height={brandAssets.logo.height}
                                className="w-auto cursor-pointer"
                            />
                        </Link>
                    </div>

                    {/* Navigation Menu */}
                    <div
                        className={`hidden md:flex items-center space-x-4 ${nunito.className}`}
                    >
                        {navigationLinks.map((link) => (
                            <Link
                                key={link.href}
                                href={link.href}
                                className="text-black hover:text-gray-900 text-sm font-normal hover:bg-littlespark-secondary duration-100 px-4 py-2 rounded-full"
                            >
                                {link.label}
                            </Link>
                        ))}
                    </div>

                    {/* Desktop Auth Buttons */}
                    <div className="hidden md:flex items-center space-x-4">
                        {loading ? (
                            <div className="text-gray-500">Loading...</div>
                        ) : isAuthenticated ? (
                            <>
                                <span
                                    className={`text-gray-700 font-medium ${nunito.className}`}
                                >
                                    Welcome,{" "}
                                    {user?.user_metadata?.full_name ||
                                        user?.email}
                                </span>
                                <button
                                    onClick={signOut}
                                    className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                                >
                                    Sign Out
                                </button>
                            </>
                        ) : (
                            <>
                                <Link
                                    href="/auth"
                                    className={`border-2 border-littlespark-primary text-littlespark-primary hover:bg-littlespark-primary hover:text-white px-4 py-2 rounded-full text-sm font-medium transition-colors ${nunito.className}`}
                                >
                                    Sign In
                                </Link>
                                <Link
                                    href="/checkout"
                                    className={`bg-littlespark-primary hover:bg-littlespark-primary-hover border-2 border-littlespark-primary text-white px-4 py-2 rounded-full text-sm font-medium transition-colors ${nunito.className}`}
                                >
                                    Get Started
                                </Link>
                            </>
                        )}
                    </div>

                    {/* Mobile menu button */}
                    <div className="md:hidden">
                        <button
                            className="text-gray-600 hover:text-gray-900 transition-colors p-2 -mr-2 min-h-[44px] min-w-[44px] flex items-center justify-center"
                            onClick={() =>
                                setIsMobileMenuOpen(!isMobileMenuOpen)
                            }
                            aria-label="Toggle mobile menu"
                        >
                            {isMobileMenuOpen ? (
                                <X className="h-6 w-6" />
                            ) : (
                                <Menu className="h-6 w-6" />
                            )}
                        </button>
                    </div>
                </div>
            </div>

            {/* Mobile menu panel */}
            {isMobileMenuOpen && (
                <div className="md:hidden bg-white border-t border-gray-100 shadow-lg">
                    <div className="px-4 pt-2 pb-3 space-y-1">
                        {/* Mobile Navigation Links */}
                        {navigationLinks.map((link) => (
                            <Link
                                key={link.href}
                                href={link.href}
                                className={`block px-4 py-3 text-base font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50 rounded-md transition-colors min-h-[44px] flex items-center ${nunito.className}`}
                                onClick={() => setIsMobileMenuOpen(false)}
                            >
                                {link.label}
                            </Link>
                        ))}
                    </div>

                    {/* Mobile Auth Section */}
                    <div className="pt-4 pb-3 border-t border-gray-200">
                        <div className="px-4 space-y-3">
                            {loading ? (
                                <div className="text-gray-500 text-center py-2">
                                    Loading...
                                </div>
                            ) : isAuthenticated ? (
                                <>
                                    <div
                                        className={`text-sm text-gray-700 ${nunito.className}`}
                                    >
                                        Welcome,{" "}
                                        {user?.user_metadata?.full_name ||
                                            user?.email}
                                    </div>
                                    <button
                                        onClick={() => {
                                            signOut?.();
                                            setIsMobileMenuOpen(false);
                                        }}
                                        className="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-3 rounded-lg text-sm font-medium transition-colors min-h-[44px]"
                                    >
                                        Sign Out
                                    </button>
                                </>
                            ) : (
                                <div className="space-y-2">
                                    <Link
                                        href="/auth"
                                        className={`block w-full text-center border-2 border-littlespark-primary text-littlespark-primary hover:bg-littlespark-primary hover:text-white px-4 py-3 rounded-full text-sm font-medium transition-colors min-h-[44px] flex items-center justify-center ${nunito.className}`}
                                        onClick={() =>
                                            setIsMobileMenuOpen(false)
                                        }
                                    >
                                        Sign In
                                    </Link>
                                    <Link
                                        href="/checkout"
                                        className={`block w-full text-center bg-littlespark-primary hover:bg-littlespark-primary-hover text-white px-4 py-3 rounded-full text-sm font-medium transition-colors min-h-[44px] flex items-center justify-center ${nunito.className}`}
                                        onClick={() =>
                                            setIsMobileMenuOpen(false)
                                        }
                                    >
                                        Get Started
                                    </Link>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            )}
        </nav>
    );
};

export default Navbar;
