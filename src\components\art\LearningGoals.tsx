
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Sparkles } from 'lucide-react';

const LearningGoals = () => {
  return (
    <Card className="mb-8">
      <CardContent className="pt-6">
        <h2 className="text-xl font-bold mb-4 flex items-center">
          <Sparkles className="h-5 w-5 text-spark-blue mr-2" />
          Art Creation Learning Goals
        </h2>
        <ul className="space-y-3">
          <li className="flex items-start gap-3">
            <span className="bg-spark-blue/10 text-spark-blue font-bold rounded-full w-6 h-6 flex items-center justify-center flex-shrink-0 mt-0.5">1</span>
            <p>Visual Creativity: Learn to imagine and describe visual scenes</p>
          </li>
          <li className="flex items-start gap-3">
            <span className="bg-spark-blue/10 text-spark-blue font-bold rounded-full w-6 h-6 flex items-center justify-center flex-shrink-0 mt-0.5">2</span>
            <p>Descriptive Language: Practice using vivid, detailed descriptions</p>
          </li>
          <li className="flex items-start gap-3">
            <span className="bg-spark-blue/10 text-spark-blue font-bold rounded-full w-6 h-6 flex items-center justify-center flex-shrink-0 mt-0.5">3</span>
            <p>Art Appreciation: Understand different styles and visual elements</p>
          </li>
          <li className="flex items-start gap-3">
            <span className="bg-spark-blue/10 text-spark-blue font-bold rounded-full w-6 h-6 flex items-center justify-center flex-shrink-0 mt-0.5">4</span>
            <p>AI Understanding: Learn how AI uses your words to create images</p>
          </li>
        </ul>
      </CardContent>
    </Card>
  );
};

export default LearningGoals;
