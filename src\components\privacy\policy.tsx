import React from "react";
import { fredoka } from "@/lib/fonts";

interface PrivacyHeaderProps {
    title: string;
    lastUpdated: string;
}

export const PrivacyHeader = ({ title, lastUpdated }: PrivacyHeaderProps) => {
    return (
        <div className="mb-8">
            <h2
                className={`text-3xl font-bold mb-2 text-gray-900 ${fredoka.className}`}
            >
                {title}
            </h2>
            <p className="text-sm text-gray-600">Last Updated: {lastUpdated}</p>
        </div>
    );
};
