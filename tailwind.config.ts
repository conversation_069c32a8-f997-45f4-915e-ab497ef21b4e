import type { Config } from 'tailwindcss';

const config: Config = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        littlespark: {
          primary: 'var(--color-littlespark-primary)',
          'primary-hover': 'var(--color-littlespark-primary-hover)',
          secondary: 'var(--color-littlespark-secondary)',
          lavender: 'var(--color-littlespark-lavender)',
          yellow: 'var(--color-littlespark-yellow)',
          blue: 'var(--color-littlespark-blue)',
          pink: 'var(--color-littlespark-pink)',
          teal: 'var(--color-littlespark-teal)',
          orange: 'var(--color-littlespark-orange)',
        },
        gray: {
          50: 'var(--color-gray-50)',
          100: 'var(--color-gray-100)',
          500: 'var(--color-gray-500)',
          600: 'var(--color-gray-600)',
          900: 'var(--color-gray-900)',
        },
        semantic: {
          success: 'var(--color-success)',
          warning: 'var(--color-warning)',
          error: 'var(--color-error)',
        },
        background: {
          purple: 'var(--color-purple-50)',
          'yellow-light': 'var(--color-yellow-light)',
        },
      },
      animation: {
        marquee: 'marquee 15s linear infinite',
      },
      keyframes: {
        marquee: {
          '0%': { transform: 'translateX(0%)' },
          '100%': { transform: 'translateX(-50%)' },
        },
      },
    },
  },
  plugins: [require('@tailwindcss/typography')],
};

export default config; 