"use client";

import React, { useState, useCallback, useEffect } from 'react';
import { Mic, Square, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import SpeechRecognition, { useSpeechRecognition } from 'react-speech-recognition';

interface VoiceEnabledInputProps {
    onTextAdded: (text: string) => void;
    isTextArea?: boolean;
    className?: string;
    placeholder?: string;
    value?: string;
    onChange?: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
    id?: string;
    onKeyDown?: (e: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
    disabledTooltip?: string;
}

const VoiceEnabledInput: React.FC<VoiceEnabledInputProps> = ({
    onTextAdded,
    isTextArea = false,
    className = '',
    placeholder = '',
    value = '',
    onChange,
    id,
    onKeyDown,
    // disabledTooltip
}) => {
    const [isProcessing, setIsProcessing] = useState(false);
    const [triggered, setTriggered] = useState(false);
    const [mounted, setMounted] = useState(false);
    const [isListeningLocal, setIsListeningLocal] = useState(false);

    useEffect(() => {
        setMounted(true);
    }, []);

    const {
        transcript,
        listening,
        resetTranscript,
        browserSupportsSpeechRecognition,
        isMicrophoneAvailable
    } = useSpeechRecognition();

    useEffect(() => {
        // When recognition stops for this instance, reset local state and process transcript
        if (!listening && triggered) {
            // clear local listening flag
            setIsListeningLocal(false);
            // if we have captured speech, add it
            if (transcript && !isProcessing) {
                onTextAdded(transcript);
                resetTranscript();
                setTriggered(false);
            }
        }
    }, [listening, transcript, isProcessing, triggered, onTextAdded, resetTranscript]);

    const startListening = useCallback(async () => {
        try {
            setTriggered(true);
            setIsProcessing(true);
            resetTranscript();
            // Notify user that listening has started
            toast.info("Listening... Speak now", { duration: 3000 });
            await SpeechRecognition.startListening({ 
                continuous: false,
                language: 'en-US'
            });
        } catch (error) {
            console.error('Speech recognition error:', error);
            toast.error("Failed to start voice recognition");
        } finally {
            setIsProcessing(false);
        }
    }, [resetTranscript]);

    const stopListening = useCallback(() => {
        SpeechRecognition.stopListening();
    }, []);

    const handleVoiceToggle = useCallback(() => {
        if (listening) {
            setIsListeningLocal(false);
            stopListening();
        } else {
            setIsListeningLocal(true);
            startListening();
        }
    }, [listening, startListening, stopListening]);

    const InputComponent = isTextArea ? 'textarea' : 'input';
    const baseInputStyles = isTextArea 
        ? "min-h-[200px] resize-none py-3 px-4" 
        : "h-10 px-4";

    const inputClassName = `${baseInputStyles} w-full pr-12 rounded-xl border-2 border-gray-200 focus:border-[#00B7FD] focus:ring-2 focus:ring-[#00B7FD]/20 outline-none transition-colors ${className}`;

    // Only render browser-specific content after mounting
    if (!mounted) {
        return (
            <div className="relative w-full">
                <InputComponent
                    id={id}
                    value={value}
                    onChange={onChange}
                    onKeyDown={onKeyDown}
                    placeholder={placeholder}
                    className={inputClassName}
                />
            </div>
        );
    }

    if (!browserSupportsSpeechRecognition) {
        return (
            <div className="relative w-full">
                <InputComponent
                    id={id}
                    value={value}
                    onChange={onChange}
                    onKeyDown={onKeyDown}
                    placeholder={placeholder}
                    className={inputClassName}
                />
                <div className="text-sm text-red-500 mt-1">
                    Voice input is not supported in this browser.
                </div>
            </div>
        );
    }

    if (!isMicrophoneAvailable) {
        toast.error("Please allow microphone access to use voice input");
    }

    return (
        <div className="relative w-full">
            <InputComponent
                id={id}
                value={value}
                onChange={onChange}
                onKeyDown={onKeyDown}
                placeholder={placeholder}
                className={inputClassName}
            />
            {mounted && (
                <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className={`absolute right-1 ${isTextArea ? 'top-1' : 'top-1/2 -translate-y-1/2'} h-8 w-8 hover:bg-gray-100 transition-colors ${listening && isListeningLocal ? 'text-red-500 bg-red-50' : 'text-gray-500'}`}
                    onClick={handleVoiceToggle}
                    disabled={isProcessing}
                >
                    {isProcessing ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (listening && isListeningLocal) ? (
                        <Square className="h-4 w-4" />
                    ) : (
                        <Mic className="h-4 w-4" />
                    )}
                </Button>
            )}
            {mounted && listening && isListeningLocal && (
                <div className="absolute right-1 top-full mt-1 bg-red-100 text-red-700 px-2 py-1 rounded text-xs whitespace-nowrap z-20 shadow-sm border border-red-200">
                    🎤 Listening...
                </div>
            )}
            {mounted && transcript && (
                <div className="absolute left-1 right-10 bottom-1 bg-blue-50 text-blue-700 px-2 py-1 rounded text-xs truncate z-10 border border-blue-200">
                    &quot;{transcript}&quot;
                </div>
            )}
        </div>
    );
};

export default VoiceEnabledInput;
