const { getPayload } = require('payload')
const config = require('./dist/payload.config.js').default

async function seedAdmin() {
  try {
    const payload = await getPayload({ config })
    
    // Check if admin user already exists
    const existingUsers = await payload.find({
      collection: 'users',
      where: {
        email: {
          equals: '<EMAIL>'
        }
      }
    })
    
    if (existingUsers.docs.length > 0) {
      console.log('Admin user already exists')
      return
    }
    
    // Create admin user
    const adminUser = await payload.create({
      collection: 'users',
      data: {
        email: '<EMAIL>',
        password: 'admin123',
        firstName: 'Admin',
        lastName: 'User',
        role: 'admin',
        bio: 'System administrator for Little Spark CMS',
        isActive: true
      }
    })
    
    console.log('Admin user created:', adminUser.email)
    
    // Create a content creator user
    const creatorUser = await payload.create({
      collection: 'users',
      data: {
        email: '<EMAIL>',
        password: 'creator123',
        firstName: 'Content',
        lastName: 'Creator',
        role: 'content-creator',
        bio: 'Content creator for Little Spark challenges',
        specialties: [
          { specialty: 'art' },
          { specialty: 'story' },
          { specialty: 'music' }
        ],
        isActive: true
      }
    })
    
    console.log('Content creator user created:', creatorUser.email)
    
    process.exit(0)
  } catch (error) {
    console.error('Error seeding admin:', error)
    process.exit(1)
  }
}

seedAdmin()
