import { ChatMessage } from '../types/chatTypes';
import { <PERSON><PERSON><PERSON><PERSON>cter } from '../../types';
import { toast } from 'sonner';
import { isBlocked, incrementStrike, getRemainingBlockMinutes } from '@/utils/ai/strikeTracker';

/**
 * Call the LangGraph AI buddy chat API
 */
const callAIBuddyChat = async (
  message: string,
  character: <PERSON><PERSON><PERSON><PERSON><PERSON>,
  characterName: string,
  recentMessages: { role: string; content: string }[]
): Promise<string> => {
  try {
    // Check local block before sending
    if (isBlocked()) {
      toast.error(`Content safety: you are temporarily blocked (${getRemainingBlockMinutes()} min left).`);
      throw new Error('User is blocked due to repeated inappropriate messages.');
    }

    const response = await fetch('/api/ai-buddy/chat', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        message,
        character,
        characterName,
        chatHistory: recentMessages
      })
    });

    if (!response.ok) {
      let errorMsg = `API call failed: ${response.status} ${response.statusText}`;
      try {
        const errJson = await response.json();
        if (errJson?.error) {
          errorMsg = errJson.error;
          // Show toast warning to the user
          toast.warning(errJson.error);
          if (errJson.blocked) {
            toast.error('Too many unsafe requests. Please try again later.');
          }
        }
      } catch {
        // ignore JSON parse errors
      }
      // Increment local strike counter and maybe block
      const strikeState = incrementStrike();
      if (strikeState.blockedUntil > Date.now()) {
        toast.error('Content safety: you are temporarily blocked for 5 minutes.');
      }
      throw new Error(errorMsg);
    }

    const data = await response.json();
    return data.response || "I'm here to help with your creative projects! What would you like to work on today?";
  } catch (error) {
    console.error('Error calling AI buddy chat service:', error);
    
    // Character-specific fallback responses
    const fallbackResponses = {
      robot: "Oops! My circuits got a bit tangled there. What creative project should we work on together?",
      owl: "Oh my! That was quite puzzling. What would you like to explore today?",
      explorer: "Well, that was an unexpected detour! What adventure should we embark on?"
    };
    
    return fallbackResponses[character] || "I'm here to help with your creative projects! What would you like to work on today?";
  }
};

/**
 * Send message to AI service and get a response
 */
export const sendMessageToAI = async (
  content: string, 
  recentMessages: { role: string; content: string }[],
  character: MentorCharacter,
  characterName: string
): Promise<string> => {
  try {
    const response = await callAIBuddyChat(content, character, characterName, recentMessages);
    return response || "I'm sorry, I couldn't generate a response right now.";
  } catch (error) {
    console.error('Error calling AI service', error);
    
    // Character-specific fallback responses
    const fallbackResponses = {
      robot: "Oops! My circuits got a bit tangled there. What creative project should we work on together?",
      owl: "Oh my! That was quite puzzling. What would you like to explore today?",
      explorer: "Well, that was an unexpected detour! What adventure should we embark on?"
    };
    
    return fallbackResponses[character] || "I'm here to help with your creative projects! What would you like to work on today?";
  }
};

/**
 * Format a new chat message
 */
export const createChatMessage = (
  role: 'user' | 'assistant', 
  content: string
): ChatMessage => {
  return {
    id: (Date.now() + (role === 'assistant' ? 1 : 0)).toString(),
    role,
    content,
    timestamp: new Date().toISOString()
  };
};
