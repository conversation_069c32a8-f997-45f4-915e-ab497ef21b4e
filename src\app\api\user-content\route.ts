import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { createServerSupabaseClient } from '@/lib/supabase/server';

// POST /api/user-content - Save user content to database
export async function POST(request: NextRequest) {
  try {
    // Get authenticated user
    const supabase = await createServerSupabaseClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Authentication required' 
        },
        { status: 401 }
      );
    }

    const {
      user_id,
      type,
      title,
      content_metadata,
      preview_url,
      challenge_id,
      content_hash
    } = await request.json();

    // Use authenticated user's ID if not provided or if it doesn't match
    const finalUserId = user_id && user_id === user.id ? user_id : user.id;

    // Validate required fields
    if (!type || !title) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Type and title are required' 
        },
        { status: 400 }
      );
    }

    // Validate content type
    const validTypes = ['story', 'art', 'music', 'chat', 'challenges', 'mindspark_history'];
    if (!validTypes.includes(type)) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid content type' 
        },
        { status: 400 }
      );
    }

    // If challenge_id is provided, verify it exists (check both database and CMS)
    if (challenge_id) {
      // First check database challenges
      const dbChallenge = await prisma.challenge.findUnique({
        where: { id: challenge_id }
      });

      // If not found in database, it might be a CMS challenge - allow it
      if (!dbChallenge) {
        console.log(`Challenge ${challenge_id} not found in database, assuming CMS challenge`);
        // Don't return error for CMS challenges - they're valid but stored externally
      }
    }

    // Check for duplicate content if hash is provided
    if (content_hash) {
      const existingContent = await prisma.userContent.findFirst({
        where: {
          user_id: finalUserId,
          content_hash: content_hash
        }
      });

      if (existingContent) {
        return NextResponse.json({
          success: false,
          error: 'Content already saved to portfolio',
          duplicate: true,
          existingContent: {
            id: existingContent.id,
            title: existingContent.title,
            created_at: existingContent.created_at
          }
        }, { status: 409 });
      }
    }

    // Create the user content record
    const userContent = await prisma.userContent.create({
      data: {
        user_id: finalUserId,
        type,
        title,
        content_metadata: content_metadata || {},
        preview_url,
        challenge_id,
        content_hash
      }
    });

    return NextResponse.json({
      success: true,
      message: 'Content saved successfully',
      content: {
        id: userContent.id,
        type: userContent.type,
        title: userContent.title,
        preview_url: userContent.preview_url,
        challenge_id: userContent.challenge_id,
        created_at: userContent.created_at
      }
    });

  } catch (error) {
    console.error('Error saving user content:', error);

    // Handle specific database errors
    if (error instanceof Error) {
      // Handle unique constraint violations
      if (error.message.includes('unique constraint') || error.message.includes('duplicate')) {
        return NextResponse.json(
          {
            success: false,
            error: 'Content already exists',
            duplicate: true
          },
          { status: 409 }
        );
      }
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to save content'
      },
      { status: 500 }
    );
  }
}

// GET /api/user-content - Get user's content (portfolio)
export async function GET(request: NextRequest) {
  try {
    // Get authenticated user
    const supabase = await createServerSupabaseClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Authentication required' 
        },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type');
    const challengeId = searchParams.get('challenge_id');

    // Build where clause
    const whereClause: {
      user_id: string;
      type?: string;
      challenge_id?: string;
    } = {
      user_id: user.id
    };

    if (type) {
      whereClause.type = type;
    }

    if (challengeId) {
      whereClause.challenge_id = challengeId;
    }

    // Get user content
    const userContent = await prisma.userContent.findMany({
      where: whereClause,
      include: {
        challenge: {
          select: {
            id: true,
            title: true,
            type: true,
            difficulty: true
          }
        }
      },
      orderBy: {
        created_at: 'desc'
      }
    });

    return NextResponse.json({
      success: true,
      content: userContent
    });

  } catch (error) {
    console.error('Error fetching user content:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch content' 
      },
      { status: 500 }
    );
  }
}
