"use client";
import React, { useState } from "react";
import { Brush, Droplet, Eraser, Pipette, ZoomIn, ZoomOut, RefreshCw, Download as DownloadIcon, Undo, Redo } from 'lucide-react';
import { Ta<PERSON>, <PERSON><PERSON>List, Ta<PERSON>Trigger, TabsContent } from '@/components/ui/tabs';

interface ColoringToolControlsProps {
  tool: string;
  setTool: (tool: string) => void;
  color: string;
  setColor: (color: string) => void;
  brushSize: number;
  setBrushSize: (size: number) => void;
  onZoomIn: () => void;
  onZoomOut: () => void;
  onReset: () => void;
  onDownload: () => void;
  onUndo: () => void;
  onRedo: () => void;
}

const presetColors = [
  "#FF5757", "#FFA757", "#FFE757", "#6FFF8C", "#57B7FF", "#A857FF", "#FF57E1", "#A87B57", "#000000", "#FFFFFF"
];

const tools = [
  { key: 'brush', label: 'Brush', icon: <Brush size={24} /> },
  { key: 'eraser', label: 'Eraser', icon: <Eraser size={24} /> },
  { key: 'fill', label: 'Fill', icon: <Droplet size={24} /> },
  { key: 'picker', label: 'Picker', icon: <Pipette size={24} /> },
];

export default function ColoringToolControls({
  tool,
  setTool,
  color,
  setColor,
  brushSize,
  setBrushSize,
  onZoomIn,
  onZoomOut,
  onReset,
  onDownload,
  onUndo,
  onRedo,
}: ColoringToolControlsProps) {
  const [activeTab, setActiveTab] = useState('tools');

  return (
    <div className="flex flex-col gap-4 lg:gap-6 w-full">
      {/* Tools Card */}
      <div className="bg-white rounded-3xl shadow-lg p-4 lg:p-6 w-full flex flex-col items-center">
        <h2 className="font-extrabold text-xl lg:text-2xl text-center mb-1 tracking-tight text-spark-blue">Coloring Tools</h2>
        <p className="text-gray-500 text-center mb-4 text-xs lg:text-sm">Choose colors and tools for coloring</p>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="w-full flex bg-gray-100 rounded-full mb-4 lg:mb-6 p-1">
            <TabsTrigger value="tools" className="flex-1 rounded-full font-semibold text-xs lg:text-sm data-[state=active]:bg-[#00BFA5] data-[state=active]:text-white transition">Tools</TabsTrigger>
            <TabsTrigger value="colors" className="flex-1 rounded-full font-semibold text-xs lg:text-sm data-[state=active]:bg-[#00BFA5] data-[state=active]:text-white transition">Colors</TabsTrigger>
          </TabsList>
          <TabsContent value="tools" className="w-full">
            {/* Tools grid - ensure valid JSX */}
            <div className="grid grid-cols-2 gap-2 lg:gap-4 w-full mb-4 lg:mb-6">
              {tools.map((t) => (
                <button
                  key={t.key}
                  className={`group flex flex-col items-center justify-center rounded-xl lg:rounded-2xl p-2 lg:p-4 border-2 font-bold text-xs shadow-sm w-full h-[60px] lg:h-[80px] transition
                    ${tool === t.key
                      ? 'bg-[#00BFA5] border-[#00BFA5] text-white'
                      : 'bg-white border-[#00BFA5] text-[#00BFA5] hover:bg-[#00BFA5] hover:text-black'}
                  `}
                  onClick={() => setTool(t.key)}
                  aria-label={t.label}
                >
                  {React.cloneElement(t.icon, {
                    size: 20,
                    color: tool === t.key ? 'white' : 'currentColor',
                    className: 'transition-colors group-hover:text-black lg:w-7 lg:h-7'
                  })}
                  <span className="mt-1 transition-colors group-hover:text-black text-xs">{t.label}</span>
                </button>
              ))}
            </div>
            <div className="flex gap-1 lg:gap-2 w-full justify-center mb-4 lg:mb-6">
              <button className="rounded-lg px-2 lg:px-3 py-2 h-8 lg:h-10 min-w-[32px] lg:min-w-[40px] border-2 border-[#00BFA5] text-[#00BFA5] bg-white hover:bg-[#00BFA5] hover:text-black transition flex items-center justify-center" onClick={onUndo} aria-label="Undo"><Undo size={16} className="lg:w-5 lg:h-5" /></button>
              <button className="rounded-lg px-2 lg:px-3 py-2 h-8 lg:h-10 min-w-[32px] lg:min-w-[40px] border-2 border-[#00BFA5] text-[#00BFA5] bg-white hover:bg-[#00BFA5] hover:text-black transition flex items-center justify-center" onClick={onRedo} aria-label="Redo"><Redo size={16} className="lg:w-5 lg:h-5" /></button>
              <button className="rounded-lg px-2 lg:px-3 py-2 h-8 lg:h-10 min-w-[32px] lg:min-w-[40px] border-2 border-[#00BFA5] text-[#00BFA5] bg-white hover:bg-[#00BFA5] hover:text-black transition flex items-center justify-center" onClick={onZoomIn} aria-label="Zoom In"><ZoomIn size={16} className="lg:w-5 lg:h-5" /></button>
              <button className="rounded-lg px-2 lg:px-3 py-2 h-8 lg:h-10 min-w-[32px] lg:min-w-[40px] border-2 border-[#00BFA5] text-[#00BFA5] bg-white hover:bg-[#00BFA5] hover:text-black transition flex items-center justify-center" onClick={onZoomOut} aria-label="Zoom Out"><ZoomOut size={16} className="lg:w-5 lg:h-5" /></button>
              <button className="rounded-lg px-2 lg:px-3 py-2 h-8 lg:h-10 min-w-[32px] lg:min-w-[40px] border-2 border-[#00BFA5] text-[#00BFA5] bg-white hover:bg-[#00BFA5] hover:text-black transition flex items-center justify-center" onClick={onReset} aria-label="Reset"><RefreshCw size={16} className="lg:w-5 lg:h-5" /></button>
              <button className="rounded-lg px-2 lg:px-3 py-2 h-8 lg:h-10 min-w-[32px] lg:min-w-[40px] border-2 border-[#00BFA5] text-[#00BFA5] bg-white hover:bg-[#00BFA5] hover:text-black transition flex items-center justify-center" onClick={onDownload} aria-label="Download"><DownloadIcon size={16} className="lg:w-5 lg:h-5" /></button>
            </div>
            <div className="flex flex-col items-center w-full gap-2">
              <label className="text-xs text-gray-500">Brush Size: <span className="font-semibold text-[#00BFA5]">{brushSize}px</span></label>
              <input
                type="range"
                min={2}
                max={40}
                value={brushSize}
                onChange={(e) => setBrushSize(Number(e.target.value))}
                className="w-3/4 accent-[#00BFA5]"
                title="Brush size slider"
              />
            </div>
          </TabsContent>
          <TabsContent value="colors" className="w-full">
            <div className="flex flex-col items-center gap-3 lg:gap-4">
              <div className="flex flex-wrap gap-1 lg:gap-2 justify-center w-full">
                {presetColors.map((c) => (
                  <button
                    key={c}
                    className={`w-6 h-6 lg:w-8 lg:h-8 rounded-full border-2 transition ${color === c ? 'border-[#00BFA5] ring-2 ring-[#00BFA5]/30' : 'border-gray-200'} focus:outline-none`}
                    style={{ background: c }}
                    onClick={() => setColor(c)}
                    aria-label={`Select color ${c}`}
                  />
                ))}
                <input
                  type="color"
                  value={color}
                  onChange={(e) => setColor(e.target.value)}
                  className="w-6 h-6 lg:w-8 lg:h-8 rounded-full border-2 border-gray-200 p-0 bg-transparent cursor-pointer"
                  aria-label="Custom color picker"
                  title="Custom color picker"
                />
              </div>
              <span className="text-xs text-gray-400 text-center">Pick a color or use the custom picker</span>
            </div>
          </TabsContent>
        </Tabs>
      </div>
      {/* Choose Coloring Page Card (if not handled in main layout) */}
      {/*
      <div className="bg-white rounded-3xl shadow-lg p-4 w-full">
        ...Choose a Coloring Page dropdown here if needed...
      </div>
      */}
    </div>
  );
} 