import { fredoka } from "@/lib/fonts";
import React from "react";

export type SubscriptionPlan = {
    id: string;
    name: string;
    description: string;
    planId: string;
    price: string;
    priceSubtext?: string;
    savings?: string;
    buttonText?: string;
    featureHeader?: string;
    features?: string[];
    tier: "monthly" | "quarterly" | "annual";
    checkoutUrl: string;
};

const plans: SubscriptionPlan[] = [
    {
        id: "monthly",
        planId: "monthly-tier",
        name: "Monthly Plan",
        description: "Billed monthly, cancel anytime.",
        price: "$14.99/mo",
        priceSubtext: "/month",
        tier: "monthly",
        checkoutUrl: "/checkout?plan=monthly-tier",
        buttonText: "Choose Monthly Plan",
        featureHeader: "Includes:",
        features: ["Unlimited creativity", "Kid-safe AI", "Cancel anytime"],
    },
    {
        id: "quarterly",
        planId: "quarterly-tier",
        name: "Quarterly Plan",
        description: "Save more with a 3-month commitment.",
        price: "$35.97/quarter",
        priceSubtext: "/quarter",
        savings: "Save 20%",
        tier: "quarterly",
        checkoutUrl: "/checkout?plan=quarterly-tier",
        buttonText: "Choose Quarterly Plan",
        featureHeader: "Includes:",
        features: ["All monthly features", "Priority support", "20% savings"],
    },
    {
        id: "annual",
        planId: "annual-tier",
        name: "Annual Plan",
        description: "Best value. Save 30%.",
        price: "$119.99/year",
        priceSubtext: "/year",
        savings: "Save 30%",
        tier: "annual",
        checkoutUrl: "/checkout?plan=annual-tier",
        buttonText: "Choose Annual Plan",
        featureHeader: "Includes:",
        features: ["All quarterly features", "Bonus content", "30% savings"],
    },
];

interface SubscriptionPlansProps {
    plans?: SubscriptionPlan[];
    onSelectPlan?: (plan: SubscriptionPlan) => void;
    trialUsed?: boolean;
    hasActiveSubscription?: boolean;
    currentPlanId?: string;
}

export const SubscriptionPlans: React.FC<SubscriptionPlansProps> = ({
    plans: providedPlans,
    onSelectPlan,
    trialUsed = false,
    hasActiveSubscription = false,
    currentPlanId,
}) => {
    const displayPlans = providedPlans || plans;

    return (
        <div className="grid gap-6 md:grid-cols-3">
            {displayPlans.map((plan) => (
                <div
                    key={plan.id}
                    className="border p-6 rounded-xl shadow-sm flex flex-col items-center justify-center"
                >
                    <h3
                        className={`${fredoka.className} font-[900] text-lg text-gray-900`}
                    >
                        {plan.name}
                    </h3>
                    <p className="text-sm text-gray-400 font-light">
                        {plan.description}
                    </p>
                    {plan.savings && (
                        <p className="text-sm text-green-600 font-medium mt-1">
                            {plan.savings}
                        </p>
                    )}
                    <p className="text-xl font-[500] mt-4 text-gray-900">
                        {plan.price}
                        {plan.priceSubtext && (
                            <span className="text-sm text-gray-900 ml-1">
                                {plan.priceSubtext}
                            </span>
                        )}
                    </p>
                    <button
                        type="button"
                        onClick={() =>
                            onSelectPlan
                                ? onSelectPlan(plan)
                                : window.location.href = plan.checkoutUrl
                        }
                        className={`mt-6 inline-block px-4 py-2 w-fit text-white cursor-pointer rounded-full text-center ${
                            currentPlanId === plan.planId
                                ? 'bg-green-600 hover:bg-green-700'
                                : 'bg-black hover:bg-black/90'
                        }`}
                        disabled={currentPlanId === plan.planId}
                    >
                        {currentPlanId === plan.planId
                            ? "Current Plan"
                            : hasActiveSubscription
                                ? "Upgrade to This Plan"
                                : "Subscribe Now" // HIDE TRIAL UI: Always show direct subscription
                        }
                    </button>
                </div>
            ))}
        </div>
    );
};

// Add default export to support both import styles
export default SubscriptionPlans;
