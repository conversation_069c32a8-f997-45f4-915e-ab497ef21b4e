import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { createServerSupabaseClient } from '@/lib/supabase/server';

export async function GET() {
  const supabase = await createServerSupabaseClient();
  const { data: { user } } = await supabase.auth.getUser();

  if (!user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const profile = await prisma.profile.findUnique({
      where: { id: user.id },
      select: {
        subscription_status: true,
        plan_name: true,
        plan_id: true,
        subscription_start: true,
        subscription_end: true,
        billing_cycle: true,
        trial_end: true,
        trial_start: true,
        trial_used: true,
      },
    });

    if (!profile) {
      return NextResponse.json({ error: 'Profile not found' }, { status: 404 });
    }

    // Enhanced status logic: Check for trial expiration and payment records
    let enhancedStatus = { ...profile };
    let statusUpdated = false;

    // Check for trialing status that should be active
    if (profile.subscription_status === 'trialing') {
      // Check if there are successful payments regardless of trial expiration
      const successfulPayments = await prisma.payment.findMany({
        where: {
          profile_id: user.id,
          status: 'succeeded',
          created_at: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Within last 30 days
          }
        },
        orderBy: { created_at: 'desc' },
        take: 1
      });

      if (successfulPayments.length > 0) {
        const latestPayment = successfulPayments[0];

        // If trial has ended OR if we have payments after trial start, update to active
        const trialEnd = profile.trial_end ? new Date(profile.trial_end) : null;
        const now = new Date();
        const trialExpired = trialEnd && now > trialEnd;
        const paymentAfterTrialStart = profile.trial_start ?
          latestPayment.created_at > new Date(profile.trial_start) : true;

        if (trialExpired || paymentAfterTrialStart) {
          console.log(`Updating user ${user.id} from trialing to active:`, {
            trialExpired,
            paymentAfterTrialStart,
            latestPaymentDate: latestPayment.created_at,
            trialEnd: trialEnd?.toISOString(),
            paymentAmount: latestPayment.amount
          });

          await prisma.profile.update({
            where: { id: user.id },
            data: {
              subscription_status: 'active',
              trial_used: true,
              updated_at: new Date()
            }
          });

          enhancedStatus.subscription_status = 'active';
          enhancedStatus.trial_used = true;
          statusUpdated = true;
        }
      }
    }

    // Check for incomplete status that should be active
    if (profile.subscription_status === 'incomplete' && !statusUpdated) {
      const successfulPayments = await prisma.payment.findMany({
        where: {
          profile_id: user.id,
          status: 'succeeded',
          created_at: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Within last 30 days
          }
        },
        orderBy: { created_at: 'desc' },
        take: 1
      });

      if (successfulPayments.length > 0) {
        console.log(`Updating user ${user.id} from incomplete to active due to successful payments`);

        await prisma.profile.update({
          where: { id: user.id },
          data: {
            subscription_status: 'active',
            updated_at: new Date()
          }
        });

        enhancedStatus.subscription_status = 'active';
        statusUpdated = true;
      }
    }

    return NextResponse.json(enhancedStatus);
  } catch (error) {
    console.error('Error fetching subscription status:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
