import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Send } from "lucide-react";
import { TestimonialForm } from "./testimonials";
import { useTestimonialManager } from "./testimonials/useTestimonialManager";
import { toast } from "sonner";

const TestimonialManager = () => {
    const {
        showAddDialog,
        setShowAddDialog,
        newTestimonial,
        setNewTestimonial,
        addTestimonial,
    } = useTestimonialManager();

    const [isSubmitting, setIsSubmitting] = React.useState(false);
    const [error, setError] = React.useState<Error | null>(null);

    React.useEffect(() => {
        if (error) {
            toast.error("There was a problem submitting your feedback", {
                description:
                    error instanceof Error
                        ? error.message
                        : "Please try again later",
            });
        }
    }, [error]);

    const handleFormChange = (
        field: keyof typeof newTestimonial,
        value: string | number
    ) => {
        setNewTestimonial(field, value);
    };

    const handleSubmitTestimonial = async () => {
        try {
            setIsSubmitting(true);
            setError(null);
            await addTestimonial();
            setIsSubmitting(false);
        } catch (err) {
            setError(
                err instanceof Error
                    ? err
                    : new Error("Failed to submit testimonial")
            );
            setIsSubmitting(false);
        }
    };

    return (
        <div className="space-y-6">
            <div className="flex justify-between items-center">
                <h2 className="text-2xl font-bold">Your Feedback</h2>
                <Button
                    onClick={() => setShowAddDialog(true)}
                    className="gap-2"
                >
                    <Send className="h-4 w-4" />
                    Share Your Experience
                </Button>
            </div>

            <div className="bg-spark-background p-6 rounded-lg">
                <p className="text-gray-700">
                    We value your feedback! Share your experience with Little
                    Spark to help us improve and inspire others. Your
                    testimonial may be featured on our website after review.
                </p>
            </div>

            <TestimonialForm
                open={showAddDialog}
                onOpenChange={setShowAddDialog}
                formData={newTestimonial}
                onChange={handleFormChange}
                onSubmit={handleSubmitTestimonial}
                isSubmitting={isSubmitting}
            />
        </div>
    );
};

export default TestimonialManager;
