"use client";
import React, { useState, useRef } from "react";
import { Mic, Loader2, Square } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
    Toolt<PERSON>,
    TooltipContent,
    Toolt<PERSON>Provider,
    TooltipTrigger,
} from "@/components/ui/tooltip";
import { toast } from "sonner";
// TODO: Replace with custom backend API calls
// import { supabase } from "@/lib/supabase/client";

interface VoiceInputProps {
    onTranscriptReceived: (text: string) => void;
    buttonVariant?: "default" | "outline" | "ghost";
    buttonSize?: "sm" | "default";
    tooltipSide?: "top" | "right" | "bottom" | "left";
    className?: string
}

// TODO: Replace this with your custom voice-to-text API endpoint
const callCustomVoiceToText = async (): Promise<string> => {
    // Placeholder for future custom backend integration
    try {
        // Example of what the custom API call might look like:
        // const response = await fetch('/api/voice-to-text', {
        //   method: 'POST',
        //   headers: { 'Content-Type': 'application/json' },
        //   body: JSON.stringify({ audio: _base64Audio })
        // });
        //
        // if (!response.ok) throw new Error('API call failed');
        // const data = await response.json();
        // return data.text;

        console.log("[PLACEHOLDER] Would send audio to voice-to-text service");

        // For now, return a placeholder message indicating the feature needs backend setup
        throw new Error("Voice-to-text feature requires custom backend setup");
    } catch (error) {
        console.error("Error calling custom voice-to-text service:", error);
        throw error;
    }
};

const VoiceInput = ({
    onTranscriptReceived,
    buttonVariant = "outline",
    buttonSize = "sm",
    tooltipSide = "top",
    className,
}: VoiceInputProps) => {
    const [isRecording, setIsRecording] = useState(false);
    const [isProcessing, setIsProcessing] = useState(false);
    const mediaRecorderRef = useRef<MediaRecorder | null>(null);
    const audioChunksRef = useRef<Blob[]>([]);

    const startRecording = async () => {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({
                audio: true,
            });
            const mediaRecorder = new MediaRecorder(stream);
            mediaRecorderRef.current = mediaRecorder;
            audioChunksRef.current = [];

            mediaRecorder.addEventListener("dataavailable", (event) => {
                if (event.data.size > 0) {
                    audioChunksRef.current.push(event.data);
                }
            });

            mediaRecorder.addEventListener("stop", handleRecordingComplete);

            mediaRecorder.start();
            setIsRecording(true);
            toast.info(
                "Recording started. Speak clearly and tap again when done.",
                {
                    duration: 3000,
                }
            );
        } catch (error) {
            console.error("Error starting recording:", error);
            toast.error(
                "Could not access microphone. Please check permissions."
            );
        }
    };

    const stopRecording = () => {
        if (mediaRecorderRef.current && isRecording) {
            mediaRecorderRef.current.stop();
            mediaRecorderRef.current.stream
                .getTracks()
                .forEach((track) => track.stop());
            setIsRecording(false);
            setIsProcessing(true);
        }
    };

    const handleRecordingComplete = async () => {
        try {
            const audioBlob = new Blob(audioChunksRef.current, {
                type: "audio/webm",
            });

            // Convert blob to base64
            const reader = new FileReader();
            reader.readAsDataURL(audioBlob);

            reader.onloadend = async () => {
                const base64Audio = reader.result?.toString().split(",")[1];

                if (!base64Audio) {
                    throw new Error("Failed to encode audio");
                }

                try {
                    // TODO: Replace with your custom backend call
                    // In the future, you would pass base64Audio to your API
                    console.log("Audio data ready for backend:", base64Audio);
                    const transcribedText = await callCustomVoiceToText();

                    if (transcribedText) {
                        onTranscriptReceived(transcribedText);
                        toast.success("Successfully transcribed your speech!");
                    } else {
                        toast.error("No speech detected. Please try again.");
                    }
                } catch (error) {
                    console.error("Voice-to-text error:", error);
                    toast.error(
                        "Voice-to-text feature requires backend setup. Please type your message instead."
                    );
                }
            };
        } catch (error) {
            console.error("Error processing recording:", error);
            toast.error("Failed to process your speech. Please try again.");
        } finally {
            setIsProcessing(false);
        }
    };

    const toggleRecording = () => {
        if (isRecording) {
            stopRecording();
        } else {
            startRecording();
        }
    };

    return (
        <TooltipProvider>
            <Tooltip>
                <TooltipTrigger asChild>
                    <Button
                        type="button"
                        onClick={toggleRecording}
                        variant={buttonVariant}
                        size={buttonSize}
                        disabled={isProcessing}
                        className={className}
                        aria-label={
                            isRecording ? "Stop recording" : "Tap to speak"
                        }
                    >
                        {isProcessing ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                        ) : isRecording ? (
                            <Square className="h-4 w-4 fill-current text-red-500" />
                        ) : (
                            <Mic className="h-4 w-4" />
                        )}
                        {buttonSize === "default" && (
                            <span className="ml-2">
                                {isProcessing
                                    ? "Processing..."
                                    : isRecording
                                    ? "Listening..."
                                    : "Tap to Speak"}
                            </span>
                        )}
                    </Button>
                </TooltipTrigger>
                <TooltipContent side={tooltipSide}>
                    <p>
                        Use your voice instead of typing (requires backend
                        setup)
                    </p>
                </TooltipContent>
            </Tooltip>
        </TooltipProvider>
    );
};

export default VoiceInput;
