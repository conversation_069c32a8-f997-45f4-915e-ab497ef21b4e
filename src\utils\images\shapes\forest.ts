
/**
 * Draws a forest scene shape on the canvas
 */
export const drawForestShape = (ctx: CanvasRenderingContext2D, x: number, y: number, size: number) => {
  // Ground
  ctx.moveTo(x - size*0.8, y + size*0.5);
  ctx.quadraticCurveTo(
    x, y + size*0.4,
    x + size*0.8, y + size*0.5
  );
  
  // Trees - function to draw a simple tree
  const drawTree = (treeX: number, treeY: number, treeSize: number) => {
    // Trunk
    ctx.moveTo(treeX - treeSize*0.05, treeY);
    ctx.lineTo(treeX - treeSize*0.05, treeY - treeSize*0.3);
    ctx.lineTo(treeX + treeSize*0.05, treeY - treeSize*0.3);
    ctx.lineTo(treeX + treeSize*0.05, treeY);
    
    // Tree top (triangle)
    ctx.moveTo(treeX - treeSize*0.2, treeY - treeSize*0.3);
    ctx.lineTo(treeX, treeY - treeSize*0.6);
    ctx.lineTo(treeX + treeSize*0.2, treeY - treeSize*0.3);
    ctx.closePath();
    
    // Middle part
    ctx.moveTo(treeX - treeSize*0.15, treeY - treeSize*0.45);
    ctx.lineTo(treeX, treeY - treeSize*0.7);
    ctx.lineTo(treeX + treeSize*0.15, treeY - treeSize*0.45);
    ctx.closePath();
    
    // Top part
    ctx.moveTo(treeX - treeSize*0.1, treeY - treeSize*0.6);
    ctx.lineTo(treeX, treeY - treeSize*0.8);
    ctx.lineTo(treeX + treeSize*0.1, treeY - treeSize*0.6);
    ctx.closePath();
  };
  
  // Draw forest with multiple trees
  drawTree(x - size*0.5, y + size*0.4, size*0.8);
  drawTree(x, y + size*0.5, size);
  drawTree(x + size*0.4, y + size*0.3, size*0.7);
  
  // Sun
  ctx.moveTo(x + size*0.6, y - size*0.6);
  ctx.arc(x + size*0.6, y - size*0.6, size*0.15, 0, 2 * Math.PI);
  
  // Sun rays
  for (let i = 0; i < 8; i++) {
    const angle = i * Math.PI / 4;
    const startX = x + size*0.6 + Math.cos(angle) * size*0.15;
    const startY = y - size*0.6 + Math.sin(angle) * size*0.15;
    const endX = x + size*0.6 + Math.cos(angle) * size*0.25;
    const endY = y - size*0.6 + Math.sin(angle) * size*0.25;
    
    ctx.moveTo(startX, startY);
    ctx.lineTo(endX, endY);
  }
  
  // Small bushes
  ctx.moveTo(x - size*0.7, y + size*0.45);
  ctx.arc(x - size*0.7, y + size*0.45, size*0.1, 0, Math.PI);
  ctx.moveTo(x + size*0.7, y + size*0.45);
  ctx.arc(x + size*0.7, y + size*0.45, size*0.1, 0, Math.PI);
};
