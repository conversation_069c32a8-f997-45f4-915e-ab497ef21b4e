import React from "react";
import { fredoka } from "@/lib/fonts";

interface PrivacyQuestionProps {
    question: string;
    answer: string;
}

const PrivacyQuestion = ({ question, answer }: PrivacyQuestionProps) => {
    return (
        <div className="border-b border-gray-200 pb-4">
            <h3 className={`text-xl font-semibold mb-2 ${fredoka.className}`}>
                {question}
            </h3>
            <p className="text-gray-700">{answer}</p>
        </div>
    );
};

export default PrivacyQuestion;
