import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { createServerSupabaseClient } from '@/lib/supabase/server';

// GET /api/user-progress - Get user's progress, badges, and statistics
export async function GET() {
  try {
    // Get authenticated user
    const supabase = await createServerSupabaseClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Authentication required' 
        },
        { status: 401 }
      );
    }

    // Get user's content count by type
    const contentStats = await prisma.userContent.groupBy({
      by: ['type'],
      where: {
        user_id: user.id,
        type: {
          in: ['story', 'art', 'music'] // Only count creative content
        }
      },
      _count: {
        id: true
      }
    });

    // Get challenge completions
    const challengeCompletions = await prisma.challengeCompletion.findMany({
      where: {
        user_id: user.id
      },
      include: {
        challenge: {
          select: {
            type: true,
            difficulty: true
          }
        }
      }
    });

    // Calculate total projects created
    const totalProjects = contentStats.reduce((sum, stat) => sum + stat._count.id, 0);

    // Calculate content by type
    const contentByType = contentStats.reduce((acc, stat) => {
      acc[stat.type] = stat._count.id;
      return acc;
    }, {} as Record<string, number>);

    // Calculate challenge completions by type
    const challengesByType = challengeCompletions.reduce((acc, completion) => {
      const type = completion.challenge.type;
      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Calculate streak (simplified - consecutive days with activity)
    const recentActivity = await prisma.userContent.findMany({
      where: {
        user_id: user.id,
        created_at: {
          gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
        }
      },
      select: {
        created_at: true
      },
      orderBy: {
        created_at: 'desc'
      }
    });

    // Calculate streak days (simplified logic)
    const streakDays = calculateStreakDays(recentActivity.map(a => a.created_at));

    // Calculate badges
    const badges = calculateBadges(contentByType, challengesByType, totalProjects);

    // Calculate skill level based on total activity
    const skillLevel = calculateSkillLevel(totalProjects, challengeCompletions.length);

    // Calculate skill progress (percentage to next level)
    const skillProgress = calculateSkillProgress(skillLevel, totalProjects);

    return NextResponse.json({
      success: true,
      progress: {
        completedProjects: totalProjects,
        skillLevel,
        skillProgress,
        streakDays,
        badges,
        challengesCompleted: challengeCompletions.length,
        contentByType,
        challengesByType,
        statistics: {
          totalContent: totalProjects,
          totalChallenges: challengeCompletions.length,
          storiesCreated: contentByType.story || 0,
          artCreated: contentByType.art || 0,
          musicCreated: contentByType.music || 0
        }
      }
    });

  } catch (error) {
    console.error('Error fetching user progress:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch user progress' 
      },
      { status: 500 }
    );
  }
}

// Helper function to calculate badges based on user activity
function calculateBadges(
  contentByType: Record<string, number>, 
  challengesByType: Record<string, number>,
  totalProjects: number
) {
  return [
    {
      name: "Storyteller",
      icon: "📝",
      earned: (contentByType.story || 0) >= 1 || (challengesByType.story || 0) >= 1
    },
    {
      name: "Artist", 
      icon: "🎨",
      earned: (contentByType.art || 0) >= 1 || (challengesByType.art || 0) >= 1
    },
    {
      name: "Musician",
      icon: "🎵", 
      earned: (contentByType.music || 0) >= 1 || (challengesByType.music || 0) >= 1
    },
    {
      name: "Game Designer",
      icon: "🎮",
      earned: (challengesByType.game || 0) >= 1
    },
    {
      name: "Creative Star",
      icon: "⭐",
      earned: totalProjects >= 5
    },
    {
      name: "Level 2",
      icon: "🏆",
      earned: totalProjects >= 3
    },
    {
      name: "5 Day Streak",
      icon: "🔥", 
      earned: false // Will be calculated based on actual streak
    }
  ];
}

// Helper function to calculate skill level
function calculateSkillLevel(totalProjects: number, challengesCompleted: number): number {
  const totalActivity = totalProjects + challengesCompleted;
  
  if (totalActivity >= 20) return 5;
  if (totalActivity >= 15) return 4;
  if (totalActivity >= 10) return 3;
  if (totalActivity >= 5) return 2;
  return 1;
}

// Helper function to calculate skill progress percentage
function calculateSkillProgress(currentLevel: number, totalProjects: number): number {
  const levelThresholds = [0, 5, 10, 15, 20, 25];
  const currentThreshold = levelThresholds[currentLevel - 1] || 0;
  const nextThreshold = levelThresholds[currentLevel] || 25;
  
  const progress = ((totalProjects - currentThreshold) / (nextThreshold - currentThreshold)) * 100;
  return Math.min(Math.max(progress, 0), 100);
}

// Helper function to calculate streak days (simplified)
function calculateStreakDays(dates: Date[]): number {
  if (dates.length === 0) return 0;
  
  // Simple implementation - count unique days in last week
  const uniqueDays = new Set();
  const oneWeekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
  
  dates.forEach(date => {
    if (date >= oneWeekAgo) {
      const dayKey = date.toDateString();
      uniqueDays.add(dayKey);
    }
  });
  
  return uniqueDays.size;
}
