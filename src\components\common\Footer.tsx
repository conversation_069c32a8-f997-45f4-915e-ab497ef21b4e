import React, { useState } from 'react';
import Link from 'next/link';
import { Mail, Instagram } from 'lucide-react';
import { footerContent } from '@/lib/constants';
import { fredoka } from '@/lib/fonts';

const Footer = () => {
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [message, setMessage] = useState('');

  const handleNewsletterSignup = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email || !email.includes('@')) {
      setMessage("Please enter a valid email address");
      return;
    }
    
    setIsSubmitting(true);
    setMessage('');
    
    try {
      // Simulate newsletter signup - replace with actual implementation
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setMessage("Thanks for signing up for our newsletter!");
      setEmail('');
    } catch (error) {
      console.error('Newsletter error:', error);
      setMessage("Something went wrong. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <footer className="bg-white pt-12 pb-6 border-t border-gray-100">
      <div className="container mx-auto px-4">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {/* Company Info */}
          <div className="flex flex-col items-center md:items-start text-center md:text-left">
            <h3 className={`text-lg text-gray-900 font-semibold mb-4 ${fredoka.className}`}>
              {footerContent.company.name}
            </h3>
            <p className="text-sm text-gray-600 mb-4">
              {footerContent.company.tagline}
            </p>
            <div className="flex space-x-4 mt-4">
              <a 
                href={footerContent.social.instagram} 
                target="_blank" 
                rel="noopener noreferrer" 
                aria-label="Instagram" 
                className="text-gray-600 hover:text-littlespark-primary transition-colors"
              >
                <Instagram className="h-5 w-5" />
              </a>
            </div>
          </div>

          {/* Links */}
          <div className="flex flex-col items-center md:items-start">
            <h3 className={`text-lg text-gray-900 font-semibold mb-4 ${fredoka.className}`}>
              Links
            </h3>
            <ul className="space-y-2 text-center md:text-left">
              {footerContent.links.map((link) => (
                <li key={link.href}>
                  <Link 
                    href={link.href} 
                    className="text-sm text-gray-500 font-light hover:text-littlespark-primary transition-colors"
                  >
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Info & Newsletter */}
          <div className="md:col-span-2 lg:col-span-1">
            <h3 className={`text-lg text-gray-900 mb-4 ${fredoka.className} text-center md:text-left`}>
              {footerContent.contact.title}
            </h3>
            <div className="space-y-3 mb-6 flex justify-center md:justify-start">
              <a 
                href={`mailto:${footerContent.contact.email}`} 
                className="flex items-center gap-2 text-sm text-gray-600 hover:text-littlespark-primary transition-colors"
              >
                <Mail className="h-4 w-4" />
                <span>{footerContent.contact.email}</span>
              </a>
            </div>
            
            <h3 className={`text-lg text-gray-900 font-semibold mb-4 ${fredoka.className}`}>
              {footerContent.newsletter.title}
            </h3>
            <form onSubmit={handleNewsletterSignup} className="space-y-2">
              <div className="flex flex-col sm:flex-row max-w-md gap-2">
                <input
                  type="email"
                  placeholder={footerContent.newsletter.placeholder}
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  className="flex-grow px-3 py-2 border border-gray-300 rounded-full text-sm 
                           focus:outline-none focus:ring-2 placeholder:text-gray-400 focus:ring-littlespark-primary/50 
                           focus:border-littlespark-primary disabled:opacity-50 min-h-[44px]"
                  disabled={isSubmitting}
                />
                <button 
                  type="submit" 
                  disabled={isSubmitting}
                  className="px-4 py-2 bg-littlespark-primary text-white rounded-full text-sm 
                           hover:bg-littlespark-primary-hover transition-colors disabled:opacity-50
                           disabled:cursor-not-allowed min-h-[44px]"
                >
                  {isSubmitting ? footerContent.newsletter.buttonSubmitting : footerContent.newsletter.buttonText}
                </button>
              </div>
              <p className="text-xs text-gray-600">
                {footerContent.newsletter.description}
              </p>
              {message && (
                <p className={`text-xs mt-2 ${
                  message.includes('Thanks') ? 'text-green-600' : 'text-red-600'
                }`}>
                  {message}
                </p>
              )}
            </form>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="pt-6 border-t border-gray-100">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <p className="text-xs text-gray-600 text-center md:text-left">
              © {new Date().getFullYear()} {footerContent.copyright}
            </p>
            <div className="flex flex-wrap justify-center gap-4 sm:gap-6">
              {footerContent.bottomLinks.map((link) => (
                <Link 
                  key={link.href}
                  href={link.href} 
                  className="text-xs text-gray-600 hover:text-littlespark-primary transition-colors"
                >
                  {link.label}
                </Link>
              ))}
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer; 