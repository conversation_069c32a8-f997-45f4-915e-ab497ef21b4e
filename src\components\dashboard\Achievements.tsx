
import React from 'react';
import { <PERSON>, <PERSON>, Brush, Wand2, <PERSON><PERSON><PERSON>, Gamepad2, Music } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';

interface BadgeProps {
  name: string;
  icon: string;
  earned: boolean;
}

interface UserDataProps {
  completedProjects: number;
  skillLevel: number;
  streakDays: number;
  badges: Array<BadgeProps>;
}

interface AchievementsProps {
  isLoading: boolean;
  userData: UserDataProps;
}

const Achievements = ({ isLoading, userData }: AchievementsProps) => {
  // Helper function to render the correct icon based on the icon string
  const renderBadgeIcon = (iconString: string) => {
    switch(iconString) {
      case "📝": return <BookOpen className="h-6 w-6 sm:h-8 sm:w-8 md:h-10 md:w-10 text-black" />;
      case "🎨": return <Brush className="h-6 w-6 sm:h-8 sm:w-8 md:h-10 md:w-10 text-black" />;
      case "🎵": return <Music className="h-6 w-6 sm:h-8 sm:w-8 md:h-10 md:w-10 text-black" />;
      case "🎮": return <Gamepad2 className="h-6 w-6 sm:h-8 sm:w-8 md:h-10 md:w-10 text-black" />;
      case "⭐": return <Star className="h-6 w-6 sm:h-8 sm:w-8 md:h-10 md:w-10 text-black" />;
      default: return <Wand2 className="h-6 w-6 sm:h-8 sm:w-8 md:h-10 md:w-10 text-black" />;
    }
  };

  return (
    <div className="bg-white rounded-2xl shadow-md p-4 sm:p-6">
      <h2 className="text-lg sm:text-xl font-bold mb-4 sm:mb-6 flex items-center">
        <Trophy className="h-5 w-5 text-spark-yellow mr-2" />
        Badges & Achievements
      </h2>
      
      {isLoading ? (
        // Loading state
        <div className="space-y-4">
          <Skeleton className="h-16 w-full" />
          <Skeleton className="h-16 w-full" />
          <Skeleton className="h-16 w-full" />
        </div>
      ) : (
        // Content when loaded
        <div className="p-3 sm:p-4 lg:p-6 border border-gray-100 rounded-lg">
          <div className="flex flex-wrap justify-center gap-3 sm:gap-4">
            {/* Map through badges and render each one */}
            {userData.badges.map((badge, index) => (
              <div 
                key={index} 
                className={`relative flex flex-col items-center justify-center w-20 h-20 sm:w-24 sm:h-24 md:w-28 md:h-28 lg:w-32 lg:h-32 rounded-full ${
                  badge.earned 
                    ? 'bg-green-500 shadow-md transition-transform hover:scale-105' 
                    : 'bg-gray-100'
                }`}
              >
                <div className="flex items-center justify-center h-8 w-8 sm:h-10 sm:w-10 md:h-12 md:w-12 mb-1">
                  {renderBadgeIcon(badge.icon)}
                </div>
                <div className="text-xs sm:text-sm text-center font-semibold mt-1 sm:mt-2 px-1 text-black leading-tight">
                  {badge.name}
                </div>
                {badge.earned && (
                  <div className="absolute -top-1 sm:-top-2 -right-1 sm:-right-2 bg-spark-yellow h-5 w-5 sm:h-6 sm:w-6 rounded-full flex items-center justify-center">
                    <Star className="h-3 w-3 sm:h-4 sm:w-4 text-white" />
                  </div>
                )}
              </div>
            ))}
            
            {/* Streak badge (only shown if streak ≥ 3) */}
            {userData.streakDays >= 3 && (
              <div className="relative flex flex-col items-center justify-center w-20 h-20 sm:w-24 sm:h-24 md:w-28 md:h-28 lg:w-32 lg:h-32 rounded-full bg-green-500 text-white shadow-md">
                <div className="flex items-center justify-center h-8 w-8 sm:h-10 sm:w-10 md:h-12 md:w-12 mb-1">
                  <div className="text-xl sm:text-2xl md:text-3xl">🔥</div>
                </div>
                <div className="text-xs sm:text-sm text-center font-semibold mt-1 sm:mt-2 px-1 text-black leading-tight">
                  <span className="sm:hidden">{userData.streakDays} Days</span>
                  <span className="hidden sm:inline">{userData.streakDays} Day Streak</span>
                </div>
                <div className="absolute -top-1 sm:-top-2 -right-1 sm:-right-2 bg-spark-yellow h-5 w-5 sm:h-6 sm:w-6 rounded-full flex items-center justify-center">
                  <Star className="h-3 w-3 sm:h-4 sm:w-4 text-white" />
                </div>
              </div>
            )}
            
            {/* Skill level badge */}
            <div className={`relative flex flex-col items-center justify-center w-20 h-20 sm:w-24 sm:h-24 md:w-28 md:h-28 lg:w-32 lg:h-32 rounded-full ${
              userData.skillLevel > 1 
                ? 'bg-green-500 shadow-md' 
                : 'bg-gray-100'
            }`}>
              <div className="flex items-center justify-center h-8 w-8 sm:h-10 sm:w-10 md:h-12 md:w-12 mb-1">
                <div className="text-xl sm:text-2xl md:text-3xl">🏆</div>
              </div>
              <div className="text-xs sm:text-sm text-center font-semibold mt-1 sm:mt-2 px-1 text-black leading-tight">Level {userData.skillLevel}</div>
              {userData.skillLevel > 1 && (
                <div className="absolute -top-1 sm:-top-2 -right-1 sm:-right-2 bg-spark-yellow h-5 w-5 sm:h-6 sm:w-6 rounded-full flex items-center justify-center">
                  <Star className="h-3 w-3 sm:h-4 sm:w-4 text-white" />
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Achievements;
