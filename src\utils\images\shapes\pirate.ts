
/**
 * Draws a pirate shape on the canvas
 */
export const drawPirateShape = (ctx: CanvasRenderingContext2D, x: number, y: number, size: number) => {
  // Pirate hat
  ctx.moveTo(x - size*0.3, y - size*0.3);
  ctx.lineTo(x + size*0.3, y - size*0.3);
  ctx.lineTo(x + size*0.2, y - size*0.5);
  ctx.lineTo(x - size*0.2, y - size*0.5);
  ctx.closePath();
  
  ctx.moveTo(x - size*0.4, y - size*0.3);
  ctx.lineTo(x + size*0.4, y - size*0.3);
  ctx.lineTo(x + size*0.3, y - size*0.2);
  ctx.lineTo(x - size*0.3, y - size*0.2);
  ctx.closePath();
  
  // Skull on hat
  ctx.moveTo(x, y - size*0.4);
  ctx.arc(x, y - size*0.4, size*0.05, 0, 2 * Math.PI);
  
  // Cross bones
  ctx.moveTo(x - size*0.1, y - size*0.35);
  ctx.lineTo(x + size*0.1, y - size*0.45);
  ctx.moveTo(x + size*0.1, y - size*0.35);
  ctx.lineTo(x - size*0.1, y - size*0.45);
  
  // Head
  ctx.moveTo(x, y - size*0.1);
  ctx.arc(x, y - size*0.1, size*0.2, 0, 2 * Math.PI);
  
  // Eye patch
  ctx.moveTo(x + size*0.05, y - size*0.15);
  ctx.arc(x, y - size*0.15, size*0.08, 0, Math.PI);
  ctx.moveTo(x + size*0.08, y - size*0.15);
  ctx.lineTo(x + size*0.15, y - size*0.25);
  
  // Beard
  for (let i = 0; i < 5; i++) {
    ctx.moveTo(x - size*0.15 + i*size*0.075, y + size*0.05);
    ctx.quadraticCurveTo(
      x - size*0.15 + i*size*0.075, y + size*0.15,
      x - size*0.15 + i*size*0.075, y + size*0.2
    );
  }
  
  // Body
  ctx.moveTo(x - size*0.2, y + size*0.1);
  ctx.lineTo(x - size*0.3, y + size*0.5);
  ctx.lineTo(x + size*0.3, y + size*0.5);
  ctx.lineTo(x + size*0.2, y + size*0.1);
  ctx.closePath();
  
  // Hook hand
  ctx.moveTo(x + size*0.2, y + size*0.25);
  ctx.lineTo(x + size*0.4, y + size*0.2);
  ctx.arc(x + size*0.4, y + size*0.25, size*0.05, -Math.PI/2, Math.PI);
};
