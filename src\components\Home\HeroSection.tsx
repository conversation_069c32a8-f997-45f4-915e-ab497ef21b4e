import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { fredoka } from '@/lib/fonts';
import { heroContent, brandAssets } from '@/lib/constants';

const HeroSection = () => {
  return (
    <main className="bg-gradient-to-b from-purple-50 to-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12 lg:py-16">
        <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-center">
          {/* Left Side - Hero Content */}
          <div className="space-y-6 sm:space-y-8">
            <h1 className={`text-3xl sm:text-4xl md:text-5xl lg:text-6xl text-gray-900 font-bold leading-tight ${fredoka.className}`}>
              {heroContent.title.main}{" "}
              <span className="text-littlespark-primary">{heroContent.title.highlight}</span> {heroContent.title.suffix}
            </h1>
            
            <p className={`text-lg sm:text-xl text-gray-600 leading-relaxed ${fredoka.className}`}>
              {heroContent.subtitle}
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4">
              <Link
                href="/auth"
                className={`bg-littlespark-primary hover:bg-littlespark-primary-hover text-white px-6 py-3 sm:px-8 sm:py-4 rounded-full text-base sm:text-lg font-semibold transition-colors inline-flex items-center justify-center min-h-[48px] ${fredoka.className}`}
              >
                {heroContent.buttons.primary}
                <svg className="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </Link>
              <button className={`border-2 border-littlespark-primary text-littlespark-primary hover:bg-littlespark-primary hover:text-white px-6 py-3 sm:px-8 sm:py-4 rounded-full text-base sm:text-lg font-semibold transition-colors min-h-[48px] ${fredoka.className}`}>
                {heroContent.buttons.secondary}
              </button>
            </div>
          </div>

          {/* Right Side - Dashboard Preview */}
          <div className="relative overflow-hidden shadow-2xl rounded-2xl animate-float">
            <Image
              src={brandAssets.dashboardPreview.src}
              alt={brandAssets.dashboardPreview.alt}
              width={brandAssets.dashboardPreview.width}
              height={brandAssets.dashboardPreview.height}
              className="w-full h-auto hover:scale-105 duration-300"
            />
          </div>
        </div>
      </div>
    </main>
  );
};

export default HeroSection; 