import { NextRequest, NextResponse } from 'next/server';

const CMS_BASE_URL = process.env.CMS_BASE_URL || 'http://localhost:3001';

export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const { slug } = params;
    
    const cmsUrl = `${CMS_BASE_URL}/api/challenges?where[slug][equals]=${slug}&where[status][equals]=published`;
    console.log('Fetching challenge from CMS:', cmsUrl);
    
    const response = await fetch(cmsUrl, {
      headers: {
        'Content-Type': 'application/json',
      },
      signal: AbortSignal.timeout(10000),
    });
    
    if (!response.ok) {
      throw new Error(`CMS API error: ${response.status}`);
    }
    
    const data = await response.json();
    const challenge = data.docs?.[0];
    
    if (!challenge) {
      return NextResponse.json(
        { success: false, error: 'Challenge not found' },
        { status: 404 }
      );
    }
    
    // Transform challenge data
    const transformedChallenge = {
      id: challenge.id,
      title: challenge.title,
      slug: challenge.slug,
      description: challenge.description,
      category: challenge.category,
      ageGroup: challenge.ageGroup,
      difficulty: challenge.difficulty,
      estimatedTime: challenge.estimatedTime,
      instructions: challenge.instructions,
      learningObjectives: challenge.learningObjectives,
      materials: challenge.materials,
      media: challenge.media?.map((m: any) => ({
        ...m,
        file: {
          ...m.file,
          url: m.file.url.startsWith('http') ? m.file.url : `${CMS_BASE_URL}${m.file.url}`
        }
      })),
      subscriptionTier: challenge.subscriptionTier,
      featured: challenge.featured,
      seasonal: challenge.seasonal,
      publishedAt: challenge.publishedAt,
      createdBy: challenge.createdBy,
    };
    
    return NextResponse.json({
      success: true,
      challenge: transformedChallenge,
    });
    
  } catch (error) {
    console.error('Error fetching challenge from CMS:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch challenge',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
