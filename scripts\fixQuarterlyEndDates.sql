-- Fix quarterly subscription end dates
-- This script finds quarterly subscriptions with incorrect end dates and fixes them

-- First, let's see what quarterly subscriptions we have
SELECT 
    email,
    plan_id,
    billing_cycle,
    subscription_start,
    subscription_end,
    subscription_start + INTERVAL '3 months' as correct_end_date,
    EXTRACT(DAY FROM (subscription_end - (subscription_start + INTERVAL '3 months'))) as days_difference
FROM profiles 
WHERE plan_id = 'quarterly-tier' 
    AND subscription_status IN ('active', 'trialing', 'cancel_at_period_end')
    AND subscription_start IS NOT NULL 
    AND subscription_end IS NOT NULL;

-- Update quarterly subscriptions with incorrect end dates
-- (where the difference is more than 5 days)
UPDATE profiles 
SET 
    subscription_end = subscription_start + INTERVAL '3 months',
    billing_cycle = 'quarter',
    updated_at = NOW()
WHERE plan_id = 'quarterly-tier' 
    AND subscription_status IN ('active', 'trialing', 'cancel_at_period_end')
    AND subscription_start IS NOT NULL 
    AND subscription_end IS NOT NULL
    AND ABS(EXTRACT(DAY FROM (subscription_end - (subscription_start + INTERVAL '3 months')))) > 5;

-- Verify the fix
SELECT 
    email,
    plan_id,
    billing_cycle,
    subscription_start,
    subscription_end,
    EXTRACT(DAY FROM (subscription_end - subscription_start)) as duration_days
FROM profiles 
WHERE plan_id = 'quarterly-tier' 
    AND subscription_status IN ('active', 'trialing', 'cancel_at_period_end')
    AND subscription_start IS NOT NULL 
    AND subscription_end IS NOT NULL
ORDER BY email; 