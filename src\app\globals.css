@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #1f2937;
  
  /* Little Spark Brand Colors */
  --color-littlespark-primary: #00c69e;
  --color-littlespark-primary-hover: #00b389;
  --color-littlespark-secondary: #00c78f;
  --color-littlespark-lavender: #9333ea;
  --color-littlespark-yellow: #F5E45F;
  --color-littlespark-blue: #3b82f6;
  --color-littlespark-light-blue: #cef2ff;
  --color-littlespark-light-blue-2: #eff6ff;
  --color-littlespark-pink: #ec4899;
  --color-littlespark-teal: #14b8a6;
  --color-littlespark-orange: #f97316;
  
  /* Gray Scale */
  --color-gray-900: #1f2937;
  --color-gray-600: #4b5563;
  --color-gray-500: #6b7280;
  --color-gray-100: #f3f4f6;
  --color-gray-50: #f9fafb;
  
  /* Semantic Colors */
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  
  /* Background Variants */
  --color-purple-50: #faf5ff;
  --color-yellow-light: rgba(245, 228, 95, 0.1);
  
  /* UI Component Colors */
  --color-primary: var(--color-littlespark-primary);
  --color-secondary: #f1f5f9;
  --color-muted: #64748b;
  --color-muted-foreground: #64748b;
  --color-foreground: var(--foreground);
  --color-spark-purple: #9333ea;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --font-nunito: var(--font-nunito);
  --font-fredoka: var(--font-fredoka);
  
  /* Custom Little Spark Colors for Tailwind */
  --color-littlespark-primary: var(--color-littlespark-primary);
  --color-littlespark-primary-hover: var(--color-littlespark-primary-hover);
  --color-littlespark-secondary: var(--color-littlespark-secondary);
  --color-littlespark-lavender: var(--color-littlespark-lavender);
  --color-littlespark-yellow: var(--color-littlespark-yellow);
  --color-littlespark-blue: var(--color-littlespark-blue);
  --color-littlespark-light-blue: var(--color-littlespark-light-blue);
  --color-littlespark-light-blue-2: var(--color-littlespark-light-blue-2);
  --color-littlespark-pink: var(--color-littlespark-pink);
  --color-littlespark-teal: var(--color-littlespark-teal);
  --color-littlespark-orange: var(--color-littlespark-orange);
  
  /* UI Colors for Components */
  --color-primary: var(--color-littlespark-primary);
  --color-secondary: #f1f5f9;
  --color-muted: #64748b;
  --color-muted-foreground: #64748b;
  --color-spark-purple: #9333ea;
  
  /* Marquee Animation */
  --animate-marquee: marquee 15s linear infinite;
  
  /* Scroll Animations */
  --animate-fade-in-up: fadeInUp 0.6s ease-out forwards;
  --animate-fade-in-up-delay-1: fadeInUp 0.6s ease-out 0.1s forwards;
  --animate-fade-in-up-delay-2: fadeInUp 0.6s ease-out 0.2s forwards;
  --animate-fade-in-up-delay-3: fadeInUp 0.6s ease-out 0.3s forwards;
  --animate-fade-in-up-delay-4: fadeInUp 0.6s ease-out 0.4s forwards;
  --animate-fade-in-up-delay-5: fadeInUp 0.6s ease-out 0.5s forwards;
  --animate-fade-in-up-delay-6: fadeInUp 0.6s ease-out 0.6s forwards;
  --animate-fade-in-up-delay-7: fadeInUp 0.6s ease-out 0.7s forwards;
  --animate-fade-in: fadeIn 0.8s ease-out forwards;

  --animate-float: float 3s ease-in-out infinite;
}

/* Force light theme always */
@media (prefers-color-scheme: dark) {
  :root {
    --background: #ffffff;
    --foreground: #1f2937;
  }
}


* {
  box-sizing: border-box;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-nunito), 'Nunito', Arial, Helvetica, sans-serif;
  overflow-x: hidden;
}

/* Marquee Animation Keyframes */
@keyframes marquee {
  0% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(-50%);
  }
}

/* Fade In Up Animation */
@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Fade In Animation */
@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

/* Utility Classes */
.text-muted-foreground {
  color: var(--color-muted-foreground);
}

.bg-secondary {
  background-color: var(--color-secondary);
}

.bg-primary {
  background-color: var(--color-primary);
}

.text-primary {
  color: var(--color-primary);
}

.border-primary {
  border-color: var(--color-primary);
}

.bg-spark-purple {
  background-color: var(--color-spark-purple);
}

.text-foreground {
  color: var(--color-foreground);
}

.font-quicksand {
  font-family: 'Quicksand', sans-serif;
}

.font-nunito {
  font-family: var(--font-nunito), 'Nunito', sans-serif;
}

.font-fredoka {
  font-family: var(--font-fredoka), 'Fredoka', sans-serif;
}
