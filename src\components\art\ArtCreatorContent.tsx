import React from 'react';
import ArtControls from './ArtControls';
import ArtDisplay from './ArtDisplay';
import ArtErrorDisplay from './ArtErrorDisplay';
import { ArtStyleType } from '@/hooks/art/useArtStyle';

interface ArtCreatorContentProps {
  prompt: string;
  setPrompt: (prompt: string) => void;
  title: string;
  setTitle: (title: string) => void;
  artStyle: ArtStyleType;
  setArtStyle: (style: ArtStyleType) => void;
  learningMode: boolean;
  handleGenerateArt: () => void;
  handleImprovePrompt?: () => void;
  isGenerating: boolean;
  isGeneratingIdea: boolean;
  isImprovingPrompt?: boolean;
  aspectRatio: string;
  setAspectRatio: (ratio: string) => void;
  handleGenerateIdea: () => void;
  generatedImage: string | null;
  handleSaveToPortfolio?: () => void;
  errorMessage: string | null;
  isSaving?: boolean;
  isSaved?: boolean;
  portfolioImage?: string | null;
}

const ArtCreatorContent = ({
  prompt,
  setPrompt,
  title,
  setTitle,
  artStyle,
  setArtStyle,
  learningMode,
  handleGenerateArt,
  handleImprovePrompt,
  isGenerating,
  isGeneratingIdea,
  isImprovingPrompt,
  aspectRatio,
  setAspectRatio,
  handleGenerateIdea,
  generatedImage,
  handleSaveToPortfolio,
  errorMessage,
  isSaving,
  isSaved,
}: ArtCreatorContentProps) => {
  return (
    <div className="bg-white rounded-2xl shadow-md p-6 mb-8">
      <ArtErrorDisplay errorMessage={errorMessage} />
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <ArtControls 
            prompt={prompt}
            setPrompt={setPrompt}
            title={title}
            setTitle={setTitle}
            artStyle={artStyle}
            setArtStyle={setArtStyle}
            learningMode={learningMode}
            handleGenerateArt={handleGenerateArt}
            handleImprovePrompt={handleImprovePrompt}
            isGenerating={isGenerating}
            isGeneratingIdea={isGeneratingIdea}
            isImprovingPrompt={isImprovingPrompt}
            aspectRatio={aspectRatio}
            setAspectRatio={setAspectRatio}
            handleGenerateIdea={handleGenerateIdea}
          />
        </div>
        
        <ArtDisplay
          generatedImage={generatedImage}
          isGenerating={isGenerating}
          title={title}
          handleGenerateArt={handleGenerateArt}
          aspectRatio={aspectRatio}
          handleSaveToPortfolio={handleSaveToPortfolio}
          isSaving={isSaving}
          isSaved={isSaved}
        />
      </div>
    </div>
  );
};

export default ArtCreatorContent;
