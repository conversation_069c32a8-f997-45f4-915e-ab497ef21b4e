const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function fixNewUserAccess() {
  try {
    console.log('🔧 Fixing new user access issues...');
    console.log('=====================================');
    
    // Find users who have no subscription status and haven't used trial
    // These are the users who would be blocked by the old logic
    const newUsersNeedingFix = await prisma.profile.findMany({
      where: {
        AND: [
          { subscription_status: null }, // No subscription status
          { trial_used: false }          // Haven't used trial
        ]
      },
      select: {
        id: true,
        email: true,
        subscription_status: true,
        trial_used: true,
        created_at: true
      }
    });

    console.log(`Found ${newUsersNeedingFix.length} new users who need access fix`);

    if (newUsersNeedingFix.length === 0) {
      console.log('✅ No users need fixing - all users have proper access setup');
      return;
    }

    console.log('\n📋 Users that will get automatic trial access:');
    newUsersNeedingFix.forEach(user => {
      console.log(`- ${user.email} (created: ${user.created_at.toLocaleDateString()})`);
    });

    console.log('\n🚀 These users will now get automatic trial access when they use creative tools');
    console.log('📝 No database changes needed - the new logic handles this automatically');

    // Also check for any users who might have been created without trial_used flag
    const usersWithoutTrialFlag = await prisma.profile.findMany({
      where: {
        trial_used: null
      },
      select: {
        id: true,
        email: true,
        trial_used: true
      }
    });

    if (usersWithoutTrialFlag.length > 0) {
      console.log(`\n⚠️ Found ${usersWithoutTrialFlag.length} users with null trial_used flag`);
      console.log('🔧 Updating these users to have trial_used: false');

      for (const user of usersWithoutTrialFlag) {
        await prisma.profile.update({
          where: { id: user.id },
          data: {
            trial_used: false,
            updated_at: new Date()
          }
        });
        console.log(`✅ Updated ${user.email}: trial_used → false`);
      }
    }

    // Test the new access logic
    console.log('\n🧪 Testing new access logic...');
    
    const { checkUserAccess } = require('../src/lib/subscription-utils');
    
    if (newUsersNeedingFix.length > 0) {
      const testUser = newUsersNeedingFix[0];
      console.log(`\n🔍 Testing access for: ${testUser.email}`);
      
      try {
        const accessResult = await checkUserAccess(testUser.id);
        console.log('Access result:', {
          hasAccess: accessResult.hasAccess,
          reason: accessResult.reason,
          isNewUserTrial: accessResult.isNewUserTrial
        });

        if (accessResult.hasAccess && accessResult.isNewUserTrial) {
          console.log('✅ New user trial logic working correctly!');
        } else {
          console.log('❌ New user trial logic not working as expected');
        }
      } catch (error) {
        console.error('❌ Error testing access logic:', error.message);
      }
    }

    console.log('\n✅ Fix completed!');
    console.log('\n📋 Summary:');
    console.log(`- ${newUsersNeedingFix.length} new users will get automatic trial access`);
    console.log(`- ${usersWithoutTrialFlag.length} users had trial_used flag fixed`);
    console.log('- New users can now access creative tools immediately');
    console.log('- Trial will be auto-activated on first creative tool use');

  } catch (error) {
    console.error('❌ Error fixing new user access:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the fix
fixNewUserAccess();
