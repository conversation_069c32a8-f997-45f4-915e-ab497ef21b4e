
import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title, DialogFooter, DialogDescription } from '@/components/ui/radix-dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Send } from 'lucide-react';
import { TestimonialFormData } from './types';

interface TestimonialFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  formData: TestimonialFormData;
  onChange: (field: keyof TestimonialFormData, value: string | number) => void;
  onSubmit: () => void;
  isSubmitting?: boolean; // Added the isSubmitting prop
}

const TestimonialForm = ({
  open,
  onOpenChange,
  formData,
  onChange,
  onSubmit,
  isSubmitting = false // Default to false if not provided
}: TestimonialFormProps) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-sm sm:max-w-[500px] mx-4">
        <DialogHeader>
          <DialogTitle>Share Your Experience</DialogTitle>
          <DialogDescription>
            Let us know what you think about Little Spark. Your feedback helps us improve!
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="testimonial-author">Your Name</Label>
            <Input 
              id="testimonial-author"
              value={formData.author}
              onChange={(e) => onChange('author', e.target.value)}
              placeholder="Enter your name"
            />
          </div>
          
          <div className="grid gap-2">
            <Label htmlFor="testimonial-role">Your Role (optional)</Label>
            <Input 
              id="testimonial-role"
              value={formData.role}
              onChange={(e) => onChange('role', e.target.value)}
              placeholder="e.g. Parent, Teacher, etc."
            />
          </div>
          
          <div className="grid gap-2">
            <Label htmlFor="testimonial-rating">Your Rating</Label>
            <Select 
              value={formData.rating.toString()}
              onValueChange={(val) => onChange('rating', parseInt(val))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select rating" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="5">5 Stars - Excellent</SelectItem>
                <SelectItem value="4">4 Stars - Great</SelectItem>
                <SelectItem value="3">3 Stars - Good</SelectItem>
                <SelectItem value="2">2 Stars - Fair</SelectItem>
                <SelectItem value="1">1 Star - Poor</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="grid gap-2">
            <Label htmlFor="testimonial-content">Your Feedback</Label>
            <Textarea 
              id="testimonial-content"
              value={formData.content}
              onChange={(e) => onChange('content', e.target.value)}
              placeholder="Share your experience with Little Spark..."
              rows={5}
            />
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>Cancel</Button>
          <Button onClick={onSubmit} disabled={isSubmitting} className="gap-2">
            {isSubmitting ? 'Submitting...' : (
              <>
                <Send className="h-4 w-4" />
                Submit
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default TestimonialForm;
