"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useProfile } from "@/hooks/useProfile";
import { useAuth } from "@/hooks/useAuth";
import { authService } from "@/lib/auth/auth-service";
import { fredoka } from "@/lib/fonts";

export default function UserProfile() {
    const router = useRouter();
    const { user, loading: authLoading } = useAuth();
    const { profile, loading: profileLoading, updateProfile } = useProfile();
    const [editing, setEditing] = useState(false);
    const [formData, setFormData] = useState({
        full_name: "",
        avatar_url: "",
    });
    const [updateLoading, setUpdateLoading] = useState(false);

    // Handle edit mode
    const handleEdit = () => {
        setFormData({
            full_name: profile?.full_name || "",
            avatar_url: profile?.avatar_url || "",
        });
        setEditing(true);
    };

    // Handle profile update
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setUpdateLoading(true);

        const result = await updateProfile(formData);

        if (result.success) {
            setEditing(false);
        }

        setUpdateLoading(false);
    };

    // Handle signout
    const handleSignOut = async () => {
        await authService.signOut();
        router.push("/auth");
    };

    if (authLoading || profileLoading) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-littlespark-primary"></div>
            </div>
        );
    }

    if (!user || !profile) {
        return (
            <div className="text-center py-20">
                <p className="text-gray-600">
                    Please sign in to view your profile.
                </p>
            </div>
        );
    }

    return (
        <div className="max-w-2xl mx-auto px-4 py-8">
            <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8">
                <div className="text-center mb-8">
                    <h1
                        className={`text-2xl font-bold text-gray-900 mb-2 ${fredoka.className}`}
                    >
                        User Profile
                    </h1>
                    <p className="text-gray-600">
                        Manage your account information
                    </p>
                </div>

                {!editing ? (
                    <div className="space-y-6">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                Email
                            </label>
                            <p className="text-gray-900 bg-gray-50 px-4 py-3 rounded-lg">
                                {profile.email}
                            </p>
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                Full Name
                            </label>
                            <p className="text-gray-900 bg-gray-50 px-4 py-3 rounded-lg">
                                {profile.full_name || "Not set"}
                            </p>
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                Member Since
                            </label>
                            <p className="text-gray-900 bg-gray-50 px-4 py-3 rounded-lg">
                                {new Date(
                                    profile.created_at
                                ).toLocaleDateString()}
                            </p>
                        </div>

                        <div className="flex space-x-4">
                            <button
                                onClick={handleEdit}
                                className="flex-1 bg-littlespark-primary text-white px-6 py-3 rounded-lg font-medium hover:bg-littlespark-primary-hover transition-colors"
                            >
                                Edit Profile
                            </button>
                            <button
                                onClick={handleSignOut}
                                className="flex-1 bg-red-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-red-700 transition-colors"
                            >
                                Sign Out
                            </button>
                        </div>
                    </div>
                ) : (
                    <form onSubmit={handleSubmit} className="space-y-6">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                Full Name
                            </label>
                            <input
                                type="text"
                                value={formData.full_name}
                                onChange={(e) =>
                                    setFormData({
                                        ...formData,
                                        full_name: e.target.value,
                                    })
                                }
                                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-littlespark-primary focus:border-littlespark-primary"
                                placeholder="Enter your full name"
                            />
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                Avatar URL (optional)
                            </label>
                            <input
                                type="url"
                                value={formData.avatar_url}
                                onChange={(e) =>
                                    setFormData({
                                        ...formData,
                                        avatar_url: e.target.value,
                                    })
                                }
                                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-littlespark-primary focus:border-littlespark-primary"
                                placeholder="https://example.com/avatar.jpg"
                            />
                        </div>

                        <div className="flex space-x-4">
                            <button
                                type="submit"
                                disabled={updateLoading}
                                className="flex-1 bg-littlespark-primary text-white px-6 py-3 rounded-lg font-medium hover:bg-littlespark-primary-hover transition-colors disabled:opacity-50"
                            >
                                {updateLoading ? "Saving..." : "Save Changes"}
                            </button>
                            <button
                                type="button"
                                onClick={() => setEditing(false)}
                                className="flex-1 bg-gray-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-gray-700 transition-colors"
                            >
                                Cancel
                            </button>
                        </div>
                    </form>
                )}
            </div>
        </div>
    );
}
