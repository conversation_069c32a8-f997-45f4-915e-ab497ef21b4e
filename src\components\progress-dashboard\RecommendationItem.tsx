
import React from 'react';
import { Button } from '@/components/ui/button';

interface RecommendationItemProps {
  text: string;
  icon: React.ReactNode;
  path: string;
  colorClass: string;
  onClick: (path: string) => void;
}

const RecommendationItem = ({ text, icon, path, colorClass, onClick }: RecommendationItemProps) => {
  return (
    <Button 
      variant="ghost" 
      className={`w-full justify-start p-3 rounded-lg flex items-center gap-2 ${colorClass} transition-colors`}
      onClick={() => onClick(path)}
    >
      {icon}
      <div>{text}</div>
    </Button>
  );
};

export default RecommendationItem;
