import React, { useState } from "react";
import {
    Card,
    CardContent,
    CardDescription,
    Card<PERSON>ooter,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import { Button } from "../ui/button";
import { Gift, ChevronDown, ChevronUp } from "lucide-react";
import { SubscriptionPlan } from "./SubscriptionPlans";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import GiftSubscriptionDialog from "./GiftSubscriptionDialog";

interface GiftFormData {
    recipientEmail: string;
    senderName: string;
    giftMessage: string;
    deliveryDate: string;
    nameOnCard: string;
    cardNumber: string;
    expiryDate: string;
    cvv: string;
}

interface GiftSubscriptionCardProps {
    plans: SubscriptionPlan[];
    onGiftPlan: (plan: SubscriptionPlan) => void;
}

const GiftSubscriptionCard = ({
    plans,
    onGiftPlan,
}: GiftSubscriptionCardProps) => {
    const [selectedPlanId, setSelectedPlanId] = useState(plans[1].planId); // Default to quarterly plan
    const [expanded, setExpanded] = useState(false);
    const [showGiftDialog, setShowGiftDialog] = useState(false);
    const [isProcessing, setIsProcessing] = useState(false);

    const selectedPlan =
        plans.find((plan) => plan.planId === selectedPlanId) || plans[1];

    // Calculate actual price from displayed price
    const getActualPrice = (plan: SubscriptionPlan) => {
        const priceStr = plan.price.replace("$", "");
        const price = parseFloat(priceStr);

        if (plan.planId === "monthly-tier") {
            return price; // Monthly price is $14.99
        } else if (plan.planId === "quarterly-tier") {
            return price * 3; // Quarterly is $11.99 * 3 = $35.97
        } else if (plan.planId === "annual-tier") {
            return price * 12; // Annual is $9.99 * 12 = $119.88
        }

        return price;
    };

    const handleGiftDialogSubmit = async (formData: GiftFormData) => {
        setIsProcessing(true);
        try {
            // Here you would typically call your payment processing API
            console.log("Gift subscription data:", formData);
            await onGiftPlan(selectedPlan);
            setShowGiftDialog(false);
        } catch (error) {
            console.error("Error processing gift subscription:", error);
        } finally {
            setIsProcessing(false);
        }
    };

    return (
        <>
            <Card className="border-2 border-primary/20 shadow-md">
                <CardHeader className="bg-primary/5">
                    <div className="flex items-center justify-center space-x-2">
                        <Gift className="h-5 w-5 text-primary" />
                        <CardTitle className="font-quicksand text-xl text-gray-900">
                            Give the Gift of Little Spark
                        </CardTitle>
                    </div>
                    <CardDescription className="text-center">
                        Share creativity with someone special
                    </CardDescription>
                </CardHeader>

                <CardContent className="pt-6">
                    <div className="space-y-4">
                        <div>
                            <p className="mb-2 font-medium text-sm text-gray-900 text-center">
                                Select a plan to gift:
                            </p>
                            <Select
                                value={selectedPlanId}
                                onValueChange={setSelectedPlanId}
                            >
                                <SelectTrigger className="w-full rounded-full">
                                    <SelectValue placeholder="Select a plan" />
                                </SelectTrigger>
                                <SelectContent>
                                    {plans.map((plan) => {
                                        if (plan.planId === "quarterly-tier") {
                                            return (
                                                <SelectItem
                                                    key={plan.planId}
                                                    value={plan.planId}
                                                >
                                                    Quarterly Plan - $35.97
                                                    Billed Every 3 Months
                                                </SelectItem>
                                            );
                                        } else if (
                                            plan.planId === "annual-tier"
                                        ) {
                                            return (
                                                <SelectItem
                                                    key={plan.planId}
                                                    value={plan.planId}
                                                >
                                                    Annual Plan - $119.99 Billed
                                                    Every Year
                                                </SelectItem>
                                            );
                                        } else {
                                            return (
                                                <SelectItem
                                                    key={plan.planId}
                                                    value={plan.planId}
                                                >
                                                    {plan.name} - {plan.price}
                                                    {plan.priceSubtext}
                                                </SelectItem>
                                            );
                                        }
                                    })}
                                </SelectContent>
                            </Select>
                        </div>

                        <div className="flex items-center justify-between text-gray-900">
                            <div>
                                <p className="font-semibold ">
                                    {selectedPlan.name}
                                </p>
                                <p className="text-sm">
                                    {selectedPlan.description}
                                </p>
                                {selectedPlan.savings && (
                                    <p className="text-sm font-medium text-red-600">
                                        {selectedPlan.savings}
                                    </p>
                                )}
                            </div>
                            <div className="text-right">
                                <p className="text-xl font-bold">
                                    {selectedPlan.price}
                                    <span className="text-sm font-normal text-muted-foreground">
                                        {selectedPlan.priceSubtext}
                                    </span>
                                </p>
                                <p className="text-sm text-muted-foreground">
                                    Total: $
                                    {getActualPrice(selectedPlan).toFixed(2)}
                                </p>
                            </div>
                        </div>

                        <Button
                            variant="outline"
                            size="sm"
                            className="w-full flex items-center gap-1 hover:bg-littlespark-primary border-littlespark-primary outline-none focus:outline-none border-2 py-4 text-littlespark-primary hover:text-white bg-white "
                            onClick={() => setExpanded(!expanded)}
                        >
                            {expanded ? "Hide details" : "Show details"}
                            {expanded ? (
                                <ChevronUp className="h-4 w-4" />
                            ) : (
                                <ChevronDown className="h-4 w-4" />
                            )}
                        </Button>

                        {expanded && (
                            <div className="border border-gray-300  rounded-2xl p-4 bg-littlespark-light-blue/30 text-gray-900">
                                <p className="font-medium mb-2 text-sm">
                                    {selectedPlan.featureHeader || "Features"}
                                </p>
                                <ul className="space-y-2">
                                    {selectedPlan.features?.map(
                                        (feature, index) => (
                                            <li
                                                key={index}
                                                className="flex items-start"
                                            >
                                                <div className="mr-2 mt-0.5">
                                                    <Gift className="h-3 w-3 text-primary" />
                                                </div>
                                                <span className="text-sm">
                                                    {feature}
                                                </span>
                                            </li>
                                        )
                                    )}
                                </ul>
                            </div>
                        )}

                        <div className="bg-secondary/10 rounded-md p-4">
                            <p className="text-sm text-gray-900">
                                When you give Little Spark as a gift, the
                                recipient will receive an email with
                                instructions on how to redeem their gift
                                subscription. You can choose to send it
                                immediately or schedule it for a future date.
                            </p>
                        </div>
                    </div>
                </CardContent>

                <CardFooter className="pt-2 pb-6">
                    <Button
                        onClick={() => setShowGiftDialog(true)}
                        className="w-full bg-primary hover:bg-primary/90"
                    >
                        <Gift className="mr-2 h-4 w-4" />
                        Gift This Plan
                    </Button>
                </CardFooter>
            </Card>

            <GiftSubscriptionDialog
                open={showGiftDialog}
                onOpenChange={setShowGiftDialog}
                plan={selectedPlan}
                onSubmit={handleGiftDialogSubmit}
                isProcessing={isProcessing}
            />
        </>
    );
};

export default GiftSubscriptionCard;
