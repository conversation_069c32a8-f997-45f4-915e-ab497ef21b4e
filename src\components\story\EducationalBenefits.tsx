"use client";

import React from "react";
import { Info } from "lucide-react";
import { useStory } from "./StoryContext";

const EducationalBenefits = () => {
    const { learningMode } = useStory();

    if (!learningMode) return null;

    return (
        <div className="flex items-center gap-2 bg-blue-50 p-4 rounded-lg border border-blue-100 mb-6">
            <Info className="h-5 w-5 text-blue-500 flex-shrink-0" />
            <div>
                <h4 className="font-semibold text-blue-700">
                    Educational Benefits
                </h4>
                <p className="text-blue-600 text-sm">
                    Interactive storytelling helps improve vocabulary,
                    creativity, and reading comprehension. Keep writing to
                    develop these skills!
                </p>
            </div>
        </div>
    );
};

export default EducationalBenefits;
