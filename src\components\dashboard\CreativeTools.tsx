import React, { useState, useEffect } from "react";
import LearnTip from "../shared/LearnTip";
// Removed unused imports
import {
    CreativeToolsHeader,
    CreativeChallenge,
    CreativeToolsGrid,
} from "./creative";

const CreativeTools = () => {
    const safeMode = true;

    const [showChallenges, setShowChallenges] = useState(() => {
        const saved = localStorage.getItem("showChallenges");
        return saved !== null ? saved === "true" : true;
    });

    useEffect(() => {
        localStorage.setItem("showChallenges", String(showChallenges));
    }, [showChallenges]);

    return (
        <div className="bg-white p-6 rounded-xl shadow-sm">
            <CreativeToolsHeader
                showChallenges={showChallenges}
                setShowChallenges={setShowChallenges}
            />

            {safeMode && (
                <div className="mb-6">
                    <LearnTip
                        tip="LittleSpark Safe AI Mode is on! This means all AI responses are filtered for age-appropriate content and learning opportunities."
                        subject="Safe AI Mode"
                        type="learning"
                    />
                </div>
            )}

            {showChallenges && <CreativeChallenge />}

            <CreativeToolsGrid />
        </div>
    );
};

export default CreativeTools;
