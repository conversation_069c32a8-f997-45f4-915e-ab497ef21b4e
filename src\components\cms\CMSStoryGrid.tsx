'use client';

import { useState, useEffect } from 'react';
import { getStoryTemplates, CMSStoryTemplate, getMediaUrl } from '@/lib/cms-api';
import { useSubscriptionStatus } from '@/hooks/useSubscriptionStatus';

interface CMSStoryGridProps {
  genre?: string;
  ageGroup?: string;
  featured?: boolean;
}

export function CMSStoryGrid({ genre, ageGroup, featured }: CMSStoryGridProps) {
  const [storyTemplates, setStoryTemplates] = useState<CMSStoryTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { hasActiveSubscription } = useSubscriptionStatus();

  useEffect(() => {
    async function loadStoryTemplates() {
      try {
        setLoading(true);
        const data = await getStoryTemplates({ genre, ageGroup });
        setStoryTemplates(data);
      } catch (err) {
        setError('Failed to load story templates');
        console.error('Error loading story templates:', err);
      } finally {
        setLoading(false);
      }
    }

    loadStoryTemplates();
  }, [genre, ageGroup, featured]);

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(6)].map((_, i) => (
          <div key={`story-skeleton-${i}`} className="bg-gray-200 animate-pulse rounded-lg h-64"></div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600">{error}</p>
        <button 
          onClick={() => window.location.reload()} 
          className="mt-4 px-4 py-2 bg-blue-500 text-white rounded"
        >
          Try Again
        </button>
      </div>
    );
  }

  if (storyTemplates.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-600">No story templates found.</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {storyTemplates.map((template) => (
        <CMSStoryCard 
          key={template.id} 
          template={template} 
          hasSubscription={hasActiveSubscription}
        />
      ))}
    </div>
  );
}

interface CMSStoryCardProps {
  template: CMSStoryTemplate;
  hasSubscription: boolean;
}

function CMSStoryCard({ template, hasSubscription }: CMSStoryCardProps) {
  const isLocked = template.subscriptionTier === 'premium' && !hasSubscription;
  
  const handleClick = () => {
    if (isLocked) {
      alert('This story template requires a premium subscription!');
      return;
    }
    
    window.location.href = `/stories/${template.slug}`;
  };

  // Get the first character image for display
  const mainImage = template.characterOptions?.find(char => char.image)?.image;

  return (
    <div 
      className={`bg-white rounded-lg shadow-md overflow-hidden cursor-pointer hover:shadow-lg transition-shadow ${
        isLocked ? 'opacity-75' : ''
      }`}
      onClick={handleClick}
    >
      {/* Story Image */}
      {mainImage && (
        <div className="relative">
          <img 
            src={getMediaUrl(mainImage.url)} 
            alt={mainImage.alt || template.title}
            className="w-full h-48 object-cover"
          />
          {isLocked && (
            <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
              <div className="text-white text-center">
                <span className="text-4xl">🔒</span>
                <p className="mt-2 text-sm">Premium</p>
              </div>
            </div>
          )}
          {template.featured && (
            <div className="absolute top-2 right-2 bg-yellow-500 text-white px-2 py-1 rounded text-xs font-bold">
              ⭐ Featured
            </div>
          )}
        </div>
      )}

      <div className="p-4">
        {/* Title */}
        <h3 className="text-lg font-semibold mb-2 line-clamp-2">{template.title}</h3>
        
        {/* Description */}
        <div 
          className="text-gray-600 text-sm mb-3 line-clamp-3"
          dangerouslySetInnerHTML={{ __html: template.description }}
        />
        
        {/* Story Prompt Preview */}
        <div className="mb-3">
          <p className="text-xs font-semibold text-gray-700 mb-1">Story Starter:</p>
          <div 
            className="text-xs text-gray-600 line-clamp-2"
            dangerouslySetInnerHTML={{ __html: template.storyPrompt }}
          />
        </div>
        
        {/* Character Count */}
        {template.characterOptions && template.characterOptions.length > 0 && (
          <div className="mb-3">
            <p className="text-xs text-gray-500">
              {template.characterOptions.length} character{template.characterOptions.length !== 1 ? 's' : ''} to choose from
            </p>
          </div>
        )}
        
        {/* Footer */}
        <div className="flex justify-between items-center text-xs text-gray-500">
          <div className="flex items-center space-x-2">
            {/* Genres */}
            {template.genre && template.genre.length > 0 && (
              <span className="px-2 py-1 rounded bg-purple-100 text-purple-800">
                {template.genre[0]}
              </span>
            )}
            {/* Age Group */}
            {template.ageGroup && template.ageGroup.length > 0 && (
              <span className="px-2 py-1 rounded bg-blue-100 text-blue-800">
                {template.ageGroup[0]}
              </span>
            )}
          </div>
          {/* Estimated Length */}
          {template.estimatedLength && (
            <span>
              {template.estimatedLength.minWords}-{template.estimatedLength.maxWords} words
            </span>
          )}
        </div>
      </div>
    </div>
  );
}
