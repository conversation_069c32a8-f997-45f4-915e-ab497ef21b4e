import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { stripe } from '@/lib/stripe';
import { calculateBillingPeriod, getBillingCycleFromPlanId } from '@/lib/stripe';
import type Stripe from 'stripe';

export async function POST(request: NextRequest) {
  try {
    if (!stripe) return NextResponse.json({ error: 'Stripe not configured' }, { status: 500 });

    const { userId } = await request.json();

    if (!userId) return NextResponse.json({ error: 'User ID required' }, { status: 400 });

    const profile = await prisma.profile.findUnique({ where: { id: userId } });
    if (!profile?.subscription_id)
      return NextResponse.json({ error: 'No active subscription found' }, { status: 400 });

    // Retrieve subscription
    const subscription = await stripe.subscriptions.retrieve(profile.subscription_id, {
      expand: ['latest_invoice.payment_intent']
    });

    if (subscription.status !== 'trialing') {
      return NextResponse.json({ error: 'Subscription is not in trial' }, { status: 400 });
    }

    // End trial immediately
    const updatedSub = await stripe.subscriptions.update(profile.subscription_id, {
      trial_end: 'now',
      proration_behavior: 'create_prorations',
      expand: ['latest_invoice.payment_intent']
    });

    const paymentIntent = (updatedSub.latest_invoice && typeof updatedSub.latest_invoice === 'object' && 'payment_intent' in updatedSub.latest_invoice)
      ? (updatedSub.latest_invoice as { payment_intent?: Stripe.PaymentIntent }).payment_intent as Stripe.PaymentIntent | undefined
      : undefined;

    console.log('Payment Intent Status:', paymentIntent?.status);
    console.log('Updated Subscription Status:', updatedSub.status);

    // Handle different payment intent states
    let activationStatus = 'processing';
    let shouldUpdateProfile = false;

    if (paymentIntent) {
      switch (paymentIntent.status) {
        case 'succeeded':
          activationStatus = 'active';
          shouldUpdateProfile = true;
          // Record successful payment
          await prisma.payment.create({
            data: {
              profile_id: userId,
              stripe_payment_id: paymentIntent.id,
              amount: paymentIntent.amount_received / 100,
              currency: paymentIntent.currency,
              status: paymentIntent.status,
              plan_id: profile.plan_id,
              plan_name: profile.plan_name,
              payment_date: new Date(),
              created_at: new Date()
            }
          });
          break;
        
        case 'requires_action':
        case 'requires_confirmation':
          activationStatus = 'requires_action';
          break;
        
        case 'processing':
          activationStatus = 'processing';
          break;
        
        case 'requires_payment_method':
          return NextResponse.json({ 
            error: 'Payment method required. Please update your payment method and try again.' 
          }, { status: 400 });
        
        case 'canceled':
          return NextResponse.json({ 
            error: 'Payment failed. Please check your payment method and try again.' 
          }, { status: 400 });
        
        default:
          console.log('Unknown payment intent status:', paymentIntent.status);
          activationStatus = 'processing';
          break;
      }
    } else if (updatedSub.status === 'active') {
      // Sometimes subscription becomes active without payment intent
      activationStatus = 'active';
      shouldUpdateProfile = true;
    }

    // Update profile only if activation is successful or processing
    if (shouldUpdateProfile || activationStatus === 'processing') {
      const billingCycle = getBillingCycleFromPlanId(profile.plan_id || 'monthly-tier');
      // Type-safe extraction of current_period_start and current_period_end
      const currentPeriodStart = typeof (updatedSub as unknown) === 'object' && updatedSub && 'current_period_start' in updatedSub
        ? (updatedSub as { current_period_start: number }).current_period_start
        : undefined;
      const currentPeriodEnd = typeof (updatedSub as unknown) === 'object' && updatedSub && 'current_period_end' in updatedSub
        ? (updatedSub as { current_period_end: number }).current_period_end
        : undefined;
      const subscriptionWithPeriods: import('@/lib/stripe').StripeSubscriptionWithPeriods = {
        ...updatedSub,
        current_period_start: currentPeriodStart!,
        current_period_end: currentPeriodEnd!,
      };
      const { periodStart, periodEnd } = calculateBillingPeriod(subscriptionWithPeriods, profile.plan_id);

      await prisma.profile.update({
        where: { id: userId },
        data: {
          subscription_status: activationStatus === 'active' ? 'active' : 'processing',
          billing_cycle: billingCycle,
          trial_end: null,
          trial_used: true, // Mark trial as used
          subscription_start: new Date(periodStart * 1000),
          subscription_end: new Date(periodEnd * 1000),
          updated_at: new Date()
        }
      });
    }

    // Return appropriate response based on activation status
    switch (activationStatus) {
      case 'active':
        return NextResponse.json({ 
          success: true, 
          subscriptionStatus: 'active',
          message: 'Subscription activated successfully!'
        });
      
      case 'processing':
        return NextResponse.json({ 
          success: true, 
          subscriptionStatus: 'processing',
          message: 'Subscription is being processed. Please refresh in a moment.'
        });
      
      case 'requires_action':
        return NextResponse.json({ 
          success: false, 
          subscriptionStatus: 'requires_action',
          message: 'Additional action required. Please check your payment method.'
        });
      
      default:
        return NextResponse.json({ 
          success: false, 
          error: 'Activation status unclear. Please refresh and try again.'
        }, { status: 400 });
    }

  } catch (error) {
    console.error('Activation error:', error);
    return NextResponse.json({ 
      error: 'Failed to activate subscription', 
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
} 