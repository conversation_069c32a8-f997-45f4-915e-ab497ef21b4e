const { spawn } = require('child_process');
const path = require('path');

async function initializeCMS() {
  console.log('🚀 Initializing Little Spark CMS...');
  
  try {
    // First, let's create the admin user through the CMS
    console.log('📝 Creating admin user...');
    
    // We'll use the payload CLI to create the first user
    const createUser = spawn('npx', ['payload', 'create-first-user'], {
      cwd: __dirname,
      stdio: 'inherit',
      shell: true
    });
    
    createUser.on('close', (code) => {
      if (code === 0) {
        console.log('✅ Admin user created successfully!');
        console.log('🎯 You can now access the CMS at: http://localhost:3001/admin');
        console.log('📧 Default credentials:');
        console.log('   Email: <EMAIL>');
        console.log('   Password: admin123');
      } else {
        console.log('❌ Failed to create admin user');
      }
    });
    
  } catch (error) {
    console.error('❌ Error initializing CMS:', error);
  }
}

initializeCMS();
