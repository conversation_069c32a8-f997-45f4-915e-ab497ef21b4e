import React from "react";
import { BookO<PERSON>, Palette, Music, Calendar, Award } from "lucide-react";
import { format, isToday, isYesterday, isThisWeek, isThisMonth } from "date-fns";

interface UserContent {
    id: string;
    type: string;
    title: string;
    content_metadata: Record<string, unknown>;
    preview_url: string | null;
    challenge_id: string | null;
    created_at: string;
    updated_at: string;
    challenge?: {
        id: string;
        title: string;
        type: string;
        difficulty: string;
    };
}

interface CreativeGrowthTimelineProps {
    projects: UserContent[];
}

const CreativeGrowthTimeline: React.FC<CreativeGrowthTimelineProps> = ({
    projects
}) => {
    const getTypeIcon = (type: string) => {
        switch (type) {
            case "story":
                return <BookOpen className="h-4 w-4 text-blue-600" />;
            case "art":
                return <Palette className="h-4 w-4 text-green-600" />;
            case "music":
                return <Music className="h-4 w-4 text-purple-600" />;
            default:
                return <BookOpen className="h-4 w-4 text-gray-600" />;
        }
    };

    const getRelativeTime = (dateString: string) => {
        const date = new Date(dateString);
        
        if (isToday(date)) {
            return "Today";
        } else if (isYesterday(date)) {
            return "Yesterday";
        } else if (isThisWeek(date)) {
            return format(date, "EEEE"); // Day of week
        } else if (isThisMonth(date)) {
            return format(date, "MMM d"); // Month and day
        } else {
            return format(date, "MMM d, yyyy"); // Full date
        }
    };

    // Group projects by date for timeline
    const groupedProjects = projects.reduce((groups, project) => {
        const date = format(new Date(project.created_at), "yyyy-MM-dd");
        if (!groups[date]) {
            groups[date] = [];
        }
        groups[date].push(project);
        return groups;
    }, {} as Record<string, UserContent[]>);

    // Sort dates in descending order
    const sortedDates = Object.keys(groupedProjects).sort((a, b) => 
        new Date(b).getTime() - new Date(a).getTime()
    );

    if (projects.length === 0) {
        return (
            <div className="bg-white border border-gray-200 rounded-lg p-6">
                <div className="flex items-center gap-2 mb-4">
                    <Calendar className="h-5 w-5 text-gray-600" />
                    <h2 className="text-lg font-semibold text-gray-900">My Creative Growth Timeline</h2>
                </div>
                <div className="text-center py-8">
                    <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">Your creative timeline will appear here as you create projects</p>
                </div>
            </div>
        );
    }

    return (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="flex items-center gap-2 mb-6">
                <Calendar className="h-5 w-5 text-gray-600" />
                <h2 className="text-lg font-semibold text-gray-900">My Creative Growth Timeline</h2>
            </div>

            <div className="space-y-6">
                {sortedDates.slice(0, 10).map((date, dateIndex) => (
                    <div key={date} className="relative">
                        {/* Date Header */}
                        <div className="flex items-center gap-3 mb-4">
                            <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                            <h3 className="font-semibold text-gray-900">
                                {getRelativeTime(date)}
                            </h3>
                            <div className="flex-1 h-px bg-gray-200"></div>
                        </div>

                        {/* Projects for this date */}
                        <div className="ml-6 space-y-3">
                            {groupedProjects[date].map((project) => (
                                <div 
                                    key={project.id} 
                                    className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                                >
                                    <div className="w-8 h-8 bg-white rounded-lg flex items-center justify-center shadow-sm">
                                        {getTypeIcon(project.type)}
                                    </div>
                                    <div className="flex-1 min-w-0">
                                        <p className="font-medium text-gray-900 truncate">
                                            {project.title}
                                        </p>
                                        <div className="flex items-center gap-2 mt-1">
                                            <span className="text-xs text-gray-500">
                                                {project.type.charAt(0).toUpperCase() + project.type.slice(1)}
                                            </span>
                                            {project.challenge && (
                                                <span className="text-xs text-orange-600 flex items-center gap-1">
                                                    <Award className="h-3 w-3" />
                                                    Challenge
                                                </span>
                                            )}
                                        </div>
                                    </div>
                                    <div className="text-xs text-gray-400">
                                        {format(new Date(project.created_at), "h:mm a")}
                                    </div>
                                </div>
                            ))}
                        </div>

                        {/* Timeline connector */}
                        {dateIndex < sortedDates.length - 1 && (
                            <div className="absolute left-1.5 top-8 w-px h-6 bg-gray-200"></div>
                        )}
                    </div>
                ))}

                {sortedDates.length > 10 && (
                    <div className="text-center pt-4">
                        <p className="text-sm text-gray-500">
                            Showing recent 10 days • {projects.length} total projects
                        </p>
                    </div>
                )}
            </div>
        </div>
    );
};

export default CreativeGrowthTimeline;
