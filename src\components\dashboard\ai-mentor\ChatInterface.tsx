import React, { useEffect, useRef, useState } from 'react';
import { Button } from '@/components/ui/button';
import AiMentor from '@/components/AiMentor';
import { ChatInterfaceProps } from './types';
import VoiceEnabledInput from '@/components/shared/VoiceEnabledInput';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { isBlocked, getRemainingBlockMinutes } from '@/utils/ai/strikeTracker';

export const ChatInterface: React.FC<ChatInterfaceProps> = ({
  isChatMode,
  chatHistory,
  isProcessing,
  userMessage,
  setUserMessage,
  handleSendMessage,
  mentorNames,
  mentor<PERSON><PERSON>cter,
  mentorMessage,
  isInlineChat = false
}) => {
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [blockRemaining, setBlockRemaining] = useState(0);

  // Poll block status every second
  useEffect(() => {
    const interval = setInterval(() => {
      if (isBlocked()) {
        setBlockRemaining(getRemainingBlockMinutes() * 60);
      } else {
        setBlockRemaining(0);
      }
    }, 1000);
    return () => clearInterval(interval);
  }, []);

  const scrollToBottom = () => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  };

  useEffect(() => {
    // Set initialized flag after component mounts
    const timer = setTimeout(() => {
      setIsInitialized(true);
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    // Only scroll if initialized, in chat mode, and there are messages
    if (isInitialized && (isChatMode || isInlineChat) && (chatHistory.length > 0 || isProcessing)) {
      // Add a small delay to ensure DOM is updated
      setTimeout(() => {
        scrollToBottom();
      }, 50);
    }
  }, [chatHistory, isProcessing, isChatMode, isInlineChat, isInitialized]);

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      if (userMessage.trim()) {
        // const message = userMessage;
        setUserMessage('');
        handleSendMessage();
      }
    }
  };

  const handleButtonClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    if (userMessage.trim() && blockRemaining === 0) {
      setUserMessage('');
    }
    if (blockRemaining === 0) handleSendMessage();
  };

  if (!isChatMode && !isInlineChat) {
    return (
      <AiMentor 
        character={mentorCharacter} 
        message={mentorMessage}
        name={mentorNames[mentorCharacter]}
        showControls={false}
      />
    );
  }

  return (
    <>
              <div 
          ref={chatContainerRef}
          className="bg-white rounded-lg p-4 shadow-sm max-h-[200px] sm:max-h-[250px] md:max-h-[300px] lg:max-h-[350px] overflow-y-auto mb-4"
        >
        {chatHistory.length === 0 ? (
          <div className="text-center text-gray-400 my-6">
            Start chatting with {mentorNames[mentorCharacter]}!
          </div>
        ) : (
          <div className="space-y-4">
            {chatHistory.map((msg, idx) => (
              <div key={idx} className={`flex ${msg.role === 'user' ? 'justify-end' : 'justify-start'}`}>
                <div className={`max-w-[90%] sm:max-w-[85%] md:max-w-[80%] rounded-lg p-3 ${msg.role === 'user' ? 'bg-spark-blue/10 text-right' : 'bg-gray-100'}`}>
                  {msg.role === 'user' ? (
                    <div className="text-right">{msg.content}</div>
                  ) : (
                    <div className="prose prose-sm max-w-full prose-headings:mb-2 prose-headings:mt-1 prose-p:my-1 prose-ul:my-1 prose-ol:my-1 prose-li:my-0 prose-pre:my-1 prose-code:text-blue-600 prose-code:bg-blue-50 prose-code:px-1 prose-code:py-0.5 prose-code:rounded">
                      <ReactMarkdown remarkPlugins={[remarkGfm]}>
                        {msg.content}
                      </ReactMarkdown>
                    </div>
                  )}
                </div>
              </div>
            ))}
            {isProcessing && (
              <div className="flex justify-start">
                <div className="max-w-[90%] sm:max-w-[85%] md:max-w-[80%] rounded-lg p-3 bg-gray-100">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.4s' }}></div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
      <div className="flex flex-col sm:flex-row gap-2">
        <div className="flex-1">
          <VoiceEnabledInput 
            value={userMessage}
            onChange={(e) => setUserMessage(e.target.value)}
            onTextAdded={(text) => setUserMessage(userMessage + (userMessage ? ' ' : '') + text)}
            placeholder="Type your message..."
            onKeyDown={handleKeyDown}
            disabledTooltip={blockRemaining > 0 ? `Blocked: ${Math.ceil(blockRemaining/60)} min left` : undefined}
          />
        </div>
        <Button 
          onClick={handleButtonClick} 
          disabled={isProcessing || blockRemaining > 0}
          title={blockRemaining > 0 ? `Blocked: ${Math.ceil(blockRemaining/60)} min left` : undefined}
          className="w-full sm:w-auto px-6 py-2 h-10"
        >
          Send
        </Button>
      </div>
      {blockRemaining > 0 && (
        <div className="text-center text-red-600 text-sm mt-2">
          Content safety: You are blocked for {Math.ceil(blockRemaining / 60)} min {blockRemaining % 60}s.
        </div>
      )}
    </>
  );
};
