"use client";

import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { BookOpen, Palette, Music, FolderOpen, Info } from "lucide-react";
import { useProject } from "./ProjectContext";

const ProjectTypeSelector = () => {
    const { projectType, setProjectType, learningMode } = useProject();

    const projectTypes = [
        {
            id: "story" as const,
            label: "Story",
            icon: BookOpen,
            color: "bg-blue-100 text-blue-600 border-blue-200",
            activeColor: "bg-blue-500 text-white border-blue-500",
            description: "Creative writing and storytelling"
        },
        {
            id: "art" as const,
            label: "Art",
            icon: Palette,
            color: "bg-purple-100 text-purple-600 border-purple-200",
            activeColor: "bg-purple-500 text-white border-purple-500",
            description: "Visual art and design projects"
        },
        {
            id: "music" as const,
            label: "Music",
            icon: Music,
            color: "bg-green-100 text-green-600 border-green-200",
            activeColor: "bg-green-500 text-white border-green-500",
            description: "Musical compositions and audio"
        },
        {
            id: "general" as const,
            label: "General",
            icon: FolderOpen,
            color: "bg-orange-100 text-orange-600 border-orange-200",
            activeColor: "bg-orange-500 text-white border-orange-500",
            description: "Any other creative project"
        }
    ];

    return (
        <div className="mb-6 text-center flex flex-col items-center">
            <h3 className="text-lg font-medium mb-4">Project Type</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-4 w-full max-w-2xl">
                {projectTypes.map((type) => {
                    const Icon = type.icon;
                    const isActive = projectType === type.id;
                    
                    return (
                        <Button
                            key={type.id}
                            variant="outline"
                            onClick={() => setProjectType(type.id)}
                            className={`h-20 flex flex-col items-center justify-center gap-2 border-2 transition-all ${
                                isActive ? type.activeColor : type.color
                            }`}
                        >
                            <Icon className="h-5 w-5" />
                            <span className="text-sm font-medium">{type.label}</span>
                        </Button>
                    );
                })}
            </div>
            
            {learningMode && (
                <div className="flex items-center gap-2 bg-littlespark-light-blue-2 border border-littlespark-light-blue p-4 rounded-full">
                    <Info className="h-5 w-5 text-blue-500" />
                    <div className="">
                        <h4 className="font-semibold text-sm text-blue-600 font-fredoka">
                            Project Types
                        </h4>
                        <p className="text-sm text-black">
                            Choose the type that best matches your project. This helps organize your work and provides relevant tools.
                        </p>
                    </div>
                </div>
            )}
        </div>
    );
};

export default ProjectTypeSelector;
