import axios, { AxiosInstance, InternalAxiosRequestConfig, AxiosResponse } from 'axios';

// API client configuration
const API_CONFIG = {
  baseURL: process.env.NEXT_PUBLIC_SITE_URL || 'https://spark-new-virid.vercel.app',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
} as const;

// Create axios instance
const createApiClient = (): AxiosInstance => {
  const client = axios.create(API_CONFIG);

  // Request interceptor
  client.interceptors.request.use(
    (config: InternalAxiosRequestConfig) => {
      // Add any request modifications here (auth tokens, etc.)
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  // Response interceptor
  client.interceptors.response.use(
    (response: AxiosResponse) => {
      return response;
    },
    (error) => {
      // Handle common errors
      if (error.response?.status === 401) {
        // Will be handled by individual components using next/navigation
        console.warn('Unauthorized request detected');
      }
      
      if (error.response?.status >= 500) {
        console.error('Server error:', error.response.data);
      }
      
      return Promise.reject(error);
    }
  );

  return client;
};

// Export singleton instance
export const apiClient = createApiClient();

// Export types for better type safety
export type ApiResponse<T> = AxiosResponse<T>;
export type ApiError = {
  response?: {
    data?: {
      error?: string;
      message?: string;
    };
    status?: number;
  };
  message?: string;
};

// Helper function to handle API errors consistently
export const handleApiError = (error: unknown): string => {
  const apiError = error as ApiError;
  
  if (apiError.response?.data?.error) {
    return apiError.response.data.error;
  }
  
  if (apiError.response?.data?.message) {
    return apiError.response.data.message;
  }
  
  if (apiError.message) {
    return apiError.message;
  }
  
  return 'An unexpected error occurred';
}; 