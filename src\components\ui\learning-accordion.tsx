import React, { useState } from "react";

interface AccordionProps {
    children: React.ReactNode;
    type?: "single" | "multiple";
    collapsible?: boolean;
    className?: string;
}

interface AccordionItemProps {
    children: React.ReactNode;
    value: string;
    className?: string;
}

interface AccordionTriggerProps {
    children: React.ReactNode;
    className?: string;
}

interface AccordionContentProps {
    children: React.ReactNode;
    className?: string;
}

const AccordionContext = React.createContext<{
    openItems: string[];
    toggleItem: (value: string) => void;
    type: "single" | "multiple";
}>({
    openItems: [],
    toggleItem: () => {},
    type: "single",
});

const AccordionItemContext = React.createContext<{
    value: string;
    isOpen: boolean;
}>({
    value: "",
    isOpen: false,
});

export const Accordion: React.FC<AccordionProps> = ({
    children,
    type = "single",
    className = "",
}) => {
    const [openItems, setOpenItems] = useState<string[]>([]);

    const toggleItem = (value: string) => {
        if (type === "single") {
            setOpenItems((prev) => (prev.includes(value) ? [] : [value]));
        } else {
            setOpenItems((prev) =>
                prev.includes(value)
                    ? prev.filter((item) => item !== value)
                    : [...prev, value]
            );
        }
    };

    return (
        <AccordionContext.Provider value={{ openItems, toggleItem, type }}>
            <div className={`space-y-2 ${className}`}>{children}</div>
        </AccordionContext.Provider>
    );
};

export const AccordionItem: React.FC<AccordionItemProps> = ({
    children,
    value,
    className = "",
}) => {
    const { openItems } = React.useContext(AccordionContext);
    const isOpen = openItems.includes(value);

    return (
        <AccordionItemContext.Provider value={{ value, isOpen }}>
            <div className={`bg-white rounded-lg border ${className}`}>
                {children}
            </div>
        </AccordionItemContext.Provider>
    );
};

export const AccordionTrigger: React.FC<AccordionTriggerProps> = ({
    children,
    className = "",
}) => {
    const { toggleItem } = React.useContext(AccordionContext);
    const { value, isOpen } = React.useContext(AccordionItemContext);

    return (
        <button
            className={`w-full text-left transition-colors duration-200 
                 flex items-center justify-between ${className}`}
            onClick={() => toggleItem(value)}
            aria-expanded={isOpen}
        >
            {children}
        </button>
    );
};

export const AccordionContent: React.FC<AccordionContentProps> = ({
    children,
    className = "",
}) => {
    const { isOpen } = React.useContext(AccordionItemContext);

    return (
        <div
            className={`overflow-hidden transition-all duration-300 ease-in-out ${
                isOpen ? "max-h-96 opacity-100" : "max-h-0 opacity-0"
            }`}
        >
            <div className={className}>{children}</div>
        </div>
    );
};
