"use client";

import React, { useRef, useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import VoiceEnabledInput from '@/components/shared/VoiceEnabledInput';
import { ChatSectionProps, ChatMessage } from './types';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

export const ChatSection: React.FC<ChatSectionProps> = ({
  chatHistory = [],
  isProcessing = false,
  userMessage = '',
  setUserMessage,
  handleSendMessage,
  clearChat,
  mentorNames,
  mentorCharacter,
}) => {
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  const scrollToBottom = () => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  };

  useEffect(() => {
    // Set initialized flag after component mounts
    const timer = setTimeout(() => {
      setIsInitialized(true);
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    // Only scroll if initialized and there are messages
    if (isInitialized && (chatHistory.length > 0 || isProcessing)) {
      // Add a small delay to ensure DOM is updated
      setTimeout(() => {
        scrollToBottom();
      }, 50);
    }
  }, [chatHistory, isProcessing, isInitialized]);

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      if (userMessage.trim()) {
        // const message = userMessage;
        setUserMessage('');
        handleSendMessage();
      }
    }
  };

  const handleButtonClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    if (userMessage.trim()) {
      setUserMessage('');
    }
    handleSendMessage();
  };

  return (
    <div className="w-full">
      <div 
        ref={chatContainerRef}
        className="bg-white rounded-lg p-4 shadow-sm max-h-[200px] sm:max-h-[250px] md:max-h-[300px] lg:max-h-[350px] overflow-y-auto mb-4"
      >
        {(!chatHistory || chatHistory.length === 0) ? (
          <div className="text-center text-gray-400 my-6">
            Start chatting with {mentorNames?.[mentorCharacter] || 'your mentor'}!
          </div>
        ) : (
          <div className="space-y-4">
            {chatHistory.map((msg: ChatMessage, idx: number) => (
              <div key={idx} className={`flex ${msg.role === 'user' ? 'justify-end' : 'justify-start'}`}>
                <div className={`max-w-[90%] sm:max-w-[85%] md:max-w-[80%] rounded-lg p-3 ${msg.role === 'user' ? 'bg-spark-blue/10 text-right' : 'bg-gray-100'}`}>
                  {msg.role === 'user' ? (
                    <div className="text-right">{msg.content}</div>
                  ) : (
                    <div className="prose prose-sm max-w-full prose-headings:mb-2 prose-headings:mt-1 prose-p:my-1 prose-ul:my-1 prose-ol:my-1 prose-li:my-0 prose-pre:my-1 prose-code:text-blue-600 prose-code:bg-blue-50 prose-code:px-1 prose-code:py-0.5 prose-code:rounded">
                      <ReactMarkdown remarkPlugins={[remarkGfm]}>
                        {msg.content}
                      </ReactMarkdown>
                    </div>
                  )}
                </div>
              </div>
            ))}
            {isProcessing && (
              <div className="flex justify-start">
                <div className="max-w-[90%] sm:max-w-[85%] md:max-w-[80%] rounded-lg p-3 bg-gray-100">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.4s' }}></div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
      
      <div className="flex flex-col sm:flex-row gap-2">
        <div className="flex-1">
        <VoiceEnabledInput
          value={userMessage}
          onChange={(e) => setUserMessage(e.target.value)}
          placeholder="Type your message..."
          onTextAdded={(text) => setUserMessage(userMessage + (userMessage ? ' ' : '') + text)}
          onKeyDown={handleKeyDown}
        />
        </div>
        <div className="flex gap-2">
          {clearChat && (
            <Button
              onClick={clearChat}
              variant="outline"
              disabled={isProcessing}
              className="px-4 py-2 h-10 text-gray-600 hover:text-red-600"
              title="Clear chat history"
            >
              🗑️
            </Button>
          )}
          <Button
            onClick={handleButtonClick}
            disabled={isProcessing}
            className="w-full sm:w-auto px-6 py-2 h-10"
          >
            Send
          </Button>
        </div>
      </div>
      
      <div className="flex items-center gap-2 mt-4 p-3 bg-spark-green/10 rounded-lg">
        <div className="h-5 w-5 text-spark-green">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z" />
          </svg>
        </div>
        <span className="text-sm text-spark-green font-medium">Kid-friendly mode active - all conversations are safe and age appropriate</span>
      </div>
    </div>
  );
};
