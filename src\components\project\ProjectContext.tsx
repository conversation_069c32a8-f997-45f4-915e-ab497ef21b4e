"use client";
import React, { createContext, useContext, useState, ReactNode } from "react";

type ProjectType = "story" | "art" | "music" | "general";

interface ProjectContextType {
    title: string;
    setTitle: (title: string) => void;
    description: string;
    setDescription: (description: string) => void;
    content: string;
    setContent: (content: string) => void;
    projectType: ProjectType;
    setProjectType: (type: ProjectType) => void;
    isGenerating: boolean;
    setIsGenerating: (isGenerating: boolean) => void;
    learningMode: boolean;
    toggleLearningMode: () => void;
    helpDialogOpen: boolean;
    setHelpDialogOpen: (open: boolean) => void;
}

const ProjectContext = createContext<ProjectContextType | undefined>(undefined);

export const useProject = () => {
    const context = useContext(ProjectContext);
    if (!context) {
        throw new Error("useProject must be used within a ProjectProvider");
    }
    return context;
};

interface ProjectProviderProps {
    children: ReactNode;
}

export const ProjectProvider = ({ children }: ProjectProviderProps) => {
    const [title, setTitle] = useState("");
    const [description, setDescription] = useState("");
    const [content, setContent] = useState("");
    const [projectType, setProjectType] = useState<ProjectType>("general");
    const [isGenerating, setIsGenerating] = useState(false);
    const [learningMode, setLearningMode] = useState(true);
    const [helpDialogOpen, setHelpDialogOpen] = useState(false);

    const toggleLearningMode = () => {
        setLearningMode(!learningMode);
    };

    const value = {
        title,
        setTitle,
        description,
        setDescription,
        content,
        setContent,
        projectType,
        setProjectType,
        isGenerating,
        setIsGenerating,
        learningMode,
        toggleLearningMode,
        helpDialogOpen,
        setHelpDialogOpen,
    };

    return (
        <ProjectContext.Provider value={value}>{children}</ProjectContext.Provider>
    );
};
