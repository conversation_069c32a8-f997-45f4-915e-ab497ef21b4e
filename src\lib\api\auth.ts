import { apiClient, handleApiError } from './client';

// Types for auth API
export interface SignInRequest {
  email: string;
}

export interface SignUpRequest {
  email: string;
  fullName: string;
}

export interface AuthResponse {
  message: string;
  success: boolean;
}

export interface User {
  id: string;
  email: string;
  created_at: string;
  user_metadata: Record<string, unknown>;
}

export interface Profile {
  id: string;
  email: string;
  full_name: string | null;
  avatar_url: string | null;
  created_at: Date;
  updated_at: Date;
}

export interface AuthStatusResponse {
  authenticated: boolean;
  user?: User;
  profile?: Profile;
  error?: string;
}

// Authentication API service
export class AuthApiService {
  /**
   * Send magic link for user sign in
   */
  static async signIn(data: SignInRequest): Promise<AuthResponse> {
    try {
      const response = await apiClient.post<AuthResponse>('/api/auth/signin', data);
      return response.data;
    } catch (error) {
      const message = handleApiError(error);
      throw new Error(message);
    }
  }

  /**
   * Send magic link for user sign up
   */
  static async signUp(data: SignUpRequest): Promise<AuthResponse> {
    try {
      const response = await apiClient.post<AuthResponse>('/api/auth/signup', data);
      return response.data;
    } catch (error) {
      const message = handleApiError(error);
      throw new Error(message);
    }
  }

  /**
   * Sign out user and clear server-side cookies
   */
  static async signOut(): Promise<AuthResponse> {
    try {
      const response = await apiClient.post<AuthResponse>('/api/auth/signout');
      return response.data;
    } catch (error) {
      const message = handleApiError(error);
      throw new Error(message);
    }
  }

  /**
   * Get current authentication status
   */
  static async getStatus(): Promise<AuthStatusResponse> {
    try {
      const response = await apiClient.get<AuthStatusResponse>('/api/auth/status');
      return response.data;
    } catch (error) {
      const message = handleApiError(error);
      throw new Error(message);
    }
  }
} 