import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { stripe } from '@/lib/stripe';

// GET /api/subscription - Get user subscription status
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    const profile = await prisma.profile.findUnique({
      where: { id: userId },
      include: {
        payments: {
          orderBy: { created_at: 'desc' },
          take: 10
        }
      }
    });

    if (!profile) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Get latest Stripe subscription if exists
    let stripeSubscription = null;
    if (profile.stripe_customer_id && stripe) {
      try {
        const subscriptions = await stripe.subscriptions.list({
          customer: profile.stripe_customer_id,
          status: 'all',
          limit: 1
        });
        stripeSubscription = subscriptions.data[0] || null;
      } catch (error) {
        console.error('Error fetching Stripe subscription:', error);
      }
    }

    return NextResponse.json({
      subscription: {
        id: profile.subscription_id,
        status: profile.subscription_status,
        plan_id: profile.plan_id,
        plan_name: profile.plan_name,
        billing_cycle: profile.billing_cycle,
        subscription_start: profile.subscription_start,
        subscription_end: profile.subscription_end,
        trial_end: profile.trial_end,
        stripe_customer_id: profile.stripe_customer_id
      },
      payments: profile.payments,
      stripeSubscription
    });
  } catch (error) {
    console.error('Error fetching subscription:', error);
    return NextResponse.json(
      { error: 'Failed to fetch subscription' },
      { status: 500 }
    );
  }
} 