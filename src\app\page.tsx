"use client";

import React from "react";
import { useAuth } from "@/hooks/useAuth";
import Navbar from "@/components/common/Navbar";
import Footer from "@/components/common/Footer";
import SmoothScrolling from "@/components/common/SmoothScrolling";
import HeroSection from "@/components/Home/HeroSection";
import MarqueeSection from "@/components/Home/MarqueeSection";
import FeaturesSection from "@/components/Home/FeaturesSection";
import HowItWorksSection from "@/components/Home/HowItWorksSection";
import SafetySection from "@/components/Home/SafetySection";
import TestimonialsSection from "@/components/Home/TestimonialsSection";
import CTASection from "@/components/Home/CTASection";
import FAQSection from "@/components/Home/FAQSection";

export default function Home() {
    const { user, loading, signOut, isAuthenticated } = useAuth();

    return (
        <div className="min-h-screen bg-gray-50 relative">
            {/* Smooth Scrolling */}
            <SmoothScrolling />

            {/* Background blobs */}
            <div className="absolute overflow-hidden w-full h-full pointer-events-none">
                <div className="absolute -top-24 -left-24 w-96 h-96 bg-littlespark-lavender/30 rounded-full blur-3xl"></div>
                <div className="absolute top-1/3 -right-24 w-72 h-72 bg-littlespark-yellow/30 rounded-full blur-3xl"></div>
                <div className="absolute -bottom-24 left-1/4 w-80 h-80 bg-littlespark-blue/20 rounded-full blur-3xl"></div>
            </div>

            {/* Navbar */}
            <Navbar
                user={user}
                loading={loading}
                isAuthenticated={isAuthenticated}
                signOut={signOut}
            />

            {/* Spacer for fixed navbar */}
            <div className="h-20"></div>

            {isAuthenticated ? (
                // Authenticated user dashboard
                <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
                    <div className="px-4 py-6 sm:px-0">
                        <div className="text-center">
                            <h1 className="text-4xl font-bold text-gray-900 mb-4">
                                Welcome back!
                            </h1>
                            <p className="text-xl text-gray-600 mb-8">
                                You are successfully logged in to Little Spark
                            </p>
                            <div className="bg-white rounded-lg shadow p-6 max-w-md mx-auto">
                                <h2 className="text-lg font-semibold text-gray-900 mb-2">
                                    Account Information
                                </h2>
                                <p className="text-gray-600">
                                    <strong>Email:</strong> {user?.email}
                                </p>
                                <p className="text-gray-600">
                                    <strong>Name:</strong>{" "}
                                    {user?.user_metadata?.full_name ||
                                        "Not provided"}
                                </p>
                            </div>
                        </div>
                    </div>
                </main>
            ) : (
                // Landing page for non-authenticated users
                <>
                    <HeroSection />

                    <MarqueeSection />

                    <FeaturesSection />

                    <HowItWorksSection />

                    <SafetySection />

                    <TestimonialsSection />

                    <CTASection />

                    <FAQSection />

                    <Footer />
                </>
            )}
        </div>
    );
}
