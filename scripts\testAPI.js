// Test script to verify API endpoints are working
async function testAPI() {
  const baseUrl = 'http://localhost:3000';
  
  console.log('🔍 Testing API Endpoints...\n');
  
  try {
    // 1. Test challenges endpoint
    console.log('1. Testing GET /api/challenges');
    const challengesResponse = await fetch(`${baseUrl}/api/challenges`);
    const challengesData = await challengesResponse.json();
    
    if (challengesResponse.ok) {
      console.log(`   ✅ Success: Found ${challengesData.challenges?.length || 0} challenges`);
      if (challengesData.challenges?.length > 0) {
        console.log(`   📝 First challenge: "${challengesData.challenges[0].title}"`);
      }
    } else {
      console.log(`   ❌ Failed: ${challengesData.error || 'Unknown error'}`);
    }
    
    // 2. Test user-content endpoint (should require auth)
    console.log('\n2. Testing POST /api/user-content (without auth)');
    const contentResponse = await fetch(`${baseUrl}/api/user-content`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        type: 'art',
        title: 'Test Art',
        content_metadata: { test: true }
      })
    });
    const contentData = await contentResponse.json();
    
    if (contentResponse.status === 401) {
      console.log('   ✅ Correctly requires authentication');
    } else {
      console.log(`   ⚠️  Unexpected response: ${contentResponse.status}`);
    }
    
    // 3. Test challenge validation endpoint (should require auth)
    console.log('\n3. Testing POST /api/challenges/validate-completion (without auth)');
    const validationResponse = await fetch(`${baseUrl}/api/challenges/validate-completion`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        challengeId: '1',
        challengeStartedAt: new Date().toISOString()
      })
    });
    const validationData = await validationResponse.json();
    
    if (validationResponse.status === 401) {
      console.log('   ✅ Correctly requires authentication');
    } else {
      console.log(`   ⚠️  Unexpected response: ${validationResponse.status}`);
    }
    
    console.log('\n✅ API endpoints are responding correctly!');
    console.log('\n🧪 To test full functionality:');
    console.log('   1. Login to the application');
    console.log('   2. Go to dashboard to see challenges');
    console.log('   3. Start a challenge (art/story/music)');
    console.log('   4. Create content using the tools');
    console.log('   5. Try to mark challenge as complete');
    
  } catch (error) {
    console.error('❌ Error testing API:', error.message);
  }
}

// Run the test
testAPI();
