import React from "react";
import { LucideIcon } from "lucide-react";
import { fredoka, nunito } from "@/lib/fonts";
import { motion } from "framer-motion";

interface FeatureCardProps {
    title: string;
    description: string;
    icon: LucideIcon;
    iconColor: string;
    borderColor: string;
    delay: string;
}

const FeatureCard: React.FC<FeatureCardProps> = ({
    title,
    description,
    icon: Icon,
    iconColor,
    borderColor,
    delay,
}) => {
    // Extract the color name from the Tailwind class for the background
    const bgColorClass = iconColor.replace("text-", "bg-") + "/10";

    // Convert delay string to number for framer-motion
    const delayNumber = parseFloat(delay.replace("s", ""));

    return (
        <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: delayNumber }}
            viewport={{ once: true }}
            whileHover={{ scale: 1.02, y: -5 }}
        >
            <div
                className={`p-4 sm:p-6 bg-white rounded-xl shadow-lg transition-all duration-300 h-full border-t-4 border-l-1 border-r-1 border-b-1 ${borderColor} hover:shadow-xl`}
            >
                <div className={`${bgColorClass} p-3 rounded-full w-fit mb-4`}>
                    <Icon className={`h-5 w-5 sm:h-6 sm:w-6 ${iconColor}`} />
                </div>
                <h3
                    className={`text-xl sm:text-2xl text-gray-900 font-bold mb-2 ${fredoka.className}`}
                >
                    {title}
                </h3>
                <p className={`text-sm sm:text-base text-gray-600 ${nunito.className}`}>
                    {description}
                </p>
            </div>
        </motion.div>
    );
};

export default FeatureCard;
