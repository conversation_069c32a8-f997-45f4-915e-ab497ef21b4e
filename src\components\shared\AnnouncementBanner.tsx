import React, { useState } from 'react';
import { X } from 'lucide-react';
import { fredoka, nunito } from '@/lib/fonts';

interface AnnouncementBannerProps {
  title?: string;
  description?: string;
  variant?: 'default' | 'destructive' | 'sunshine' | 'blueIris' | 'nasturtium' | 'mint' | 'lilac';
  allowDismiss?: boolean;
  className?: string;
}

const AnnouncementBanner = ({
  title = "🌟 Founding Beta Special!",
  description = "Join Little Spark by July 31st and get 50% off for life with code LSBETA50",
  allowDismiss = true,
  className = "",
}: AnnouncementBannerProps) => {
  const [isVisible, setIsVisible] = useState(true);
  
  if (!isVisible) return null;
  
  return (
    <div className={`relative bg-purple-50 border border-purple-200 text-gray-900 rounded-full py-4 px-6 mb-6 mx-4 ${className}`}>
      {allowDismiss && (
        <button 
          onClick={() => setIsVisible(false)} 
          className="absolute right-4 top-1/2 transform -translate-y-1/2 text-current p-1 rounded-full hover:bg-purple-200/50 transition-colors"
          aria-label="Dismiss"
        >
          <X className="h-4 w-4" />
        </button>
      )}
      
      <div className="text-center pr-8">
        {title && (
          <div className={`${fredoka.className} font-[900] text-md mb-1`}>
            {title}
          </div>
        )}
        {description && (
          <div className={`${nunito.className} font-[400] text-sm`}>
            {description}
          </div>
        )}
      </div>
    </div>
  );
};

export default AnnouncementBanner; 