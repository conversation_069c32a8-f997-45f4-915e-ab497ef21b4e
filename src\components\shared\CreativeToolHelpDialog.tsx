import React from "react";
import {
    <PERSON><PERSON>,
    <PERSON><PERSON>Content,
    <PERSON><PERSON><PERSON>eader,
    DialogTitle,
    DialogDescription,
} from "@/components/ui/dialog";
import { X } from "lucide-react";
import AiMentor from "@/components/AiMentor";

interface CreativeToolHelpDialogProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    title: string;
    description: string;
    steps: { title: string; content: string }[];
    aiMentorMessage?: string;
}

const CreativeToolHelpDialog = ({
    open,
    onOpenChange,
    title,
    description,
    steps,
    aiMentorMessage = "I'm here to help you get started with this creative tool!",
}: CreativeToolHelpDialogProps) => {
    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="bg-white p-4 sm:p-6 rounded-2xl max-w-xs sm:max-w-md lg:max-w-4xl xl:max-w-6xl max-h-[95vh] overflow-y-auto mx-4">
                {/* Close Button */}
                <button
                    onClick={() => onOpenChange(false)}
                    className="absolute top-4 right-4 p-2 rounded-full hover:bg-gray-100 transition-colors"
                    aria-label="Close dialog"
                >
                    <X className="h-5 w-5 text-gray-500" />
                </button>

                <DialogHeader>
                    <DialogTitle className="text-2xl font-bold text-center mb-1">
                        {title} Help
                    </DialogTitle>
                    <DialogDescription className="text-center text-muted-foreground">
                        {description}
                    </DialogDescription>
                </DialogHeader>

                <div className="flex justify-center mb-6">
                    <AiMentor
                        character="explorer"
                        message={aiMentorMessage}
                        showControls={false}
                    />
                </div>

                <div className="space-y-4">
                    {steps.map((step, index) => (
                        <div key={index} className="bg-gray-50 p-4 rounded-lg">
                            <h3 className="font-bold mb-2">{step.title}</h3>
                            <p className="text-gray-700">{step.content}</p>
                        </div>
                    ))}
                </div>
            </DialogContent>
        </Dialog>
    );
};

export default CreativeToolHelpDialog;
