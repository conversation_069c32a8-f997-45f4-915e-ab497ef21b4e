
/**
 * Draws a rocket shape on the canvas
 */
export const drawRocketShape = (ctx: CanvasRenderingContext2D, x: number, y: number, size: number) => {
  // Rocket body
  ctx.moveTo(x - size*0.2, y + size*0.5);
  ctx.lineTo(x - size*0.2, y - size*0.3);
  ctx.quadraticCurveTo(
    x, y - size*0.8,
    x + size*0.2, y - size*0.3
  );
  ctx.lineTo(x + size*0.2, y + size*0.5);
  ctx.closePath();
  
  // Fins
  ctx.moveTo(x - size*0.2, y + size*0.3);
  ctx.lineTo(x - size*0.5, y + size*0.5);
  ctx.lineTo(x - size*0.2, y + size*0.5);
  ctx.closePath();
  
  ctx.moveTo(x + size*0.2, y + size*0.3);
  ctx.lineTo(x + size*0.5, y + size*0.5);
  ctx.lineTo(x + size*0.2, y + size*0.5);
  ctx.closePath();
  
  // Window
  ctx.moveTo(x + size*0.1, y);
  ctx.arc(x, y, size*0.1, 0, 2 * Math.PI);
  
  // Flame
  ctx.moveTo(x - size*0.1, y + size*0.5);
  ctx.quadraticCurveTo(
    x, y + size*0.9,
    x + size*0.1, y + size*0.5
  );
};
