"use client";
import { useState } from 'react';
import { toast } from 'sonner';
import { generateArtIdea, improveArtPrompt } from '@/utils/ai/artService';
import { ArtStyleType } from './useArtStyle';

export const useArtPrompt = (currentStyle: ArtStyleType) => {
  const [prompt, setPrompt] = useState('');
  const [title, setTitle] = useState('');
  const [isGeneratingIdea, setIsGeneratingIdea] = useState(false);
  const [isImprovingPrompt, setIsImprovingPrompt] = useState(false);

  const handleGenerateIdea = async () => {
    setIsGeneratingIdea(true);
    try {
      const idea = await generateArtIdea(currentStyle);
      setPrompt(idea);
      toast.success("New art idea generated!");
    } catch (error) {
      console.error("Error generating art idea:", error);
      toast.error("Failed to generate idea. Please try again.");
    } finally {
      setIsGeneratingIdea(false);
    }
  };

  const handleImprovePrompt = async () => {
    if (!prompt.trim()) {
      toast.error("Please enter a description first");
      return;
    }

    setIsImprovingPrompt(true);
    try {
      const improvedPrompt = await improveArtPrompt(prompt, currentStyle);
      setPrompt(improvedPrompt);
      toast.success("Prompt improved!");
    } catch (error) {
      console.error("Error improving prompt:", error);
      toast.error("Failed to improve prompt. Please try again.");
    } finally {
      setIsImprovingPrompt(false);
    }
  };

  return {
    prompt,
    setPrompt,
    title,
    setTitle,
    isGeneratingIdea,
    isImprovingPrompt,
    handleGenerateIdea,
    handleImprovePrompt
  };
};
