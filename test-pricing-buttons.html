<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Pricing Buttons</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .plan {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 10px 0;
            text-align: center;
        }
        .button {
            background: black;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
        }
        .button:hover {
            background: #333;
        }
        .test-result {
            margin: 20px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>Pricing Button Test</h1>
    <p>Testing the updated pricing page button functionality</p>

    <div class="plan">
        <h3>Monthly Plan</h3>
        <p>$14.99/month</p>
        <a href="/checkout?plan=monthly-tier" class="button">Purchase Subscription</a>
    </div>

    <div class="plan">
        <h3>Quarterly Plan</h3>
        <p>$35.97/quarter</p>
        <a href="/checkout?plan=quarterly-tier" class="button">Purchase Subscription</a>
    </div>

    <div class="plan">
        <h3>Annual Plan</h3>
        <p>$119.99/year</p>
        <a href="/checkout?plan=annual-tier" class="button">Purchase Subscription</a>
    </div>

    <div class="test-result success">
        <h3>✅ Fixed Issues:</h3>
        <ul>
            <li>Removed broken ThriveCart URLs</li>
            <li>Updated all checkout URLs to use internal /checkout routes</li>
            <li>Removed gift subscription sections</li>
            <li>Fixed button functionality for trial_used users</li>
        </ul>
    </div>

    <div class="test-result success">
        <h3>✅ Expected Behavior:</h3>
        <ul>
            <li>Clicking "Purchase Subscription" should redirect to /checkout?plan=PLAN_ID</li>
            <li>Users with trial_used=true see "Purchase Subscription" text</li>
            <li>Users with trial_used=false see normal plan button text</li>
            <li>All purchases go through internal Stripe payment flow</li>
        </ul>
    </div>

    <script>
        // Test the button URLs
        document.addEventListener('DOMContentLoaded', function() {
            const buttons = document.querySelectorAll('.button');
            buttons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const url = this.getAttribute('href');
                    console.log('Button clicked, would redirect to:', url);
                    
                    // Validate URL format
                    if (url.startsWith('/checkout?plan=') && url.includes('-tier')) {
                        alert('✅ Success: Valid checkout URL - ' + url);
                    } else {
                        alert('❌ Error: Invalid URL format - ' + url);
                    }
                });
            });
        });
    </script>
</body>
</html>
