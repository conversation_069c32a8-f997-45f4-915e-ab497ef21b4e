
/**
 * Draws a dragon shape on the canvas
 */
export const drawDragonShape = (ctx: CanvasRenderingContext2D, x: number, y: number, size: number) => {
  // Save current context state
  ctx.save();
  
  // Set line properties for better coloring page quality
  ctx.lineWidth = size * 0.02;
  ctx.lineCap = 'round';
  ctx.lineJoin = 'round';
  ctx.strokeStyle = '#000';
  
  // Start a new path for the dragon
  ctx.beginPath();
  
  // Head and snout
  ctx.moveTo(x + size * 0.6, y - size * 0.1);
  ctx.quadraticCurveTo(
    x + size * 0.7, y - size * 0.15,
    x + size * 0.75, y - size * 0.1
  );
  ctx.quadraticCurveTo(
    x + size * 0.8, y - size * 0.05,
    x + size * 0.7, y + size * 0.05
  );
  
  // Neck
  ctx.quadraticCurveTo(
    x + size * 0.6, y + size * 0.1,
    x + size * 0.5, y + size * 0.05
  );
  
  // Body
  ctx.quadraticCurveTo(
    x + size * 0.3, y,
    x + size * 0.2, y + size * 0.1
  );
  ctx.quadraticCurveTo(
    x, y + size * 0.2,
    x - size * 0.3, y + size * 0.1
  );
  
  // Tail
  ctx.quadraticCurveTo(
    x - size * 0.5, y,
    x - size * 0.7, y - size * 0.2
  );
  
  ctx.stroke();
  
  // Spikes along back
  ctx.beginPath();
  for (let i = 0; i < 6; i++) {
    const spikeX = x + size * 0.4 - i * size * 0.15;
    const baseY = i < 3 ? y + size * 0.03 : y + size * 0.08;
    
    ctx.moveTo(spikeX, baseY);
    ctx.lineTo(spikeX, baseY - size * 0.15);
    ctx.lineTo(spikeX - size * 0.05, baseY);
  }
  ctx.stroke();
  
  // Wings
  ctx.beginPath();
  ctx.moveTo(x + size * 0.3, y + size * 0.05);
  // Wing membrane
  ctx.quadraticCurveTo(
    x + size * 0.2, y - size * 0.3,
    x, y - size * 0.2
  );
  // Wing fold
  ctx.lineTo(x + size * 0.1, y - size * 0.1);
  ctx.lineTo(x + size * 0.2, y);
  ctx.lineTo(x + size * 0.3, y + size * 0.05);
  ctx.stroke();
  
  // Eye
  ctx.beginPath();
  ctx.arc(x + size * 0.68, y - size * 0.08, size * 0.02, 0, 2 * Math.PI);
  ctx.fill();
  
  // Nostrils
  ctx.beginPath();
  ctx.arc(x + size * 0.73, y - size * 0.05, size * 0.01, 0, 2 * Math.PI);
  ctx.fill();
  
  // Legs
  ctx.beginPath();
  // Front leg
  ctx.moveTo(x + size * 0.3, y + size * 0.1);
  ctx.lineTo(x + size * 0.25, y + size * 0.25);
  ctx.lineTo(x + size * 0.2, y + size * 0.25);
  
  // Back leg
  ctx.moveTo(x, y + size * 0.15);
  ctx.lineTo(x - size * 0.1, y + size * 0.3);
  ctx.lineTo(x - size * 0.15, y + size * 0.3);
  
  ctx.stroke();
  
  // Restore the context state
  ctx.restore();
};
