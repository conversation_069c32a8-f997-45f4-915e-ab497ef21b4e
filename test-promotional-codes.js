// Test script for promotional codes
const testPromotionalCode = async () => {
  try {
    console.log('🧪 Testing promotional code validation...');
    
    const response = await fetch('http://localhost:3000/api/promotional-codes/validate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        code: 'ALPHA100',
        planId: 'monthly-tier'
      }),
    });
    
    const data = await response.json();
    console.log('📋 Response:', JSON.stringify(data, null, 2));
    
    if (data.valid) {
      console.log('✅ ALPHA100 promotional code is working!');
      console.log(`💰 Original Amount: ₹${(data.original_amount / 100).toFixed(2)}`);
      console.log(`🎉 Discount: ₹${(data.discount_amount / 100).toFixed(2)}`);
      console.log(`💳 Final Amount: ₹${(data.final_amount / 100).toFixed(2)}`);
    } else {
      console.log('❌ Promotional code validation failed');
    }
    
  } catch (error) {
    console.error('❌ Error testing promotional code:', error);
  }
};

// Test invalid code
const testInvalidCode = async () => {
  try {
    console.log('\n🧪 Testing invalid promotional code...');
    
    const response = await fetch('http://localhost:3000/api/promotional-codes/validate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        code: 'INVALID123',
        planId: 'monthly-tier'
      }),
    });
    
    const data = await response.json();
    console.log('📋 Response:', JSON.stringify(data, null, 2));
    
    if (!data.valid) {
      console.log('✅ Invalid code correctly rejected');
    } else {
      console.log('❌ Invalid code was accepted (this is wrong)');
    }
    
  } catch (error) {
    console.error('❌ Error testing invalid code:', error);
  }
};

// Run tests
const runTests = async () => {
  console.log('🚀 Starting promotional code tests...\n');
  await testPromotionalCode();
  await testInvalidCode();
  console.log('\n🎯 Tests completed!');
};

// Check if running in Node.js environment
if (typeof window === 'undefined') {
  // Node.js environment
  const fetch = require('node-fetch');
  runTests();
} else {
  // Browser environment
  console.log('Run this in browser console:');
  console.log('testPromotionalCode()');
  window.testPromotionalCode = testPromotionalCode;
  window.testInvalidCode = testInvalidCode;
  window.runTests = runTests;
}
