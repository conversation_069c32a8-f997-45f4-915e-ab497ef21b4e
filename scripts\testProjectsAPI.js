/**
 * Test script for Projects API endpoints
 * Run with: node scripts/testProjectsAPI.js
 */

const BASE_URL = 'http://localhost:3000';

// Test data
const testEndpoints = [
  {
    name: 'Get User Content',
    method: 'GET',
    url: '/api/user-content',
    description: 'Fetch all user content'
  },
  {
    name: 'Get User Content (Stories Only)',
    method: 'GET',
    url: '/api/user-content?type=story',
    description: 'Fetch only story content'
  },
  {
    name: 'Get User Content Stats',
    method: 'GET',
    url: '/api/user-content/stats',
    description: 'Get content statistics'
  },
  {
    name: 'Get Challenges',
    method: 'GET',
    url: '/api/challenges',
    description: 'Get available challenges'
  }
];

async function testEndpoint(endpoint) {
  console.log(`\n🧪 Testing: ${endpoint.name}`);
  console.log(`📝 Description: ${endpoint.description}`);
  console.log(`🔗 URL: ${endpoint.method} ${endpoint.url}`);
  
  try {
    const response = await fetch(`${BASE_URL}${endpoint.url}`, {
      method: endpoint.method,
      headers: {
        'Content-Type': 'application/json',
      },
      // Note: In a real test, you'd need to include authentication cookies
    });

    console.log(`📊 Status: ${response.status} ${response.statusText}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log(`✅ Success: ${JSON.stringify(data, null, 2).substring(0, 200)}...`);
    } else {
      const errorText = await response.text();
      console.log(`❌ Error: ${errorText}`);
    }
  } catch (error) {
    console.log(`💥 Network Error: ${error.message}`);
  }
}

async function runTests() {
  console.log('🚀 Starting Projects API Tests');
  console.log('=' .repeat(50));
  
  // Check if server is running
  try {
    const healthCheck = await fetch(`${BASE_URL}/api/challenges`);
    if (!healthCheck.ok) {
      console.log('❌ Server not responding. Make sure the development server is running:');
      console.log('   npm run dev');
      return;
    }
  } catch (error) {
    console.log('❌ Cannot connect to server. Make sure the development server is running:');
    console.log('   npm run dev');
    return;
  }

  console.log('✅ Server is running');

  // Run all tests
  for (const endpoint of testEndpoints) {
    await testEndpoint(endpoint);
    await new Promise(resolve => setTimeout(resolve, 500)); // Small delay between tests
  }

  console.log('\n' + '=' .repeat(50));
  console.log('🏁 Tests completed');
  console.log('\n📋 Manual Testing Checklist:');
  console.log('1. ✅ Navigate to http://localhost:3000/dashboard');
  console.log('2. ✅ Click "My Projects" button');
  console.log('3. ✅ Verify projects page loads');
  console.log('4. ✅ Test filtering by content type');
  console.log('5. ✅ Click "View Project" on any project');
  console.log('6. ✅ Test project viewer modal');
  console.log('7. ✅ Test sharing functionality');
  console.log('8. ✅ Verify growth tracker displays stats');
  console.log('9. ✅ Check timeline shows chronological order');
  console.log('\n💡 Tips:');
  console.log('- Create some content first using the creative tools');
  console.log('- Use the seed script to add sample data:');
  console.log('  npx ts-node scripts/seedUserContent.ts <user-id>');
  console.log('- Check browser console for any JavaScript errors');
  console.log('- Verify API responses in Network tab');
}

// Run the tests
runTests().catch(console.error);
