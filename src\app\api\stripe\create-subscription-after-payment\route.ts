import { NextRequest, NextResponse } from 'next/server';
import { stripe, STRIPE_PLANS, calculateBillingPeriod, getBillingCycleFromPlanId, getPlanDisplayName, type StripeSubscriptionWithPeriods } from '@/lib/stripe';
import { prisma } from '@/lib/prisma';

export async function POST(request: NextRequest) {
  try {
    if (!stripe) {
      return NextResponse.json({ error: 'Stripe not configured' }, { status: 500 });
    }

    const { paymentIntentId, planId, userId } = await request.json();

    if (!paymentIntentId || !planId || !userId) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    const plan = STRIPE_PLANS[planId as keyof typeof STRIPE_PLANS];
    if (!plan) {
      return NextResponse.json({ error: 'Invalid plan ID' }, { status: 400 });
    }

    // Retrieve the payment intent to get customer info
    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);
    
    if (paymentIntent.status !== 'succeeded') {
      return NextResponse.json({ error: 'Payment not successful' }, { status: 400 });
    }

    // Find profile
    const profile = await prisma.profile.findUnique({
      where: { id: userId }
    });

    if (!profile) {
      return NextResponse.json({ error: 'Profile not found' }, { status: 404 });
    }

    // Create subscription without trial
    const subscription = await stripe.subscriptions.create({
      customer: paymentIntent.customer as string,
      items: [{ price: plan.priceId }],
      payment_behavior: 'default_incomplete',
      payment_settings: { 
        save_default_payment_method: 'on_subscription',
        payment_method_types: ['card']
      },
      default_payment_method: paymentIntent.payment_method as string,
      collection_method: 'charge_automatically',
      metadata: {
        planId: planId,
        email: profile.email,
        userId: profile.id,
        direct_subscription: 'true',
        payment_intent_id: paymentIntentId
      }
    }) as unknown as StripeSubscriptionWithPeriods;

    console.log('Direct subscription created successfully:', subscription.id);
    console.log('Subscription status:', subscription.status);

    // Record payment
    await prisma.payment.create({
      data: {
        profile_id: profile.id,
        stripe_payment_id: paymentIntentId,
        amount: paymentIntent.amount_received / 100,
        currency: paymentIntent.currency,
        status: 'succeeded',
        plan_id: planId,
        plan_name: plan.priceId, // You might want to add a displayName to STRIPE_PLANS
        payment_date: new Date(),
        created_at: new Date()
      }
    });

    // Calculate correct billing period
    const { periodStart, periodEnd } = calculateBillingPeriod(subscription, planId);
    const billingCycle = getBillingCycleFromPlanId(planId);

    // Immediately update profile subscription details so UI reflects active status
    await prisma.profile.update({
      where: { id: profile.id },
      data: {
        subscription_id: subscription.id,
        subscription_status: 'active',
        plan_id: planId,
        plan_name: getPlanDisplayName(planId),
        billing_cycle: billingCycle,
        subscription_start: new Date(periodStart * 1000),
        subscription_end: new Date(periodEnd * 1000),
        trial_end: subscription.trial_end ? new Date(subscription.trial_end * 1000) : null,
        updated_at: new Date()
      }
    });

    // Mark trial as used for this user
    await prisma.profile.update({
      where: { id: profile.id },
      data: {
        trial_used: true,
        updated_at: new Date()
      }
    });

    return NextResponse.json({
      subscriptionId: subscription.id,
      status: subscription.status,
      success: true
    });

  } catch (error) {
    console.error('Error creating subscription after payment:', error);
    return NextResponse.json(
      { error: 'Failed to create subscription' },
      { status: 500 }
    );
  }
} 