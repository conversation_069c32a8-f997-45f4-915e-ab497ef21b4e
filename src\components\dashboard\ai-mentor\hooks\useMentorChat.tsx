import { useState, useCallback, useEffect, useRef } from "react";
import { useAuth } from "@/hooks/useAuth";
import { toast } from "sonner";
import { ChatState } from "./types/chatTypes";
import { loadChatHistory, saveChatHistory, clearChatHistory } from "./utils/chatStorage";
import { sendMessageToAI, createChatMessage } from "./utils/chatService";
import { Mentor<PERSON>haracter } from "../types";

interface UseMentorChatProps {
  character: <PERSON>tor<PERSON><PERSON><PERSON>;
  characterName: string;
}

export const useMentorChat = (props?: UseMentorChatProps) => {
    const { user } = useAuth();
    const [state, setState] = useState<ChatState>({
        messages: [],
        isLoading: false,
        error: null,
    });

    // Keep track of current user to detect user changes
    const currentUserRef = useRef<string | undefined>(user?.id);

    // Track if this is a fresh page load
    const isInitialLoad = useRef(true);

    // Clear chat history for current user
    const clearChat = useCallback(async () => {
        try {
            // Don't show loading indicator when clearing chat for character changes
            await clearChatHistory(user?.id);
            const messages = await loadChatHistory(user?.id);
            setState((prev) => ({ ...prev, messages, isLoading: false }));
            toast.success("Chat history cleared!");
        } catch (err) {
            console.error("Error clearing chat history", err);
            setState((prev) => ({
                ...prev,
                error: err instanceof Error ? err : new Error("Failed to clear chat history"),
                isLoading: false,
            }));
        }
    }, [user?.id]);

    // Load chat history - always start fresh on page load
    const loadHistory = useCallback(async () => {
        try {
            setState((prev) => ({ ...prev, isLoading: true }));

            // Check if this is a fresh page load using sessionStorage
            const sessionKey = `chatSession_${user?.id || 'anonymous'}`;
            const hasExistingSession = sessionStorage.getItem(sessionKey);

            // Always clear chat history on fresh page load or user change
            if (!hasExistingSession || currentUserRef.current !== user?.id) {
                await clearChatHistory(user?.id);
                console.log('Chat history cleared for fresh start - page refresh or user change');

                // Mark this session as active
                sessionStorage.setItem(sessionKey, 'active');
            }

            // Load fresh default messages
            const messages = await loadChatHistory(user?.id);
            setState((prev) => ({ ...prev, messages, isLoading: false }));

            // Mark that initial load is complete
            isInitialLoad.current = false;
        } catch (err) {
            console.error("Error in loadHistory", err);
            setState((prev) => ({
                ...prev,
                error:
                    err instanceof Error
                        ? err
                        : new Error("Failed to load chat history"),
                isLoading: false,
            }));
        }
    }, [user?.id]);

    // Send message to AI mentor
    const sendMessage = useCallback(
        async (content: string) => {
            if (!content.trim()) return;

            try {
                setState((prev) => ({ ...prev, isLoading: true, error: null }));

                // Add user message
                const userMessage = createChatMessage("user", content);
                const updatedMessages = [...state.messages, userMessage];

                setState((prev) => ({ ...prev, messages: updatedMessages }));
                
                // Save to storage (with error handling)
                try {
                    await saveChatHistory(updatedMessages, user?.id);
                } catch (saveError) {
                    console.error("Error saving chat history:", saveError);
                    // Continue with the chat even if saving fails
                }

                // Prepare context for the AI
                const recentMessages = updatedMessages
                    .slice(-10)
                    .map((msg) => ({
                        role: msg.role,
                        content: msg.content,
                    }));

                // Get AI response
                const aiResponse = await sendMessageToAI(
                    content,
                    recentMessages,
                    props?.character || 'robot',
                    props?.characterName || 'Sparky'
                );

                // Add AI response
                const assistantMessage = createChatMessage(
                    "assistant",
                    aiResponse
                );
                const finalMessages = [...updatedMessages, assistantMessage];

                setState((prev) => ({
                    ...prev,
                    messages: finalMessages,
                    isLoading: false,
                }));
                
                // Save final messages (with error handling)
                try {
                    await saveChatHistory(finalMessages, user?.id);
                } catch (saveError) {
                    console.error("Error saving final chat history:", saveError);
                    // Continue with the chat even if saving fails
                }
            } catch (err) {
                console.error("Error in chat flow", err);
                setState((prev) => ({
                    ...prev,
                    error:
                        err instanceof Error
                            ? err
                            : new Error("Failed to process message"),
                    isLoading: false,
                }));
                toast.error(err instanceof Error ? err.message : "Unable to process your message right now.");
            }
        },
        [state.messages, user, props?.character, props?.characterName]
    );

    // Note: clearChat function is defined above (line 26)

    // Load chat history on component mount
    useEffect(() => {
        loadHistory();
    }, [loadHistory]);

    // Handle user changes and page refresh
    useEffect(() => {
        // Check if user has changed
        if (currentUserRef.current !== user?.id) {
            console.log('User changed from', currentUserRef.current, 'to', user?.id);
            currentUserRef.current = user?.id;

            // Load fresh chat history for the new user
            loadHistory();
        }
    }, [user?.id, loadHistory]);

    // Clear chat history on page refresh/component unmount for better UX
    useEffect(() => {
        const handleBeforeUnload = () => {
            // Clear chat on page refresh for privacy and fresh experience
            try {
                const storageKey = user?.id ? `mentorChatHistory_${user.id}` : 'mentorChatHistory_anonymous';
                const sessionKey = `chatSession_${user?.id || 'anonymous'}`;

                localStorage.removeItem(storageKey);
                sessionStorage.removeItem(sessionKey);

                console.log('Chat history and session cleared on page refresh for user:', user?.id || 'anonymous');
            } catch (error) {
                console.warn('Error clearing chat on page refresh:', error);
            }
        };

        window.addEventListener('beforeunload', handleBeforeUnload);

        return () => {
            window.removeEventListener('beforeunload', handleBeforeUnload);
        };
    }, [user?.id]);

    return {
        messages: state.messages,
        isLoading: state.isLoading,
        error: state.error,
        sendMessage,
        clearChat,
    };
};
