"use client";
import React from "react";
import Image from "next/image";

interface Page {
  name: string;
  src: string;
}

interface PremadeColoringDropdownProps {
  open: boolean;
  onClose: () => void;
  pages: Page[];
  onSelect: (src: string) => void;
}

const PremadeColoringDropdown: React.FC<PremadeColoringDropdownProps> = ({ open, onClose, pages, onSelect }) => {
  if (!open) return null;
  return (
    <div className="w-full max-w-2xl bg-white rounded-2xl shadow-lg mt-4 p-6 z-50 border border-spark-teal">
      <div className="flex items-center gap-2 mb-4">
        <span className="text-2xl">🎨</span>
        <span className="font-semibold text-gray-700 text-lg">Choose a Coloring Page</span>
        <button type="button" className="ml-auto text-gray-400 hover:text-spark-teal" onClick={onClose}>&times;</button>
      </div>
      <div className="flex gap-6 justify-center">
        {pages.map((page) => (
          <div key={page.name} className="flex flex-col items-center cursor-pointer group" onClick={() => onSelect(page.src)}>
            <Image src={page.src} alt={page.name} width={128} height={128} className="w-32 h-32 object-contain rounded-xl border-2 border-gray-200 group-hover:border-spark-teal transition" />
            <span className="mt-2 text-sm font-medium text-gray-700">{page.name}</span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default PremadeColoringDropdown; 