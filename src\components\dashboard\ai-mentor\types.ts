
export type MentorCharacter = 'robot' | 'owl' | 'explorer';

export interface MentorNames {
  [key: string]: string;
  robot: string;
  owl: string;
  explorer: string;
}

export type ChatRole = 'ai' | 'user' | 'assistant';

export interface ChatMessage {
  id?: string;
  role: ChatRole;
  content: string;
  timestamp?: string;
}

export interface CharacterSelectorProps {
  mentorCharacter: MentorCharacter;
  onChange: (character: <PERSON><PERSON><PERSON><PERSON><PERSON>) => void;
}

export interface MentorNameEditorProps {
  isEditing: boolean;
  mentorName: string;
  setMentorName: (name: string) => void;
  onSave: () => void;
  setIsEditing: (isEditing: boolean) => void;
  mentorCharacter: MentorCharacter;
  mentorNames: MentorNames;
}

export interface MentorControlsProps {
  isChatMode: boolean;
  toggleChatMode: () => void;
  mentorCharacter: MentorCharacter;
}

export interface ChatInterfaceProps {
  isChatMode: boolean;
  chatHistory: ChatMessage[];
  isProcessing: boolean;
  userMessage: string;
  setUserMessage: (message: string) => void;
  handleSendMessage: () => void;
  mentorNames: MentorNames;
  mentorCharacter: MentorCharacter;
  mentorMessage: string;
  isInlineChat?: boolean;
}

export interface ChatSectionProps {
  chatHistory?: ChatMessage[];
  isProcessing?: boolean;
  userMessage?: string;
  setUserMessage: (message: string) => void;
  handleSendMessage: () => void;
  clearChat?: () => void;
  mentorNames: Record<string, string>;
  mentorCharacter: string;
}
