
import React from 'react';
import { Lightbulb } from 'lucide-react';
import { Button } from '@/components/ui/button';
import VoiceEnabledInput from '@/components/shared/VoiceEnabledInput';

interface MusicDescriptionInputProps {
  musicDescription: string;
  setMusicDescription: (value: string) => void;
  handleGenerateDescription: () => void;
  isGeneratingDescription: boolean;
  handleImproveDescription: () => void;
  isImprovingDescription: boolean;
}

export const MusicDescriptionInput = ({ 
  musicDescription, 
  setMusicDescription, 
  handleGenerateDescription, 
  isGeneratingDescription,
  handleImproveDescription,
  isImprovingDescription
}: MusicDescriptionInputProps) => {
  return (
    <div>
      <label htmlFor="musicDescription" className="block text-sm text-center font-medium mb-1">
        Describe Your Music
      </label>
      {/* Text area */}
      <VoiceEnabledInput
        id="musicDescription"
        isTextArea
        value={musicDescription}
        onChange={(e) => setMusicDescription(e.target.value)}
        onTextAdded={(text) => setMusicDescription(
          musicDescription + (musicDescription ? ' ' : '') + text
        )}
        placeholder="Describe the mood, style, and instruments you want..."
      />
      {/* Action buttons centered below */}
      <div className="flex justify-center gap-2 mt-3">
        <Button 
          onClick={handleGenerateDescription} 
          disabled={isGeneratingDescription}
          variant="outline" 
          size="sm" 
          className="!border-emerald-500 !text-emerald-500 hover:!bg-emerald-500 hover:!text-white gap-2"
        >
          {isGeneratingDescription ? (
            <>
              <div className="h-3 w-3 border-2 border-current border-t-transparent rounded-full animate-spin"></div>
              Generating...
            </>
          ) : (
            <>
              <Lightbulb className="h-3 w-3 !font-bold" />
              Get Music Idea
            </>
          )}
        </Button>
        <Button
          onClick={handleImproveDescription}
          disabled={isImprovingDescription}
          variant="outline"
          size="sm"
          className="border-emerald-500 text-emerald-500 hover:bg-emerald-500 hover:text-white gap-2"
        >
          {isImprovingDescription ? (
            <>
              <div className="h-3 w-3 border-2 border-current border-t-transparent rounded-full animate-spin"></div>
              Improving...
            </>
          ) : (
            <>
              <Lightbulb className="h-3 w-3" />
              Improve Idea
            </>
          )}
        </Button>
      </div>
    </div>
  );
};

export default MusicDescriptionInput;
