import { NextRequest, NextResponse } from "next/server";
// import Replicate from "replicate";

// const replicate = new Replicate({
//   auth: process.env.REPLICATE_API_TOKEN,
// });

// In production and preview deployments (on Vercel), the VERCEL_URL environment variable is set.
// In development (on your local machine), the NGROK_HOST environment variable is set.
// const WEBHOOK_HOST = process.env.VERCEL_URL
//   ? `https://${process.env.VERCEL_URL}`
//   : process.env.NGROK_HOST;

type PredictionInput = {
  prompt: string;
  width: number;
  height: number;
  num_outputs: number;
  scheduler: string;
  num_inference_steps: number;
  guidance_scale: number;
  seed: number;
};

type PredictionOptions = {
  model: string;
  input: PredictionInput;
  webhook?: string;
  webhook_events_filter?: string[];
};

export async function POST(request: NextRequest) {
  if (!process.env.REPLICATE_API_TOKEN) {
    return new NextResponse("Replicate API token not configured", { status: 500 });
  }

  const { prompt, aspectRatio = '1:1' } = await request.json();

  // Calculate dimensions based on aspect ratio
  let width, height;
  switch (aspectRatio) {
    case '16:9':
      width = 1024;
      height = 576;
      break;
    case '9:16':
      width = 576;
      height = 1024;
      break;
    case '4:3':
      width = 1024;
      height = 768;
      break;
    case '3:4':
      width = 768;
      height = 1024;
      break;
    default: // 1:1
      width = 768;
      height = 768;
  }

  const options: PredictionOptions = {
    model: 'stability-ai/stable-diffusion:27b93a2413e7f36cd83da926f3656280b2931564ff050bf9575f1fdf9bcd7478',
    input: { 
      prompt,
      width,
      height,
      num_outputs: 1,
      scheduler: "K_EULER",
      num_inference_steps: 20,
      guidance_scale: 7.5,
      seed: Math.floor(Math.random() * 1000000)
    }
  };

  try {
    const response = await fetch(
      "https://api.replicate.com/v1/predictions",
      {
        method: "POST",
        headers: {
          Authorization: `Token ${process.env.REPLICATE_API_TOKEN}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(options),
      }
    );

    if (!response.ok) {
      return new NextResponse("Prediction creation failed", { status: 500 });
    }

    const result = await response.json();
    return NextResponse.json(result);
  } catch (error) {
    console.error("Error creating prediction:", error);
    return new NextResponse("Error creating prediction", { status: 500 });
  }
}