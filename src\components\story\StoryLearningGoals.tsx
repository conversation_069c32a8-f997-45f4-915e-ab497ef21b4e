"use client";

import React from "react";
import { useStory } from "./StoryContext";
import { ChevronDown } from "lucide-react";
import {
    Accordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger,
} from "@/components/ui/learning-accordion";

const StoryLearningGoals = () => {
    const { learningMode } = useStory();

    if (!learningMode) return null;

    const goals = [
        {
            id: "1",
            title: "Plot Development: Learn how to structure a beginning, middle, and end",
            content:
                "A good story needs a clear structure. The beginning introduces characters and setting, the middle presents a problem or conflict, and the end shows how it's resolved.",
        },
        {
            id: "2",
            title: "Character Creation: Build memorable characters with distinct traits",
            content:
                "Strong characters have unique personalities, goals, and challenges. Think about what makes your character special and how they change throughout the story.",
        },
        {
            id: "3",
            title: "Descriptive Language: Practice using vivid words to create images in readers' minds",
            content:
                "Use specific details and sensory words to help readers imagine the scene. Instead of 'The forest was dark,' try 'The ancient pine trees blocked out the sunlight, casting deep shadows on the mossy ground.'",
        },
    ];

    return (
        <div className="mb-6">
            <h2 className="flex items-center gap-2 text-xl font-bold mb-4">
                <span className="text-purple-400 text-2xl">✨</span>
                Story Writing Learning Goals
            </h2>
            <Accordion type="single" collapsible className="w-full">
                {goals.map((goal) => (
                    <AccordionItem
                        key={goal.id}
                        value={goal.id}
                        className="mb-2"
                    >
                        <AccordionTrigger className="p-4">
                            <div className="flex items-center gap-4">
                                <span className="text-purple-400 text-xl">
                                    {goal.id}
                                </span>
                                <span className="font-medium text-gray-900">
                                    {goal.title}
                                </span>
                            </div>
                            <ChevronDown className="h-4 w-4 shrink-0 text-purple-400 transition-transform duration-200" />
                        </AccordionTrigger>
                        <AccordionContent className="px-4 pb-4 pt-0 text-gray-600">
                            {goal.content}
                        </AccordionContent>
                    </AccordionItem>
                ))}
            </Accordion>
        </div>
    );
};

export default StoryLearningGoals;
