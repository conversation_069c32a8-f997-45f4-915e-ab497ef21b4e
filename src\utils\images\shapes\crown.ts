
/**
 * Draws a crown shape on the canvas (princess)
 */
export const drawCrownShape = (ctx: CanvasRenderingContext2D, x: number, y: number, size: number) => {
  // Base of crown
  ctx.moveTo(x - size*0.5, y + size*0.1);
  ctx.lineTo(x + size*0.5, y + size*0.1);
  ctx.lineTo(x + size*0.4, y - size*0.1);
  ctx.lineTo(x + size*0.25, y + size*0.1);
  ctx.lineTo(x, y - size*0.3);
  ctx.lineTo(x - size*0.25, y + size*0.1);
  ctx.lineTo(x - size*0.4, y - size*0.1);
  ctx.lineTo(x - size*0.5, y + size*0.1);
  
  // Princess silhouette below crown
  ctx.moveTo(x, y + size*0.1);
  ctx.quadraticCurveTo(
    x, y + size*0.3,
    x - size*0.3, y + size*0.4
  );
  ctx.lineTo(x - size*0.2, y + size*0.8);
  ctx.lineTo(x + size*0.2, y + size*0.8);
  ctx.lineTo(x + size*0.3, y + size*0.4);
  ctx.quadraticCurveTo(
    x, y + size*0.3,
    x, y + size*0.1
  );
  
  // Jewels on crown
  ctx.moveTo(x - size*0.4, y);
  ctx.arc(x - size*0.4, y, size*0.06, 0, 2 * Math.PI);
  
  ctx.moveTo(x, y - size*0.2);
  ctx.arc(x, y - size*0.2, size*0.06, 0, 2 * Math.PI);
  
  ctx.moveTo(x + size*0.4, y);
  ctx.arc(x + size*0.4, y, size*0.06, 0, 2 * Math.PI);
};
