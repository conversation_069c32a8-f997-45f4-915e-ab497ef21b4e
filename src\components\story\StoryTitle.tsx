"use client";

import React from "react";
import { Input } from "@/components/ui/input";
import { Info } from "lucide-react";
import { useStory } from "./StoryContext";

const StoryTitle = () => {
    const { title, setTitle, learningMode } = useStory();

    return (
        <div className="mb-6 text-center flex flex-col items-center">
            <h3 className="text-lg font-medium mb-2">Story Title</h3>
            <Input
                type="text"
                placeholder="Enter a fun title for your story"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                className="mb-4 rounded-full px-4 py-4 placeholder:text-center placeholder:text-gray-400"
            />
            {learningMode && (
                <div className="flex items-center gap-2 bg-littlespark-light-blue-2 border border-littlespark-light-blue p-4 rounded-full">
                    <Info className="h-5 w-5 text-blue-500" />
                    <div className="">
                        <h4 className="font-semibold text-sm text-blue-600 font-fredoka">
                            Title Creation
                        </h4>
                        <p className="text-sm text-black">
                            A good story title should give readers a hint about
                            what your story is about without giving away the
                            ending!
                        </p>
                    </div>
                </div>
            )}
        </div>
    );
};

export default StoryTitle;
