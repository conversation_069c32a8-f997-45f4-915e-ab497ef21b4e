
/**
 * Utility functions for working with canvas elements
 */

/**
 * Clears a canvas and redraws the base image if available
 */
export const clearCanvas = (
  canvas: HTMLCanvasElement | null,
  selectedImage: string | null
) => {
  if (!canvas) return;
  
  const ctx = canvas.getContext('2d');
  if (!ctx) return;
  
  ctx.clearRect(0, 0, canvas.width, canvas.height);
  
  if (selectedImage) {
    const img = new Image();
    img.onload = () => {
      if (!ctx) return;
      ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
    };
    img.src = selectedImage;
  }
};

/**
 * Downloads the current canvas content as a PNG image
 */
export const downloadCanvasImage = (
  canvas: HTMLCanvasElement | null,
  filename: string = 'my-coloring-creation.png'
) => {
  if (!canvas) return;
  
  const link = document.createElement('a');
  link.download = filename;
  link.href = canvas.toDataURL('image/png');
  link.click();
};
