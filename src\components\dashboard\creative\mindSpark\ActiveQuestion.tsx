
import React from 'react';
import { Question } from './types';
import { Button } from '@/components/ui/button';

interface ActiveQuestionProps {
  currentQuestion: Question;
  selectedAnswerIndex: number | null;
  showResults: boolean;
  onAnswerSelect: (index: number) => void;
  onSubmitAnswer: () => void;
  onNextQuestion: () => void;
}

const ActiveQuestion: React.FC<ActiveQuestionProps> = ({
  currentQuestion,
  selectedAnswerIndex,
  showResults,
  onAnswerSelect,
  onSubmitAnswer,
  onNextQuestion
}) => {
  return (
    <div className="space-y-4">
      <p className="text-lg font-medium">{currentQuestion.question}</p>
      
      <div className="space-y-2">
        {currentQuestion.answers.map((answer, index) => (
          <div 
            key={index}
            className={`p-3 rounded-lg border cursor-pointer transition-colors ${
              selectedAnswerIndex === index 
                ? 'bg-spark-blue/10 border-spark-blue' 
                : 'hover:bg-gray-50 border-gray-200'
            } ${
              showResults && index === currentQuestion.correctAnswerIndex
                ? 'bg-green-100 border-green-500'
                : ''
            } ${
              showResults && index === selectedAnswerIndex && index !== currentQuestion.correctAnswerIndex
                ? 'bg-red-100 border-red-500'
                : ''
            }`}
            onClick={() => !showResults && onAnswerSelect(index)}
          >
            <div className="flex items-start">
              <div className={`w-6 h-6 flex-shrink-0 rounded-full flex items-center justify-center mr-2 ${
                selectedAnswerIndex === index 
                  ? 'bg-spark-blue text-white' 
                  : 'bg-gray-100'
              }`}>
                {String.fromCharCode(65 + index)}
              </div>
              <span>{answer}</span>
            </div>
          </div>
        ))}
      </div>
      
      {showResults && currentQuestion.explanation && (
        <div className="bg-blue-50 p-4 rounded-lg">
          <p className="font-medium text-spark-blue mb-1">Explanation:</p>
          <p className="text-blue-800">{currentQuestion.explanation}</p>
        </div>
      )}
      
      <div className="pt-4 flex justify-between">
        {!showResults ? (
          <Button
            onClick={onSubmitAnswer}
            disabled={selectedAnswerIndex === null}
          >
            Submit Answer
          </Button>
        ) : (
          <Button
            onClick={onNextQuestion}
          >
            Next Question
          </Button>
        )}
      </div>
    </div>
  );
};

export default ActiveQuestion;
