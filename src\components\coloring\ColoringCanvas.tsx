"use client";
import React, { useRef, useImperativeHandle, forwardRef, useState, useEffect } from "react";

export interface ColoringCanvasHandle {
  clear: () => void;
  download: () => void;
  setTool: (tool: ToolType) => void;
  setColor: (color: string) => void;
  setBrushSize: (size: number) => void;
  resetView: () => void;
  loadImage: (src: string) => void;
  getCanvasData: () => string;
  undo: () => void;
  redo: () => void;
  zoomIn: () => void;
  zoomOut: () => void;
}

export type ToolType = "brush" | "eraser" | "fill" | "picker" | "pan" | "zoom";

interface ColoringCanvasProps {
  width?: number;
  height?: number;
}

const DEFAULT_WIDTH = 700;
const DEFAULT_HEIGHT = 500;

const ColoringCanvas = forwardRef<ColoringCanvasHandle, ColoringCanvasProps>(
  ({ width = DEFAULT_WIDTH, height = DEFAULT_HEIGHT }, ref) => {
    const canvasRef = useRef<HTMLCanvasElement>(null);
    const [tool, setTool] = useState<ToolType>("brush");
    const [color, setColor] = useState<string>("#000000");
    const [brushSize, setBrushSize] = useState<number>(10);
    const [isDrawing, setIsDrawing] = useState(false);
    const [lastPos, setLastPos] = useState<{ x: number; y: number } | null>(null);
    const [zoom, setZoom] = useState(1);
    const [canvasOffset, setCanvasOffset] = useState({ x: 0, y: 0 });
    const [history, setHistory] = React.useState<ImageData[]>([]);
    const [historyIndex, setHistoryIndex] = React.useState(-1);

    // Responsive canvas dimensions
    const [canvasWidth, setCanvasWidth] = useState(width);
    const [canvasHeight, setCanvasHeight] = useState(height);

    // Update canvas size on window resize
    useEffect(() => {
      const updateCanvasSize = () => {
        const isMobile = window.innerWidth < 768;
        const isTablet = window.innerWidth < 1024;
        
        if (isMobile) {
          setCanvasWidth(Math.min(350, window.innerWidth - 40));
          setCanvasHeight(Math.min(250, window.innerHeight * 0.4));
        } else if (isTablet) {
          setCanvasWidth(Math.min(500, window.innerWidth - 80));
          setCanvasHeight(Math.min(350, window.innerHeight * 0.5));
        } else {
          setCanvasWidth(width);
          setCanvasHeight(height);
        }
      };

      updateCanvasSize();
      window.addEventListener('resize', updateCanvasSize);
      return () => window.removeEventListener('resize', updateCanvasSize);
    }, [width, height]);

    const saveHistory = () => {
      const canvas = canvasRef.current;
      if (!canvas) return;
      const ctx = canvas.getContext('2d');
      if (!ctx) return;
      const data = ctx.getImageData(0, 0, canvasWidth, canvasHeight);
      // prune future history on new action
      const newHist = history.slice(0, historyIndex + 1);
      newHist.push(data);
      if (newHist.length > 50) newHist.shift();
      setHistory(newHist);
      setHistoryIndex(newHist.length - 1);
    };

    useImperativeHandle(ref, () => ({
      clear: () => {
        const ctx = canvasRef.current?.getContext("2d");
        if (ctx) {
          ctx.clearRect(0, 0, canvasWidth, canvasHeight);
        }
      },
      download: () => {
        if (canvasRef.current) {
          const url = canvasRef.current.toDataURL("image/png");
          const link = document.createElement("a");
          link.href = url;
          link.download = "coloring.png";
          link.click();
        }
      },
      setTool,
      setColor,
      setBrushSize,
      resetView: () => {
        setZoom(1);
        setCanvasOffset({ x: 0, y: 0 });
      },
      loadImage: (src: string) => {
        console.log('🔥 CANVAS: loadImage called with src:', src);
        const img = new Image();
        img.crossOrigin = "anonymous"; // Enable CORS for data URLs
        img.onload = () => {
          console.log('🔥 CANVAS: Image loaded successfully, drawing to canvas');
          const ctx = canvasRef.current?.getContext("2d");
          if (ctx) {
            console.log('🔥 CANVAS: Drawing image to canvas');
            // Clear canvas first
            ctx.clearRect(0, 0, canvasWidth, canvasHeight);
            // Fill with white background
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(0, 0, canvasWidth, canvasHeight);
            // Draw the image
            ctx.drawImage(img, 0, 0, canvasWidth, canvasHeight);
          }
        };
        img.onerror = (err) => {
          console.log('🔥 CANVAS: Image load error:', err);
        };
        img.src = src;
      },
      getCanvasData: () => {
        const canvas = canvasRef.current;
        if (canvas) {
          return canvas.toDataURL('image/png');
        }
        return '';
      },
      undo: () => {
        if (historyIndex > 0) {
          const newIndex = historyIndex - 1;
          const canvas = canvasRef.current;
          const ctx = canvas?.getContext('2d');
          if (ctx && history[newIndex]) {
            ctx.putImageData(history[newIndex], 0, 0);
            setHistoryIndex(newIndex);
          }
        }
      },
      redo: () => {
        if (historyIndex < history.length - 1) {
          const newIndex = historyIndex + 1;
          const canvas = canvasRef.current;
          const ctx = canvas?.getContext('2d');
          if (ctx && history[newIndex]) {
            ctx.putImageData(history[newIndex], 0, 0);
            setHistoryIndex(newIndex);
          }
        }
      },
      zoomIn: () => {
        setZoom((z) => Math.min(3, z + 0.2));
      },
      zoomOut: () => {
        setZoom((z) => Math.max(0.5, z - 0.2));
      }
    }));

    // Drawing logic with proper coordinate transformation
    const getCanvasPos = (e: React.MouseEvent<HTMLCanvasElement, MouseEvent>) => {
      const rect = canvasRef.current!.getBoundingClientRect();
      const x = (e.clientX - rect.left) / zoom - canvasOffset.x;
      const y = (e.clientY - rect.top) / zoom - canvasOffset.y;
      return { x, y };
    };

    const handlePointerDown = (e: React.MouseEvent<HTMLCanvasElement, MouseEvent>) => {
      if (tool === "pan") {
        return;
      }
      const pos = getCanvasPos(e);
      setIsDrawing(true);
      setLastPos(pos);
      if (tool === "fill") {
        floodFill(pos.x, pos.y, color);
      } else if (tool === "picker") {
        pickColor(pos.x, pos.y);
      } else if (tool === "brush" || tool === "eraser") {
        drawDot(pos.x, pos.y);
      }
    };

    const handlePointerMove = (e: React.MouseEvent<HTMLCanvasElement, MouseEvent>) => {
      if (tool === "pan") {
      }
      if (!isDrawing || !lastPos) return;
      const pos = getCanvasPos(e);
      if (tool === "brush" || tool === "eraser") {
        drawLine(lastPos.x, lastPos.y, pos.x, pos.y);
        setLastPos(pos);
      }
    };

    const handlePointerUp = () => {
      setIsDrawing(false);
      setLastPos(null);
      // save state for undo
      saveHistory();
    };

    // Drawing helpers with proper transformation
    const drawDot = (x: number, y: number) => {
      const ctx = canvasRef.current?.getContext("2d");
      if (ctx) {
        ctx.save();
        ctx.translate(canvasOffset.x, canvasOffset.y);
        ctx.beginPath();
        ctx.arc(x, y, brushSize / 2, 0, 2 * Math.PI);
        ctx.fillStyle = tool === "eraser" ? "#fff" : color;
        ctx.globalCompositeOperation = tool === "eraser" ? "destination-out" : "source-over";
        ctx.fill();
        ctx.restore();
      }
    };

    const drawLine = (x1: number, y1: number, x2: number, y2: number) => {
      const ctx = canvasRef.current?.getContext("2d");
      if (ctx) {
        ctx.save();
        ctx.translate(canvasOffset.x, canvasOffset.y);
        ctx.beginPath();
        ctx.moveTo(x1, y1);
        ctx.lineTo(x2, y2);
        ctx.strokeStyle = tool === "eraser" ? "#fff" : color;
        ctx.lineWidth = brushSize;
        ctx.lineCap = "round";
        ctx.globalCompositeOperation = tool === "eraser" ? "destination-out" : "source-over";
        ctx.stroke();
        ctx.restore();
      }
    };

    // Flood fill with proper transformation
    const floodFill = (x: number, y: number, fillColor: string) => {
      const ctx = canvasRef.current?.getContext("2d");
      if (!ctx) return;
      const adjustedX = Math.floor(x + canvasOffset.x);
      const adjustedY = Math.floor(y + canvasOffset.y);
      const imageData = ctx.getImageData(0, 0, canvasWidth, canvasHeight);
      const data = imageData.data;
      const targetColor = getColorAtPixel(data, adjustedX, adjustedY);
      const fillRgba = hexToRgba(fillColor);
      
      // Don't fill if target color is the same as fill color
      if (colorsMatch(targetColor, fillRgba)) return;
      
      const stack = [[Math.floor(adjustedX), Math.floor(adjustedY)]];
      
      while (stack.length) {
        const [cx, cy] = stack.pop()!;
        if (cx < 0 || cx >= canvasWidth || cy < 0 || cy >= canvasHeight) continue;
        
        const idx = (cy * canvasWidth + cx) * 4;
        const currentColor = getColorAtPixel(data, cx, cy);
        
        // Check if current pixel matches target color and isn't a line boundary
        if (colorsMatch(currentColor, targetColor)) {
          data[idx] = fillRgba[0];
          data[idx + 1] = fillRgba[1];
          data[idx + 2] = fillRgba[2];
          data[idx + 3] = 255;
          stack.push([cx - 1, cy], [cx + 1, cy], [cx, cy - 1], [cx, cy + 1]);
        }
      }
      ctx.putImageData(imageData, 0, 0);
    };

    // Color picker with proper transformation
    const pickColor = (x: number, y: number) => {
      const ctx = canvasRef.current?.getContext("2d");
      if (!ctx) return;
      const adjustedX = Math.floor(x + canvasOffset.x);
      const adjustedY = Math.floor(y + canvasOffset.y);
      const imageData = ctx.getImageData(adjustedX, adjustedY, 1, 1);
      const [r, g, b] = imageData.data;
      setColor(rgbToHex(r, g, b));
    };

    // Color helpers
    const getColorAtPixel = (data: Uint8ClampedArray, x: number, y: number) => {
      const idx = (Math.floor(y) * canvasWidth + Math.floor(x)) * 4;
      return [data[idx], data[idx + 1], data[idx + 2], data[idx + 3]];
    };
    const colorsMatch = (a: number[], b: number[]) => a.every((v, i) => v === b[i]);
    const hexToRgba = (hex: string) => {
      const h = hex.replace('#', '');
      return [parseInt(h.substring(0, 2), 16), parseInt(h.substring(2, 4), 16), parseInt(h.substring(4, 6), 16), 255];
    };
    const rgbToHex = (r: number, g: number, b: number) =>
      '#' + [r, g, b].map((x) => x.toString(16).padStart(2, '0')).join('');

    // Zoom with wheel (improved)
    const handleWheel = (e: React.WheelEvent<HTMLDivElement>) => {
      const delta = e.deltaY < 0 ? 0.1 : -0.1;
      setZoom((z) => Math.max(0.5, Math.min(3, z + delta)));
    };

    return (
      <div
        className="relative flex flex-col items-center w-full"
        style={{ width: canvasWidth, height: canvasHeight, overflow: 'hidden', touchAction: 'none' }}
        onWheel={handleWheel}
      >
        <canvas
          ref={canvasRef}
          width={canvasWidth}
          height={canvasHeight}
          style={{
            border: '1px solid #e5e7eb',
            borderRadius: '12px',
            background: '#fff',
            overflow: 'hidden',
            transform: `scale(${zoom})`,
            transition: 'transform 0.2s',
            cursor: tool === 'pan' ? 'grab' : tool === 'picker' ? 'crosshair' : 'pointer',
            boxShadow: '0 4px 24px 0 rgba(0,0,0,0.07)',
            display: 'block',
            maxWidth: '100%',
            maxHeight: '100%',
          }}
          onMouseDown={handlePointerDown}
          onMouseMove={handlePointerMove}
          onMouseUp={handlePointerUp}
          onMouseLeave={handlePointerUp}
        />
      </div>
    );
  }
);

ColoringCanvas.displayName = "ColoringCanvas";

export default ColoringCanvas; 