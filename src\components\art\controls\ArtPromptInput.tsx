import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON><PERSON>, Wand2 } from 'lucide-react';
import VoiceEnabledInput from '@/components/shared/VoiceEnabledInput';

interface ArtPromptInputProps {
  title: string;
  setTitle: (title: string) => void;
  prompt: string;
  setPrompt: (prompt: string) => void;
  handleGenerateIdea?: () => void;
  isGeneratingIdea?: boolean;
  handleImprovePrompt?: () => void;
  isImprovingPrompt?: boolean;
}

const ArtPromptInput = ({ 
  title, 
  setTitle, 
  prompt, 
  setPrompt,
  handleGenerateIdea,
  isGeneratingIdea,
  handleImprovePrompt,
  isImprovingPrompt
}: ArtPromptInputProps) => {
  return (
    <>
      <div className="mb-6">
        <label htmlFor="title" className="block text-lg font-medium mb-2">
          Artwork Title
        </label>
        <input
          id="title"
          type="text"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          placeholder="Give your artwork a name"
          className="w-full text-base sm:text-lg py-3 sm:py-4 lg:py-6 px-4 rounded-xl border-2 border-gray-200 focus:border-[#00B7FD] focus:ring-2 focus:ring-[#00B7FD]/20 outline-none transition-colors"
        />
      </div>
      
      <div className="mb-4">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 mb-2">
          <label htmlFor="prompt" className="block text-lg font-medium">
            Describe Your Artwork
          </label>
          
          <div className="flex flex-col sm:flex-row gap-2">
            {handleImprovePrompt && (
              <Button 
                onClick={handleImprovePrompt}
                disabled={isImprovingPrompt || !prompt.trim()}
                variant="outline"
                size="sm"
                className="gap-1 w-full sm:w-auto"
              >
                <Wand2 className="h-4 w-4" />
                <span className="sm:hidden">Improve</span>
                <span className="hidden sm:inline">{isImprovingPrompt ? "Improving..." : "Improve Prompt"}</span>
              </Button>
            )}
            
            {handleGenerateIdea && (
              <Button 
                onClick={handleGenerateIdea}
                disabled={isGeneratingIdea}
                variant="outline"
                size="sm"
                className="gap-1 w-full sm:w-auto"
              >
                <Sparkles className="h-4 w-4" />
                <span className="sm:hidden">Ideas</span>
                <span className="hidden sm:inline">{isGeneratingIdea ? "Getting Ideas..." : "Give Me an Idea"}</span>
              </Button>
            )}
          </div>
        </div>
        
        <VoiceEnabledInput
          id="prompt"
          value={prompt}
          onChange={(e) => setPrompt(e.target.value)}
          onTextAdded={(text) => setPrompt(prompt + (prompt ? ' ' : '') + text)}
          placeholder="A magical castle with a rainbow and unicorns..."
          className="min-h-[150px] text-lg"
          isTextArea={true}
        />
      </div>
    </>
  );
};

export default ArtPromptInput;
