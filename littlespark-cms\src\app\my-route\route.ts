import configPromise from '@payload-config'
import { getPayload } from 'payload'

export const GET = async (request: Request) => {
  try {
    console.log('🔍 [CMS DIRECT] Fetching challenges directly from database');

    const payload = await getPayload({
      config: configPromise,
    })

    // Fetch challenges without authentication
    const challenges = await payload.find({
      collection: 'challenges',
      where: {
        status: {
          equals: 'published'
        }
      },
      limit: 100,
      // Disable authentication for this query
      overrideAccess: true,
      user: null
    });

    console.log('✅ [CMS DIRECT] Found challenges:', challenges.docs.length);
    console.log('📋 [CMS DIRECT] Challenge titles:', challenges.docs.map(c => c.title));

    return Response.json({
      success: true,
      challenges: challenges.docs,
      total: challenges.totalDocs,
      message: 'Challenges fetched directly from CMS database'
    });
  } catch (error) {
    console.log('❌ [CMS DIRECT] Error:', error);
    return Response.json({
      success: false,
      error: error.message,
      message: 'Failed to fetch challenges from CMS database'
    });
  }
}
