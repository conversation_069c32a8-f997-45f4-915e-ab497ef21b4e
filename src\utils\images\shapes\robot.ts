
/**
 * Draws a robot shape on the canvas
 */
export const drawRobotShape = (ctx: CanvasRenderingContext2D, x: number, y: number, size: number) => {
  // Head
  ctx.rect(x - size*0.3, y - size*0.6, size*0.6, size*0.4);
  
  // Eyes
  ctx.moveTo(x - size*0.15, y - size*0.45);
  ctx.arc(x - size*0.15, y - size*0.45, size*0.07, 0, 2 * Math.PI);
  
  ctx.moveTo(x + size*0.15, y - size*0.45);
  ctx.arc(x + size*0.15, y - size*0.45, size*0.07, 0, 2 * Math.PI);
  
  // Mouth
  ctx.moveTo(x - size*0.15, y - size*0.3);
  ctx.lineTo(x + size*0.15, y - size*0.3);
  
  // Antenna
  ctx.moveTo(x, y - size*0.6);
  ctx.lineTo(x, y - size*0.8);
  ctx.arc(x, y - size*0.8, size*0.05, 0, 2 * Math.PI);
  
  // Body
  ctx.rect(x - size*0.4, y - size*0.15, size*0.8, size*0.5);
  
  // Arms
  ctx.moveTo(x - size*0.4, y - size*0.05);
  ctx.lineTo(x - size*0.6, y + size*0.2);
  
  ctx.moveTo(x + size*0.4, y - size*0.05);
  ctx.lineTo(x + size*0.6, y + size*0.2);
  
  // Legs
  ctx.moveTo(x - size*0.2, y + size*0.35);
  ctx.lineTo(x - size*0.2, y + size*0.7);
  
  ctx.moveTo(x + size*0.2, y + size*0.35);
  ctx.lineTo(x + size*0.2, y + size*0.7);
  
  // Control panel
  for (let i = 0; i < 3; i++) {
    ctx.moveTo(x - size*0.25 + i*size*0.25, y);
    ctx.arc(x - size*0.25 + i*size*0.25, y, size*0.05, 0, 2 * Math.PI);
  }
};
