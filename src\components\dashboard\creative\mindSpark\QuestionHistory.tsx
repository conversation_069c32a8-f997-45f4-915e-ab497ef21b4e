import React from "react";
import { Question } from "./types";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";

interface QuestionHistoryProps {
    questionHistory: Question[];
    onBack: () => void;
}

const QuestionHistory: React.FC<QuestionHistoryProps> = ({
    questionHistory,
    onBack,
}) => {
    return (
        <div className="space-y-4">
            <h3 className="text-lg font-semibold mb-2">
                Your Question History
            </h3>
            {questionHistory.length === 0 ? (
                <p className="text-gray-500">
                    You haven&apos;t answered any questions yet.
                </p>
            ) : (
                <div className="space-y-4">
                    {questionHistory.map((q, index) => (
                        <Card
                            key={index}
                            className={`p-4 ${
                                q.isCorrect ? "bg-green-50" : "bg-red-50"
                            }`}
                        >
                            <p className="font-medium">{q.question}</p>
                            <p className="text-sm mt-2">
                                Your answer:{" "}
                                <span
                                    className={
                                        q.isCorrect
                                            ? "text-green-600 font-semibold"
                                            : "text-red-600 font-semibold"
                                    }
                                >
                                    {q.userAnswer}
                                </span>
                            </p>
                            {!q.isCorrect &&
                                q.correctAnswerIndex !== undefined && (
                                    <p className="text-sm mt-1">
                                        Correct answer:{" "}
                                        <span className="text-green-600 font-semibold">
                                            {q.answers[q.correctAnswerIndex]}
                                        </span>
                                    </p>
                                )}
                            {q.explanation && (
                                <p className="text-sm mt-2 italic text-gray-600">
                                    {q.explanation}
                                </p>
                            )}
                        </Card>
                    ))}
                </div>
            )}
            <Button onClick={onBack} className="mt-4">
                Back to Questions
            </Button>
        </div>
    );
};

export default QuestionHistory;
