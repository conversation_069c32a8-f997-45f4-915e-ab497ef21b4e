
import { useState, useCallback } from 'react';
import { Question, AgeGroup } from '../types';
import { 
  getQuestionsWithAI,
  INITIAL_QUESTIONS_COUNT
} from '../questionUtils';
import { toast } from 'sonner';

export const useQuestions = (
  selectedAgeGroup: AgeGroup,
  setCurrentQuestion: React.Dispatch<React.SetStateAction<Question | null>>,
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>
) => {
  const [upcomingQuestions, setUpcomingQuestions] = useState<Question[]>([]);
  
  // Define fetch questions function with error handling first
  const fetchQuestionsWithErrorHandling = useCallback(async (count: number): Promise<Question[]> => {
    try {
      return await getQuestionsWithAI(selectedAgeGroup, count);
    } catch (error) {
      console.error('Error generating questions:', error);
      
      // Only show toast on first failure
      if (count === INITIAL_QUESTIONS_COUNT) {
        toast.error('Failed to load questions', {
          description: 'Using our standard question bank instead.'
        });
      }
      
      // Try again - getQuestionsWithAI already includes fallback mechanism
      return await getQuestionsWithAI(selectedAgeGroup, count);
    }
  }, [selectedAgeGroup]);
  
  // Load initial set of questions
  const loadQuestions = useCallback(async () => {
    setIsLoading(true);
    
    try {
      const questions = await fetchQuestionsWithErrorHandling(INITIAL_QUESTIONS_COUNT);
      
      // Set the first question as current
      if (questions.length > 0) {
        setCurrentQuestion(questions[0]);
        setUpcomingQuestions(questions.slice(1));
      }
    } finally {
      setIsLoading(false);
    }
  }, [fetchQuestionsWithErrorHandling, setCurrentQuestion, setIsLoading]);
  
  // Generate a new question when requested
  const generateNewQuestion = useCallback(async () => {
    if (upcomingQuestions.length > 0) {
      // Use the next question from the queue
      setCurrentQuestion(upcomingQuestions[0]);
      setUpcomingQuestions(upcomingQuestions.slice(1));
      
      // If we're running low on upcoming questions, add more
      if (upcomingQuestions.length < 3) {
        setIsLoading(true);
        
        try {
          const newQuestions = await fetchQuestionsWithErrorHandling(3);
          setUpcomingQuestions(prev => [...prev, ...newQuestions]);
        } finally {
          setIsLoading(false);
        }
      }
    } else {
      // Queue is empty - generate a new batch
      setIsLoading(true);
      
      try {
        const newCurrentQuestion = await fetchQuestionsWithErrorHandling(1);
        const newQuestions = await fetchQuestionsWithErrorHandling(4);
        
        if (newCurrentQuestion.length > 0) {
          setCurrentQuestion(newCurrentQuestion[0]);
          setUpcomingQuestions(newQuestions);
        }
      } finally {
        setIsLoading(false);
      }
    }
  }, [upcomingQuestions, fetchQuestionsWithErrorHandling, setCurrentQuestion, setIsLoading]);
  
  return {
    loadQuestions,
    generateNewQuestion,
    upcomingQuestions
  };
};
