import type { CollectionConfig } from 'payload'

export const Challenges: CollectionConfig = {
  slug: 'challenges',
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'category', 'ageGroup', 'difficulty', 'status', 'publishedAt'],
    group: 'Content',
  },
  access: {
    // Content creators and admins can create challenges
    create: ({ req: { user } }) => {
      return user?.role === 'admin' ||
             user?.role === 'content-creator' ||
             user?.role === 'educator'
    },
    // For now, allow public read access - we'll filter in the API
    read: () => true,
    // Users can update their own challenges, admins can update all
    update: ({ req: { user } }) => {
      if (user?.role === 'admin') return true

      if (user?.role === 'content-creator' || user?.role === 'educator') {
        return {
          createdBy: {
            equals: user.id
          }
        }
      }

      return false
    },
    // Only admins can delete challenges
    delete: ({ req: { user } }) => {
      return user?.role === 'admin'
    },
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
      admin: {
        description: 'The main title of the creative challenge',
      },
    },
    {
      name: 'slug',
      type: 'text',
      required: true,
      unique: true,
      admin: {
        description: 'URL-friendly version of the title',
      },
      hooks: {
        beforeValidate: [
          ({ value, data }) => {
            if (!value && data?.title) {
              return data.title
                .toLowerCase()
                .replace(/[^a-z0-9]+/g, '-')
                .replace(/(^-|-$)/g, '')
            }
            return value
          },
        ],
      },
    },
    {
      name: 'description',
      type: 'textarea',
      required: true,
      admin: {
        description: 'Brief description of what children will create',
      },
    },
    {
      name: 'category',
      type: 'select',
      required: true,
      options: [
        { label: '🎨 Art & Drawing', value: 'art' },
        { label: '📚 Creative Writing', value: 'story' },
        { label: '🎵 Music & Sound', value: 'music' },
        { label: '🎮 Game Design', value: 'game' },
        { label: '💻 Coding & Logic', value: 'coding' },
        { label: '🎬 Video Creation', value: 'video' },
      ],
      admin: {
        description: 'Choose the primary category - this determines which tool opens when users click "Start Challenge"',
        position: 'sidebar',
      },
    },
    {
      name: 'ageGroup',
      type: 'select',
      required: true,
      hasMany: true,
      options: [
        { label: '6-8 years', value: '6-8' },
        { label: '9-11 years', value: '9-11' },
        { label: '12-14 years', value: '12-14' },
      ],
      admin: {
        description: 'Age groups this challenge is suitable for',
      },
    },
    {
      name: 'difficulty',
      type: 'select',
      required: true,
      options: [
        { label: 'Easy', value: 'easy' },
        { label: 'Medium', value: 'medium' },
        { label: 'Hard', value: 'hard' },
      ],
      admin: {
        description: 'Difficulty level for children',
      },
    },
    {
      name: 'estimatedTime',
      type: 'number',
      required: true,
      min: 5,
      max: 120,
      admin: {
        description: 'Estimated completion time in minutes',
        step: 5,
      },
    },
    {
      name: 'instructions',
      type: 'textarea',
      required: true,
      admin: {
        description: 'Step-by-step instructions for completing the challenge',
        rows: 8,
      },
    },
    {
      name: 'learningObjectives',
      type: 'array',
      required: true,
      minRows: 1,
      maxRows: 5,
      fields: [
        {
          name: 'objective',
          type: 'text',
          required: true,
        },
      ],
      admin: {
        description: 'What children will learn from this challenge',
      },
    },
    {
      name: 'materials',
      type: 'array',
      fields: [
        {
          name: 'material',
          type: 'text',
          required: true,
        },
        {
          name: 'optional',
          type: 'checkbox',
          defaultValue: false,
        },
      ],
      admin: {
        description: 'Materials needed to complete the challenge',
      },
    },
    {
      name: 'media',
      type: 'array',
      fields: [
        {
          name: 'file',
          type: 'upload',
          relationTo: 'media',
          required: true,
        },
        {
          name: 'caption',
          type: 'text',
        },
        {
          name: 'type',
          type: 'select',
          options: [
            { label: 'Tutorial Video', value: 'tutorial' },
            { label: 'Example Image', value: 'example' },
            { label: 'Reference Material', value: 'reference' },
            { label: 'Step Image', value: 'step' },
          ],
          required: true,
        },
        {
          name: 'order',
          type: 'number',
          defaultValue: 0,
          admin: {
            description: 'Display order (0 = first)',
          },
        },
      ],
      admin: {
        description: 'Images, videos, and other media for this challenge',
      },
    },
    {
      name: 'subscriptionTier',
      type: 'select',
      required: true,
      options: [
        { label: 'Free', value: 'free' },
        { label: 'Premium', value: 'premium' },
      ],
      defaultValue: 'premium',
      admin: {
        description: 'Whether this challenge requires a subscription',
      },
    },
    {
      name: 'featured',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        description: 'Show this challenge prominently on the homepage',
      },
    },
    {
      name: 'seasonal',
      type: 'group',
      fields: [
        {
          name: 'isSeasonalContent',
          type: 'checkbox',
          defaultValue: false,
        },
        {
          name: 'season',
          type: 'select',
          options: [
            { label: 'Spring', value: 'spring' },
            { label: 'Summer', value: 'summer' },
            { label: 'Fall/Autumn', value: 'fall' },
            { label: 'Winter', value: 'winter' },
            { label: 'Halloween', value: 'halloween' },
            { label: 'Christmas', value: 'christmas' },
            { label: 'New Year', value: 'newyear' },
          ],
          admin: {
            condition: (_, siblingData) => siblingData?.isSeasonalContent,
          },
        },
      ],
    },
    {
      name: 'status',
      type: 'select',
      required: true,
      options: [
        { label: 'Draft', value: 'draft' },
        { label: 'Under Review', value: 'review' },
        { label: 'Published', value: 'published' },
        { label: 'Archived', value: 'archived' },
      ],
      defaultValue: 'draft',
      admin: {
        description: 'Publication status of this challenge',
      },
    },
    {
      name: 'publishedAt',
      type: 'date',
      admin: {
        condition: (data) => data.status === 'published',
        description: 'When this challenge was published',
      },
    },
    {
      name: 'createdBy',
      type: 'relationship',
      relationTo: 'users',
      required: true,
      admin: {
        description: 'Content creator who made this challenge',
        position: 'sidebar',
      },
      access: {
        update: ({ req: { user } }) => {
          // Only admins can change the creator
          return user?.role === 'admin'
        },
      },
    },
  ],
  hooks: {
    beforeChange: [
      ({ data, operation, req }) => {
        // Auto-set publishedAt when status changes to published
        if (data.status === 'published' && !data.publishedAt) {
          data.publishedAt = new Date()
        }

        // Set createdBy on creation if not already set
        if (operation === 'create' && req.user && !data.createdBy) {
          data.createdBy = req.user.id
        }

        return data
      },
    ],
    beforeValidate: [
      ({ data, operation, req }) => {
        // Ensure createdBy is set for new challenges
        if (operation === 'create' && req.user && data && !data.createdBy) {
          data.createdBy = req.user.id
        }
        return data
      },
    ],
  },
  versions: {
    drafts: true,
    maxPerDoc: 10,
  },
}
