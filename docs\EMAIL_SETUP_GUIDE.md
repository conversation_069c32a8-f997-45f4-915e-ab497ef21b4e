# 📧 Email Setup Guide for Share With Parents

## Overview

The "Share With Parents" feature allows children to send their creative portfolio via email to their parents. This guide explains how to set up email functionality.

## ✅ What's Been Implemented

### 🎯 **Complete Email System**
- ✅ Email modal with parent email input
- ✅ Beautiful HTML email templates
- ✅ Secure portfolio sharing links
- ✅ Portfolio statistics in email
- ✅ Recent projects showcase
- ✅ 30-day link expiration
- ✅ Shared portfolio viewer page

### 📧 **Email Features**
- **Rich HTML Email**: Beautiful, responsive email design
- **Portfolio Summary**: Shows statistics and recent projects
- **Personal Messages**: Optional message from child to parent
- **Secure Links**: Time-limited access tokens
- **Mobile Friendly**: Responsive email design

## 🔧 Setup Instructions

### 1. **Install Dependencies**
```bash
npm install nodemailer @types/nodemailer
```
✅ Already installed!

### 2. **Configure Environment Variables**
Add these to your `.env.local` file:

```env
# Email Configuration
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your_app_password"
```

### 3. **Gmail Setup (Recommended)**

#### **Option A: Gmail App Password (Recommended)**
1. Go to [Google Account Settings](https://myaccount.google.com/)
2. Navigate to **Security** → **2-Step Verification**
3. Enable 2-Step Verification if not already enabled
4. Go to **App passwords**
5. Generate an app password for "Mail"
6. Use this app password in `SMTP_PASS`

#### **Option B: Other Email Providers**
- **Outlook**: `smtp-mail.outlook.com`, port `587`
- **Yahoo**: `smtp.mail.yahoo.com`, port `587`
- **Custom SMTP**: Use your provider's settings

### 4. **Test Email Configuration**
```bash
# Test the email API endpoint
curl -X POST "http://localhost:3000/api/share-portfolio" \
  -H "Content-Type: application/json" \
  -H "Cookie: your-session-cookie" \
  -d '{
    "parentEmail": "<EMAIL>",
    "message": "Test message",
    "includeStats": true,
    "includeProjects": true
  }'
```

## 🎯 How It Works

### **User Flow**
1. **Click "Share With Parents"** button on projects page
2. **Modal opens** asking for parent's email
3. **Enter email and optional message**
4. **Click "Send Email"** 
5. **Email sent** with portfolio link
6. **Parent receives** beautiful HTML email
7. **Parent clicks link** to view portfolio

### **Email Content**
- **Header**: Child's name and Little Spark branding
- **Personal Message**: Optional message from child
- **Statistics**: Creative achievements summary
- **Recent Projects**: Showcase of latest creations
- **Portfolio Link**: Secure link to full portfolio
- **About Section**: Information about Little Spark

### **Security Features**
- **Time-Limited Links**: 30-day expiration
- **Secure Tokens**: Base64URL encoded with timestamp
- **No Authentication Required**: Parents don't need accounts
- **Privacy Protected**: Only shared content is visible

## 📱 Testing the Feature

### **1. Basic Test**
1. Navigate to `/my-projects`
2. Click "Share With Parents" button
3. Enter a valid email address
4. Add optional message
5. Click "Send Email"
6. Check email inbox

### **2. Email Content Test**
- Verify email arrives in inbox (check spam folder)
- Check email formatting and content
- Test portfolio link functionality
- Verify statistics are accurate

### **3. Portfolio Viewer Test**
1. Click link in email
2. Verify portfolio loads correctly
3. Check project display
4. Test responsive design

## 🐛 Troubleshooting

### **Common Issues**

#### **Email Not Sending**
- ✅ Check SMTP credentials in `.env.local`
- ✅ Verify Gmail app password is correct
- ✅ Check console for error messages
- ✅ Test with different email provider

#### **Email Goes to Spam**
- ✅ Use proper "From" name and email
- ✅ Include unsubscribe link (optional)
- ✅ Avoid spam trigger words
- ✅ Test with different email providers

#### **Portfolio Link Not Working**
- ✅ Check token generation in API
- ✅ Verify NEXT_PUBLIC_SITE_URL is correct
- ✅ Test token decoding logic
- ✅ Check 30-day expiration

#### **Missing Projects in Email**
- ✅ Verify user has created content
- ✅ Check content type filtering
- ✅ Test with sample data
- ✅ Check database queries

### **Debug Steps**
1. **Check Browser Console**: Look for JavaScript errors
2. **Check Server Logs**: Verify API calls and responses
3. **Test Email Manually**: Use email testing tools
4. **Verify Environment**: Check all env variables are set
5. **Database Check**: Confirm user content exists

## 📊 Email Analytics (Optional)

### **Track Email Opens**
```javascript
// Add to email template
<img src="${process.env.NEXT_PUBLIC_SITE_URL}/api/track/email-open/${shareToken}" 
     width="1" height="1" style="display:none;" />
```

### **Track Link Clicks**
```javascript
// Modify portfolio URL
const portfolioUrl = `${process.env.NEXT_PUBLIC_SITE_URL}/shared/portfolio/${shareToken}?utm_source=email&utm_medium=share`;
```

## 🚀 Production Considerations

### **Email Service Providers**
For production, consider using:
- **SendGrid**: Reliable, good deliverability
- **Mailgun**: Developer-friendly
- **Amazon SES**: Cost-effective
- **Postmark**: Transactional email specialist

### **Rate Limiting**
```javascript
// Add rate limiting to prevent spam
const rateLimit = {
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5 // limit each user to 5 emails per windowMs
};
```

### **Email Templates**
- Store templates in database for easy updates
- Support multiple languages
- A/B test different designs
- Include branding customization

## 📧 Example Email Output

When a child shares their portfolio, parents receive an email like this:

```
Subject: [Child's Name]'s Creative Portfolio from Little Spark

🎨 [Child's Name]'s Creative Portfolio
Shared from Little Spark - Where Creativity Comes to Life!

Personal Message:
"Hi Mom/Dad! Check out my creative projects..."

📊 Creative Achievements
Stories: 5    Artwork: 3    Music: 2    Total: 10

🎯 Recent Projects
- The Magical Forest Adventure (Story)
- Rainbow Dragon (Artwork)
- Happy Sunshine Song (Music)

[View Full Portfolio Button]

About Little Spark:
Little Spark is a creative platform where children can write stories...
```

The email is fully responsive and looks great on both desktop and mobile devices!

## ✨ Next Steps

1. **Set up email credentials** in `.env.local`
2. **Test the feature** with a real email
3. **Customize email template** if needed
4. **Add rate limiting** for production
5. **Monitor email deliverability**

The email sharing feature is now fully functional and ready to use! 🎉
