import { SubscriptionPlan } from "@/components/subscription/SubscriptionPlans";

// Helper type to handle both the simplified plan structure from useCheckout 
// and the full SubscriptionPlan type
type SimplifiedPlan = {
  planId: string;
  name: string;
  price: string;
  billingInfo?: string;
  savings?: string;
  popular?: boolean;
};

export const getPlanDetails = (selectedPlanId: string | null, plans: SimplifiedPlan[] | SubscriptionPlan[]) => {
  const selectedPlan = plans.find(plan => plan.planId === selectedPlanId);
  if (!selectedPlan) return {
    planName: '',
    monthlyPrice: '',
    billingInfo: ''
  };

  const monthlyPrice = selectedPlan.planId === 'annual-tier'
    ? '9.99'
    : selectedPlan.planId === 'quarterly-tier'
    ? '11.99'
    : '14.99';

  return {
    planName: selectedPlan.name,
    monthlyPrice,
    billingInfo: selectedPlan.planId === 'annual-tier' 
      ? 'Billed annually at $119.99'
      : selectedPlan.planId === 'quarterly-tier'
      ? 'Billed every three months at $35.97'
      : 'Billed monthly'
  };
}; 