import React from "react";

interface BadgeProps {
    children: React.ReactNode;
    variant?: "default" | "secondary" | "destructive" | "outline";
    className?: string;
}

export const Badge: React.FC<BadgeProps> = ({
    children,
    variant = "default",
    className = "",
}) => {
    const baseClasses =
        "inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none";

    const variantClasses = {
        default: "bg-gray-900 text-gray-50 hover:bg-gray-900/80",
        secondary: "bg-gray-100 text-gray-900 hover:bg-gray-100/80",
        destructive: "bg-red-500 text-gray-50 hover:bg-red-500/80",
        outline:
            "text-gray-950 border border-gray-200 bg-transparent hover:bg-gray-100",
    };

    const classes = `${baseClasses} ${variantClasses[variant]} ${className}`;

    return <div className={classes}>{children}</div>;
};
