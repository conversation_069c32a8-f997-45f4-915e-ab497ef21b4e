import type { <PERSON><PERSON><PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "next/font/google";
import { Toaster } from "sonner";
import "./globals.css";

const geistSans = Geist({
    variable: "--font-geist-sans",
    subsets: ["latin"],
});

const geistMono = Geist_Mono({
    variable: "--font-geist-mono",
    subsets: ["latin"],
});

const nunito = <PERSON><PERSON><PERSON>({
    variable: "--font-nunito",
    subsets: ["latin"],
});

const fredoka = Fredoka({
    variable: "--font-fredoka",
    subsets: ["latin"],
});

export const metadata: Metadata = {
    title: "Little Spark",
    description: "Welcome to Little Spark",
    icons: {
        icon: "https://littlespark.ai/lovable-uploads/6d6fc8b3-a2da-4b21-b6d5-d2860c0a9d5c.png",
    },
};

export default function RootLayout({
    children,
}: Readonly<{
    children: React.ReactNode;
}>) {
    return (
        <html lang="en">
            <body
                className={`${geistSans.variable} ${geistMono.variable} ${nunito.variable} ${fredoka.variable} antialiased bg-white text-gray-900`}
            >
                {children}
                <Toaster />
            </body>
        </html>
    );
}
