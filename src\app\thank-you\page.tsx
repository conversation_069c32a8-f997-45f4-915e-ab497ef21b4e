"use client";

import React from "react";
import Link from "next/link";
import { CheckCircle } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";

const ThankYou = () => {
    return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
            <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
                <div className="mb-6">
                    <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
                    <h1 className="text-2xl font-bold text-gray-900 mb-2">
                        Thank You!
                    </h1>
                    <p className="text-gray-600">
                        Your subscription to Little Spark has been successfully
                        activated.
                    </p>
                </div>

                <div className="space-y-4">
                    <p className="text-sm text-gray-500">
                        You should receive a confirmation email shortly with
                        your account details.
                    </p>

                    <div className="pt-4">
                        <Link href="/dashboard">
                            <Button className="w-full bg-littlespark-primary hover:bg-littlespark-primary-hover">
                                Go to Dashboard
                            </Button>
                        </Link>
                    </div>

                    <div className="pt-2">
                        <Link
                            href="/"
                            className="text-sm text-gray-500 hover:text-gray-700"
                        >
                            Return to Home
                        </Link>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ThankYou;
