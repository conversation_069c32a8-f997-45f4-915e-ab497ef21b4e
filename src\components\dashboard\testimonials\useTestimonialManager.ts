import { useState } from 'react';
import { toast } from 'sonner';
import { useTestimonialForm } from './hooks/useTestimonialForm';
import { submitTestimonial } from './api/testimonialService';

export const useTestimonialManager = () => {
  const [showAddDialog, setShowAddDialog] = useState(false);
  const { testimonialData, updateField, resetForm } = useTestimonialForm();
  
  // Note: isAdmin may not be available in the hooks version, defaulting to false
  const isAdmin = false; // TODO: Implement admin check if needed
  
  const addTestimonial = async () => {
    if (!testimonialData.content || !testimonialData.author) {
      toast.error('Please fill in all required fields');
      return;
    }
    
    const success = await submitTestimonial(testimonialData, isAdmin);
    
    if (success) {
      setShowAddDialog(false);
      resetForm();
    }
  };

  return {
    showAddDialog,
    setShowAddDialog,
    newTestimonial: testimonialData,
    setNewTestimonial: update<PERSON>ield,
    isAdmin,
    addTestimonial
  };
};
