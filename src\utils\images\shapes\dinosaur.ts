
/**
 * Draws a dinosaur shape on the canvas
 */
export const drawDinosaurShape = (ctx: CanvasRenderingContext2D, x: number, y: number, size: number) => {
  // Head
  ctx.ellipse(x + size*0.7, y - size*0.3, size*0.25, size*0.18, 0, 0, 2 * Math.PI);
  
  // Neck
  ctx.moveTo(x + size*0.5, y - size*0.1);
  ctx.quadraticCurveTo(
    x + size*0.6, y - size*0.3,
    x + size*0.45, y - size*0.3
  );
  
  // Body
  ctx.ellipse(x, y, size*0.6, size*0.4, 0, 0, 2 * Math.PI);
  
  // Tail
  ctx.moveTo(x - size*0.6, y);
  ctx.quadraticCurveTo(
    x - size*1.0, y + size*0.2,
    x - size*1.2, y + size*0.1
  );
  
  // Legs
  ctx.moveTo(x - size*0.3, y + size*0.3);
  ctx.lineTo(x - size*0.3, y + size*0.7);
  
  ctx.moveTo(x + size*0.2, y + size*0.3);
  ctx.lineTo(x + size*0.2, y + size*0.7);
  
  // Back spikes
  for (let i = 0; i < 5; i++) {
    ctx.moveTo(x - size*0.3 + i*size*0.15, y - size*0.4);
    ctx.lineTo(x - size*0.2 + i*size*0.15, y - size*0.6);
    ctx.lineTo(x - size*0.1 + i*size*0.15, y - size*0.4);
  }
  
  // Eye
  ctx.moveTo(x + size*0.75, y - size*0.35);
  ctx.arc(x + size*0.75, y - size*0.35, size*0.05, 0, 2 * Math.PI);
};
