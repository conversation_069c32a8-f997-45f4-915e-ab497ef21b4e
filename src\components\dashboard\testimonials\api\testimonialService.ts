import { supabase } from '@/lib/supabase/client';
import { toast } from 'sonner';
import { TestimonialFormData } from '../types';

/**
 * Submits a new testimonial to the database
 */
export const submitTestimonial = async (
  testimonialData: TestimonialFormData,
  isAdmin: boolean
): Promise<boolean> => {
  try {
    const avatarUrl = `https://ui-avatars.com/api/?name=${encodeURIComponent(testimonialData.author)}&background=random`;
    
    // Save to the database
    const { error } = await supabase
      .from('testimonials')
      .insert({
        content: testimonialData.content,
        author: testimonialData.author,
        role: testimonialData.role || 'LittleSpark User',
        rating: testimonialData.rating,
        avatar_url: avatarUrl,
        approved: isAdmin
      });
        
    if (error) {
      console.error('Error submitting feedback:', error);
      toast.error('Could not submit your feedback. Please try again later.');
      return false;
    }
    
    toast.success('Thank you for your feedback!');
    return true;
  } catch (error) {
    console.error('Error adding testimonial:', error);
    toast.error('Could not submit your feedback. Please try again later.');
    return false;
  }
};
