import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { useAuth } from "@/hooks/useAuth";
import { Star, LogOut } from "lucide-react";

const DashboardHeader = () => {
    const router = useRouter();
    const { user, signOut } = useAuth();

    // Get the user's first name from metadata if available
    const firstName = user?.user_metadata?.first_name;

    const handleSignOut = async () => {
        await signOut();
        router.push("/auth");
    };

    return (
        <div className="flex flex-col gap-4 justify-center items-center text-center mb-6">
            <div className="text-center w-full">
                <h1 className="font-fredoka text-2xl sm:text-3xl font-bold text-gray-800">
                    {firstName ? `Welcome, ${firstName}!` : "Welcome!"}
                </h1>
                <p className="text-gray-600 mt-2 font-nunito text-sm sm:text-base">
                    Let&apos;s create something amazing today.
                </p>
            </div>
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 w-full sm:w-auto">
                <Button
                    className="bg-orange-500 hover:bg-orange-600 text-white font-nunito min-h-[44px] w-full sm:w-auto"
                    onClick={() => router.push("/my-projects")}
                >
                    <Star className="mr-2 h-4 w-4" />
                    My Projects
                </Button>
                <Button
                    variant="outline"
                    className="border-gray-300 text-gray-700 hover:bg-gray-50 font-nunito min-h-[44px] w-full sm:w-auto"
                    onClick={handleSignOut}
                >
                    <LogOut className="mr-2 h-4 w-4" />
                    Sign Out
                </Button>
            </div>
        </div>
    );
};

export default DashboardHeader;
