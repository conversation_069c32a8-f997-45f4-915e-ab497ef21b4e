// Test script to verify subscription status synchronization
// Run with: node scripts/test-subscription-status.js

const BASE_URL = process.env.BASE_URL || 'http://localhost:3000';

async function checkSubscriptionSync() {
  console.log('🧪 Testing Subscription Status Synchronization');
  console.log('==============================================');
  console.log(`Base URL: ${BASE_URL}\n`);

  try {
    // 1. Check accounts needing sync
    console.log('📊 Step 1: Checking accounts that need synchronization...');
    const checkResponse = await fetch(`${BASE_URL}/api/admin/fix-subscription-sync`);
    const checkData = await checkResponse.json();
    
    console.log('✅ Check completed');
    console.log(`Accounts needing fix: ${checkData.needsFixing || 0}`);
    console.log(`Trialing with payments: ${checkData.trialingWithPayments || 0}`);
    console.log(`Incomplete with payments: ${checkData.incompleteWithPayments || 0}`);
    
    if (checkData.accounts && checkData.accounts.length > 0) {
      console.log('\n📋 Accounts with mismatches:');
      checkData.accounts.forEach((account, index) => {
        console.log(`  ${index + 1}. ${account.email} - Status: ${account.currentStatus}, Payments: ${account.paymentCount}`);
      });
    }

    // 2. If there are accounts to fix, apply the fix
    if (checkData.needsFixing > 0) {
      console.log('\n🔄 Step 2: Applying synchronization fixes...');
      
      const fixResponse = await fetch(`${BASE_URL}/api/admin/fix-subscription-sync`, {
        method: 'POST'
      });
      const fixData = await fixResponse.json();
      
      if (fixData.success) {
        console.log('✅ Synchronization completed successfully!');
        console.log(`Fixed accounts: ${fixData.summary.accountsFixed}`);
        console.log(`Trialing → Active: ${fixData.summary.trialingFixed}`);
        console.log(`Incomplete → Active: ${fixData.summary.incompleteFixed}`);
        
        if (fixData.fixedAccounts && fixData.fixedAccounts.length > 0) {
          console.log('\n📋 Fixed accounts:');
          fixData.fixedAccounts.forEach((account, index) => {
            console.log(`  ${index + 1}. ${account.email}: ${account.previousStatus} → ${account.newStatus} ($${account.latestPaymentAmount})`);
          });
        }
      } else {
        console.log('❌ Synchronization failed:', fixData.error);
      }
    } else {
      console.log('\n✅ No synchronization needed - all accounts are properly synced');
    }

    // 3. Verify the fix by checking again
    console.log('\n🔍 Step 3: Verifying synchronization...');
    const verifyResponse = await fetch(`${BASE_URL}/api/admin/fix-subscription-sync`);
    const verifyData = await verifyResponse.json();
    
    if (verifyData.needsFixing === 0) {
      console.log('✅ Verification passed - all accounts are now synchronized');
    } else {
      console.log(`⚠️  Verification found ${verifyData.needsFixing} accounts still needing sync`);
    }

    // 4. Test subscription status API for a few accounts
    console.log('\n🧪 Step 4: Testing subscription status API...');
    
    // This would require authentication tokens in a real scenario
    console.log('ℹ️  To test individual user subscription status:');
    console.log('   GET /api/subscription/status (requires authentication)');
    console.log('   This should now return "active" status for users with successful payments');

    console.log('\n🎉 Test completed successfully!');
    console.log('\n💡 Next steps:');
    console.log('   1. Have users refresh their subscription page');
    console.log('   2. Verify UI shows "active" status instead of "trialing"');
    console.log('   3. Test that users can access protected features');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('   1. Ensure the server is running');
    console.log('   2. Check the BASE_URL is correct');
    console.log('   3. Verify database connectivity');
  }
}

// Run the test
checkSubscriptionSync();
