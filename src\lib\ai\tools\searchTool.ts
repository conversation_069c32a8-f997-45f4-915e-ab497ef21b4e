import axios from 'axios';

export interface SearchResult {
  title: string;
  link: string;
  snippet: string;
}

interface SerperResult {
  title: string;
  link: string;
  snippet: string;
  [key: string]: unknown;
}

/**
 * Google Search Tool for retrieving real-time information from the web
 * 
 * To configure:
 * 1. Create or update your .env.local file with:
 *    GOOGLE_SEARCH_API_KEY=your_serper_api_key
 * 2. Restart the development server
 */
export class GoogleSearchTool {
  private apiKey: string;

  constructor(apiKey?: string) {
    // Use provided API key, or environment variable, or throw an error if neither exists
    this.apiKey = apiKey || process.env.GOOGLE_SEARCH_API_KEY || '';
    
    if (!this.apiKey) {
      console.error('No Google Search API key provided. Search functionality will not work.');
    }
  }

  async search(query: string): Promise<SearchResult[]> {
    try {
      if (!this.apiKey) {
        console.error('Search aborted: No API key available');
        return [];
      }

      const response = await axios.post(
        'https://google.serper.dev/search',
        { q: query },
        {
          headers: {
            'X-API-KEY': this.apiKey,
            'Content-Type': 'application/json'
          }
        }
      );

      const results = response.data.organic || [];
      return results.slice(0, 3).map((result: SerperResult) => ({
        title: result.title,
        link: result.link,
        snippet: result.snippet
      }));
    } catch (error) {
      console.error('Search error:', error);
      return [];
    }
  }

  async searchForAnswer(query: string): Promise<string> {
    try {
      const results = await this.search(query);
      
      if (results.length === 0) {
        return "I couldn't find current information about that.";
      }

      // Combine search results into a summary
      const summary = results
        .map(result => `${result.title}: ${result.snippet}`)
        .join('\n\n');

      return summary;
    } catch (error) {
      console.error('Search for answer error:', error);
      return "I couldn't find current information about that.";
    }
  }
} 