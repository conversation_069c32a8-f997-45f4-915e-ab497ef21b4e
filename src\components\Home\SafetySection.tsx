import React, { useEffect, useRef } from "react";
import <PERSON> from "next/link";
import { Lock, ArrowRight } from "lucide-react";
import {
    safetyFeatures,
    privacyCommitments,
    safetyContent,
} from "@/lib/constants";
import { fredoka } from "@/lib/fonts";

const SafetySection = () => {
    // Ref for the cards container to observe for intersection
    const cardsRef = useRef(null);
    const privacyBoxRef = useRef(null);

    // Add scroll animation effect
    useEffect(() => {
        const observer = new IntersectionObserver(
            (entries) => {
                entries.forEach((entry) => {
                    // When cards container comes into view
                    if (entry.isIntersecting) {
                        // Get all safety cards
                        const cards = document.querySelectorAll(".safety-card");

                        // Add animation with staggered delay to each card
                        cards.forEach((card, index) => {
                            setTimeout(() => {
                                card.classList.add("animate-fade-in");
                                card.classList.remove("opacity-0");
                            }, index * 200); // 200ms delay between each card animation
                        });
                    }
                });
            },
            { threshold: 0.1 } // Trigger when at least 10% of the element is visible
        );

        // Observer for the privacy commitment box
        const privacyObserver = new IntersectionObserver(
            (entries) => {
                entries.forEach((entry) => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add("animate-fade-in");
                        entry.target.classList.remove("opacity-0");
                    }
                });
            },
            { threshold: 0.1 }
        );

        // Copy ref values to variables to avoid stale closure in cleanup
        const currentCardsRef = cardsRef.current;
        const currentPrivacyBoxRef = privacyBoxRef.current;

        // Start observing the cards container
        if (currentCardsRef) {
            observer.observe(currentCardsRef);
        }

        // Start observing the privacy box
        if (currentPrivacyBoxRef) {
            privacyObserver.observe(currentPrivacyBoxRef);
        }

        // Cleanup observer on component unmount
        return () => {
            if (currentCardsRef) {
                observer.unobserve(currentCardsRef);
            }
            if (currentPrivacyBoxRef) {
                privacyObserver.unobserve(currentPrivacyBoxRef);
            }
        };
    }, []);

    return (
        <section className="py-16 relative overflow-hidden">
            {/* Background pattern */}
            <div className="absolute inset-0 bg-gradient-to-b from-white via-gray-50 to-white"></div>
            <div className="absolute left-0 right-0 top-0 h-32 bg-gradient-to-b from-white to-transparent z-10"></div>
            <div className="absolute left-0 right-0 bottom-0 h-32 bg-gradient-to-t from-white to-transparent z-10"></div>

            {/* Background blobs */}
            <div className="absolute top-1/4 left-0 w-72 h-72 bg-littlespark-yellow/20 rounded-full blur-3xl"></div>
            <div className="absolute bottom-1/4 right-0 w-96 h-96 bg-littlespark-blue/15 rounded-full blur-3xl"></div>

            <div className="container mx-auto px-4 relative z-20">
                <div className="text-center mb-20">
                    <h2
                        className={`text-3xl text-gray-900 md:text-4xl font-bold mb-6 ${fredoka.className}`}
                    >
                        {safetyContent.title}
                    </h2>
                    <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                        {safetyContent.subtitle}
                    </p>
                </div>

                <div
                    className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-5xl mx-auto"
                    ref={cardsRef}
                >
                    {safetyFeatures.map((feature) => {
                        const Icon = feature.icon;
                        return (
                            <div
                                key={feature.title}
                                className="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl border border-gray-100 group safety-card opacity-0 transform translate-y-8
                         hover:scale-105 hover:-translate-y-2 transition-all duration-300"
                                style={{
                                    boxShadow: `0 10px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)`,
                                }}
                            >
                                <div
                                    className="mb-6 w-14 h-14 rounded-xl flex items-center justify-center shadow-md 
                              transform transition-transform group-hover:scale-110 group-hover:rotate-3 bg-white"
                                >
                                    <Icon
                                        className={`h-7 w-7 text-${feature.color}`}
                                    />
                                </div>
                                <h3
                                    className={`text-xl text-gray-900 mb-3 font-bold ${fredoka.className}`}
                                >
                                    {feature.title}
                                </h3>
                                <p className="text-gray-600">
                                    {feature.description}
                                </p>
                            </div>
                        );
                    })}
                </div>

                <div
                    className="mt-16 bg-white p-8 rounded-2xl shadow-lg max-w-3xl mx-auto border border-gray-100 opacity-0 transform translate-y-8"
                    ref={privacyBoxRef}
                >
                    <h3
                        className={`text-xl font-bold mb-6 flex items-center text-gray-800 ${fredoka.className}`}
                    >
                        <Lock className="h-5 w-5 mr-2 text-littlespark-pink" />
                        {safetyContent.privacyTitle}
                    </h3>
                    <ul className="space-y-3">
                        {privacyCommitments.map((commitment, index) => (
                            <li key={index} className="flex items-start">
                                <div className="flex-shrink-0 h-6 w-6 rounded-full bg-littlespark-pink/10 flex items-center justify-center mr-3 mt-0.5">
                                    <span className="text-littlespark-pink font-semibold">
                                        ✓
                                    </span>
                                </div>
                                <span className="text-gray-600">
                                    {commitment}
                                </span>
                            </li>
                        ))}
                    </ul>
                    <div className="mt-8 text-center">
                        <Link
                            href="/privacy"
                            className="inline-flex items-center px-8 py-3 border border-gray-300 text-base font-medium rounded-full 
                           text-gray-700 bg-white hover:bg-gray-50 hover:border-gray-400 transition-all duration-300 group"
                        >
                            {safetyContent.privacyButton}
                            <ArrowRight className="ml-1 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                        </Link>
                    </div>
                </div>
            </div>
        </section>
    );
};

export default SafetySection;
