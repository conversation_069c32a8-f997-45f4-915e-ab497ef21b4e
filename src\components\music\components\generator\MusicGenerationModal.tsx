import React, { useEffect, useState } from 'react';
import { Music, Clock, Sparkles } from 'lucide-react';

interface MusicGenerationModalProps {
  isOpen: boolean;
  onClose?: () => void; // Optional since we don't use it during generation
}

const MusicGenerationModal: React.FC<MusicGenerationModalProps> = ({ isOpen }) => {
  const [timeElapsed, setTimeElapsed] = useState(0);

  useEffect(() => {
    if (!isOpen) {
      setTimeElapsed(0);
      return;
    }

    const interval = setInterval(() => {
      setTimeElapsed(prev => prev + 1);
    }, 1000);

    return () => clearInterval(interval);
  }, [isOpen]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center z-20 p-4 backdrop-blur-xs bg-white/20">
      <div className="bg-white rounded-lg p-8 max-w-md w-full mx-4 text-center shadow-2xl border border-gray-100">
        {/* Animated Music Icon */}
        <div className="relative mb-6">
          <div className="absolute inset-0 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full animate-pulse opacity-20"></div>
          <Music className="h-16 w-16 text-purple-600 mx-auto animate-bounce" />
        </div>

        {/* Title */}
        <h3 className="text-2xl font-bold text-gray-800 mb-2">
          Creating Your Music
        </h3>

        {/* Description */}
        <p className="text-gray-600 mb-6">
          Our AI is composing your unique piece. This process typically takes 30 seconds to 1 minute.
        </p>

        {/* Progress Indicators */}
        <div className="space-y-4 mb-6">
          <div className="flex items-center justify-center space-x-2 text-sm text-gray-500">
            <Clock className="h-4 w-4" />
            <span>Time elapsed: {formatTime(timeElapsed)}</span>
          </div>

          {/* Animated Progress Bar */}
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div className="bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full animate-pulse" 
                 style={{ width: `${Math.min((timeElapsed / 60) * 100, 100)}%` }}>
            </div>
          </div>

          {/* Status Messages */}
          <div className="text-sm text-gray-600">
            {timeElapsed < 15 && (
              <div className="flex items-center justify-center space-x-2">
                <Sparkles className="h-4 w-4 text-yellow-500" />
                <span>Analyzing your musical preferences...</span>
              </div>
            )}
            {timeElapsed >= 15 && timeElapsed < 30 && (
              <div className="flex items-center justify-center space-x-2">
                <Sparkles className="h-4 w-4 text-blue-500" />
                <span>Composing melodies and harmonies...</span>
              </div>
            )}
            {timeElapsed >= 30 && timeElapsed < 45 && (
              <div className="flex items-center justify-center space-x-2">
                <Sparkles className="h-4 w-4 text-green-500" />
                <span>Adding instruments and effects...</span>
              </div>
            )}
            {timeElapsed >= 45 && (
              <div className="flex items-center justify-center space-x-2">
                <Sparkles className="h-4 w-4 text-purple-500" />
                <span>Finalizing your masterpiece...</span>
              </div>
            )}
          </div>
        </div>

        {/* Tips */}
        <div className="bg-blue-50 rounded-lg p-4 text-left">
          <h4 className="font-semibold text-blue-900 mb-2">💡 While you wait:</h4>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Your music will be unique and original</li>
            <li>• You can save it to your portfolio when ready</li>
            <li>• Try different styles and moods for variety</li>
          </ul>
        </div>

        {/* Note */}
        <p className="text-xs text-gray-500 mt-4">
          Please keep this tab open while your music is being generated.
        </p>
      </div>
    </div>
  );
};

export default MusicGenerationModal;
