-- Seed challenges table with initial data
-- Clear existing challenges first
DELETE FROM challenges;

-- Insert initial challenges with 1 month expiry
INSERT INTO challenges (id, title, description, difficulty, type, prompt, is_active, created_by, valid_until) VALUES
('1', 'Magical Forest Story', 'Write a short story about a child who discovers a magical forest.', 'easy', 'story', 'Once upon a time, in a forest unlike any other...', true, 'system', '2025-08-14 23:59:59'),
('2', 'Space Explorer Portrait', 'Draw your own space explorer character visiting a new planet.', 'medium', 'art', 'Imagine a brave explorer discovering a colorful new world...', true, 'system', '2025-08-14 23:59:59'),
('3', 'Heroes Journey Music', 'Compose a short melody that represents a hero setting off on an adventure.', 'medium', 'music', 'Think of the excitement and determination of beginning a quest...', true, 'system', '2025-08-14 23:59:59'),
('4', 'Puzzle Game Design', 'Design a simple puzzle game with at least three levels.', 'hard', 'game', 'Create puzzles that get progressively more challenging...', true, 'system', '2025-08-14 23:59:59'),
('weekly-robot', 'Creative Challenge of the Week', 'First create a story about what the robot does, then draw it in the Art Studio. What special powers does it have?', 'medium', 'story', 'Imagine a friendly robot with amazing abilities...', true, 'system', '2025-08-14 23:59:59'),
('ocean-adventure', 'Ocean Adventure Story', 'Write about a underwater adventure with sea creatures.', 'easy', 'story', 'Deep beneath the ocean waves, something magical awaits...', true, 'system', '2025-08-14 23:59:59'),
('dragon-friend', 'Friendly Dragon Art', 'Create artwork of a kind dragon who helps people.', 'easy', 'art', 'A gentle dragon with sparkling scales and a warm smile...', true, 'system', '2025-08-14 23:59:59'),
('celebration-song', 'Celebration Music', 'Compose a joyful song for a special celebration.', 'easy', 'music', 'Music that makes everyone want to dance and smile...', true, 'system', '2025-08-14 23:59:59');
