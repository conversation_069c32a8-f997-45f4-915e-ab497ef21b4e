import React from 'react';
import { useRouter } from 'next/navigation';
import { ArrowRight } from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { ctaContent } from '@/lib/constants';
import { fredoka } from '@/lib/fonts';

const CTASection = () => {
  const router = useRouter();
  const { user } = useAuth();

  const handleGetStarted = () => {
    if (user) {
      router.push('/dashboard');
    } else {
      router.push('/checkout');
    }
  };

  const handleSeePricing = () => {
    router.push('/pricing');
  };

  return (
    <section className="py-28 relative overflow-hidden">
      {/* Background gradient - updated to match brand guidelines with teal */}
      <div className="absolute inset-0 bg-gradient-to-r from-littlespark-teal to-littlespark-blue opacity-90"></div>
      
      {/* Background blobs */}
      <div className="absolute -top-24 -left-24 w-96 h-96 bg-white/10 rounded-full blur-3xl"></div>
      <div className="absolute -bottom-24 -right-24 w-96 h-96 bg-white/10 rounded-full blur-3xl"></div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-3xl mx-auto text-center">
          <h2 className={`text-3xl md:text-4xl font-bold mb-6 text-white ${fredoka.className} leading-tight`}>
            {ctaContent.title}
          </h2>
          <p className="text-xl mb-10 text-white/90 max-w-2xl mx-auto">
            {ctaContent.subtitle}
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button 
              className="group bg-white text-littlespark-teal hover:bg-littlespark-yellow hover:text-gray-800 
                       px-8 py-4 rounded-full font-semibold transition-all duration-300 
                       hover:scale-105 hover:shadow-lg flex items-center justify-center"
              onClick={handleGetStarted}
            >
              {ctaContent.buttons.primary}
              <ArrowRight className="ml-1 h-5 w-5 group-hover:translate-x-1 transition-transform" />
            </button>
            <button 
              className="border-2 border-white text-white hover:bg-white/20 
                       px-8 py-4 rounded-full font-semibold transition-all duration-300
                       hover:scale-105 flex items-center justify-center"
              onClick={handleSeePricing}
            >
              {ctaContent.buttons.secondary}
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CTASection; 