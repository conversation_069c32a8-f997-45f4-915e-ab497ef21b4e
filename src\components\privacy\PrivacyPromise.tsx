import React from "react";
import { Shield } from "lucide-react";
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import { fredoka } from "@/lib/fonts";

const PrivacyPromise = () => {
    return (
        <Card className="mb-8 text-gray-900">
            <CardHeader>
                <CardTitle
                    className={`flex items-center gap-2 ${fredoka.className}`}
                >
                    <Shield className="h-6 w-6 text-littlespark-lavender" />
                    Our Privacy Promise
                </CardTitle>
                <CardDescription className="text-left">
                    Little Spark was built with privacy as a fundamental
                    principle
                </CardDescription>
            </CardHeader>
            <CardContent className="text-left">
                <p className="mb-4">
                    We understand that parents have legitimate concerns about
                    their children&apos;s privacy online. That&apos;s why
                    we&apos;ve built Little Spark to be private-by-design,
                    ensuring that your child&apos;s creative work stays under
                    your control.
                </p>

                <h3
                    className={`text-lg font-semibold mt-6 mb-2 ${fredoka.className}`}
                >
                    How We Use AI Services
                </h3>
                <p className="mb-4">
                    Little Spark uses AI services from OpenAI and Runware to
                    power our creative tools:
                </p>
                <ul className="list-disc pl-6 mb-4 space-y-2">
                    <li>
                        <strong>Story Generation:</strong> When your child
                        creates stories, we send their prompts to OpenAI&apos;s
                        API to generate age-appropriate, creative content.
                    </li>
                    <li>
                        <strong>Art & Coloring Pages:</strong> Art prompts and
                        requests are processed by OpenAI&apos;s DALL-E model to
                        create custom illustrations and coloring pages.
                    </li>
                    <li>
                        <strong>Video Creation:</strong> When creating videos,
                        we use Runware&apos;s API to transform stories and
                        images into animated content.
                    </li>
                </ul>

                <h3
                    className={`text-lg font-semibold mt-6 mb-2 ${fredoka.className}`}
                >
                    Your Data Protection Commitment
                </h3>
                <p className="mb-4">
                    While we do send content to these AI services to create
                    personalized experiences, we maintain strict data protection
                    practices:
                </p>
                <ul className="list-disc pl-6 mb-4 space-y-2">
                    <li>
                        We explicitly configure these AI systems to prevent your
                        child&apos;s uploads and chat history from being used
                        for AI training purposes.
                    </li>
                    <li>
                        Your data is never shared with third parties beyond our
                        essential AI service providers.
                    </li>
                    <li>
                        All data transmission uses secure, encrypted
                        connections.
                    </li>
                    <li>
                        We retain only the minimum data necessary to provide our
                        services.
                    </li>
                    <li>
                        You maintain full control of your creations with direct
                        downloads.
                    </li>
                </ul>
            </CardContent>
        </Card>
    );
};

export default PrivacyPromise;
