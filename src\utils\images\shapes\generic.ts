
/**
 * Draws a generic coloring shape on the canvas
 */
export const drawGenericColoringShape = (ctx: CanvasRenderingContext2D, x: number, y: number, size: number) => {
  ctx.moveTo(x + size * Math.cos(0), y + size * Math.sin(0));
  
  for (let i = 1; i <= 10; i++) {
    const angle = (i / 5) * Math.PI;
    const radius = size * (0.8 + Math.random() * 0.4);
    const newX = x + radius * Math.cos(angle);
    const newY = y + radius * Math.sin(angle);
    ctx.lineTo(newX, newY);
  }
  
  ctx.closePath();
};
