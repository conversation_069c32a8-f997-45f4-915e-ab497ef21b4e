'use client';

import { useEffect } from 'react';
import Lenis from 'lenis';

const SmoothScrolling = () => {
  useEffect(() => {
    // Initialize Lenis
    const lenis = new Lenis({
      duration: 1.2,
      easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t)), // https://www.desmos.com/calculator/brs54l4xou
      orientation: 'vertical',
      gestureOrientation: 'vertical',
      smoothWheel: true,
      wheelMultiplier: 1,
      touchMultiplier: 2,
      infinite: false,
    });

    // Get scroll value and dispatch custom event
    lenis.on('scroll', (e) => {
      // Dispatch custom event for other components to listen to
      window.dispatchEvent(new CustomEvent('lenisScroll', { 
        detail: { scroll: e.scroll } 
      }));
    });

    function raf(time: number) {
      lenis.raf(time);
      requestAnimationFrame(raf);
    }

    requestAnimationFrame(raf);

    // Store lenis instance globally if needed
    (window as typeof window & { lenis?: Lenis }).lenis = lenis;

    // Cleanup
    return () => {
      lenis.destroy();
      const windowWithLenis = window as typeof window & { lenis?: Lenis };
      delete windowWithLenis.lenis;
    };
  }, []);

  return null; // This component doesn't render anything
};

export default SmoothScrolling; 