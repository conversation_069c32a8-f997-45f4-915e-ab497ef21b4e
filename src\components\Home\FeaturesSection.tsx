import React from 'react';
import { fredoka, nunito } from '@/lib/fonts';
import { featuresContent, firstRowFeatures, secondRowFeatures, thirdRowFeatures } from '@/lib/constants';
import FeatureCard from '@/components/FeatureCard';

const FeaturesSection = () => {
  return (
    <section className="py-12 sm:py-16 lg:py-20 bg-white" id="features">
      <div className="container mx-auto px-4 max-w-6xl">
        <div className="text-center mb-12 sm:mb-16 opacity-0 animate-fade-in-up [animation-delay:0.1s]">
          <h2 className={`text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-4 text-gray-800 ${fredoka.className}`}>
            {featuresContent.title}
          </h2>
          <p className={`text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto ${nunito.className}`}>
            {featuresContent.subtitle}
          </p>
        </div>
        
        {/* First Row - 3 cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 sm:gap-8 mb-8 sm:mb-10">
          {firstRowFeatures.map((feature) => (
            <FeatureCard
              key={feature.title}
              title={feature.title}
              description={feature.description}
              icon={feature.icon}
              iconColor={feature.iconColor}
              borderColor={feature.borderColor}
              delay={feature.delay}
            />
          ))}
        </div>
        
        {/* Second Row - 3 cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 sm:gap-8 mb-8 sm:mb-10">
          {secondRowFeatures.map((feature) => (
            <FeatureCard
              key={feature.title}
              title={feature.title}
              description={feature.description}
              icon={feature.icon}
              iconColor={feature.iconColor}
              borderColor={feature.borderColor}
              delay={feature.delay}
            />
          ))}
        </div>
        
        {/* Third Row - 2 cards centered */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 sm:gap-8 max-w-4xl mx-auto">
          {thirdRowFeatures.map((feature) => (
            <FeatureCard
              key={feature.title}
              title={feature.title}
              description={feature.description}
              icon={feature.icon}
              iconColor={feature.iconColor}
              borderColor={feature.borderColor}
              delay={feature.delay}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default FeaturesSection; 