import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/components/dashboard/ai-mentor/types';

/**
 * Call the LangGraph AI buddy API for mentor responses
 */
const callAIBuddyAPI = async (params: {
  message: string;
  character: <PERSON><PERSON><PERSON><PERSON><PERSON>;
  characterName: string;
  chatHistory: { role: 'user' | 'ai'; content: string }[];
}): Promise<string> => {
  try {
    const response = await fetch('/api/ai-buddy/chat', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        message: params.message,
        character: params.character,
        characterName: params.characterName,
        chatHistory: params.chatHistory
      })
    });

    if (!response.ok) {
      throw new Error(`API call failed: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data.response || "I'm here to help with your creative projects! What would you like to create today?";
  } catch (error) {
    console.error('Error calling AI buddy API:', error);
    
    // Character-specific fallback responses
    const fallbackResponses = {
      robot: "Oops! My circuits got a bit tangled there. What creative project should we work on together?",
      owl: "Oh my! That was quite puzzling. What would you like to explore today?",
      explorer: "Well, that was an unexpected detour! What adventure should we embark on?"
    };
    
    return fallbackResponses[params.character] || "I'm here to help with your creative projects! What would you like to create today?";
  }
};

/**
 * Generate mentor responses for the AI buddy
 */
export const generateMentorResponse = async (
  userMessage: string, 
  character: MentorCharacter,
  chatHistory: { role: 'user' | 'ai', content: string }[],
  characterName?: string
): Promise<string> => {
  try {
    // Get character names based on character type
    const defaultNames = {
      robot: 'Sparky',
      owl: 'Professor Hootie', 
      explorer: 'Captain Nova'
    };
    
    const finalCharacterName = characterName || defaultNames[character];
    
    // Call the LangGraph AI buddy API
    const response = await callAIBuddyAPI({
      message: userMessage,
      character,
      characterName: finalCharacterName,
      chatHistory
    });
    
    // Additional content safety check
    return filterInappropriateContent(response);
  } catch (error) {
    console.error('Error generating mentor response:', error);
    
    // Character-specific fallback responses
    const fallbacks = {
      robot: [
        "Wow, that sounds interesting! What kind of creative project are you thinking about?",
        "My circuits are buzzing with excitement! Tell me more about your idea!",
        "That's so cool! Want to explore that idea together?"
      ],
      owl: [
        "How wonderful! I'd love to hear more about that!",
        "That's quite fascinating! What sparked that idea?",
        "Oh my! That sounds like a delightful adventure!"
      ],
      explorer: [
        "What an adventure! I'm ready to explore that with you!",
        "That sounds out of this world! Let's go on this creative journey!",
        "Amazing! My explorer instincts are telling me this will be great!"
      ]
    };
    
    const characterFallbacks = fallbacks[character];
    return characterFallbacks[Math.floor(Math.random() * characterFallbacks.length)];
  }
};

/**
 * Additional content filtering to ensure responses are kid-friendly
 */
const filterInappropriateContent = (text: string): string => {
  // List of potentially inappropriate topics to check for
  const inappropriateTopics = [
    /violence/i, /weapon/i, /gun/i, /kill/i, /blood/i, /fight/i, 
    /alcohol/i, /beer/i, /wine/i, /drunk/i, 
    /drug/i, /smoking/i, /cigarette/i, 
    /sex/i, /dating/i, /kiss/i, /romantic/i, /boyfriend/i, /girlfriend/i,
    /swear/i, /curse/i, /profanity/i,
    /horror/i, /scary/i, /nightmare/i,
    /adult/i, /mature/i, /explicit/i
  ];
  
  // Check if the response contains any inappropriate content
  const containsInappropriate = inappropriateTopics.some(topic => topic.test(text));
  
  if (containsInappropriate) {
    // If inappropriate content is detected, provide a safe alternative response
    return "I'd love to help with your creative ideas! Let's focus on fun stories, colorful art projects, or musical compositions. What kind of creative activity interests you the most?";
  }
  
  return text;
};

/**
 * Generate personalized greeting for the selected character
 */
export const generateCharacterGreeting = (character: MentorCharacter, name: string): string => {
  switch (character) {
    case 'robot':
      return `Beep boop! I'm ${name}! Let's create something awesome with technology!`;
    case 'owl':
      return `Hoot! I'm ${name}! I love helping with creative stories and art!`;
    case 'explorer':
      return `Greetings, creative explorer! I'm ${name}, ready for a creative adventure across the stars?`;
    default:
      return `Hi there! I'm ${name}. What would you like to create today?`;
  }
};
