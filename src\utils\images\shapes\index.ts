
import { drawButterflyShape } from './butterfly';
import { drawDinosaurShape } from './dinosaur';
import { drawUnicornShape } from './unicorn';
import { drawRocketShape } from './rocket';
import { drawRobotShape } from './robot';
import { drawCrownShape } from './crown';
import { drawGenericColoringShape } from './generic';
import { drawDragonShape } from './dragon';
import { drawCastleShape } from './castle';
import { drawMermaidShape } from './mermaid';
import { drawPirateShape } from './pirate';
import { drawSuperheroShape } from './superhero';
import { drawForestShape } from './forest';

/**
 * Main function to draw a shape on canvas based on shape type
 */
export const drawShapeOnCanvas = (
  ctx: CanvasRenderingContext2D,
  x: number,
  y: number,
  size: number,
  shapeType: string
): void => {
  ctx.beginPath();
  
  switch (shapeType) {
    case 'butterfly':
      drawButterflyShape(ctx, x, y, size);
      break;
    case 'dinosaur':
      drawDinosaurShape(ctx, x, y, size);
      break;
    case 'unicorn':
      drawUnicornShape(ctx, x, y, size);
      break;
    case 'rocket':
      drawRocketShape(ctx, x, y, size);
      break;
    case 'robot':
      drawRobotShape(ctx, x, y, size);
      break;
    case 'princess':
      drawCrownShape(ctx, x, y, size);
      break;
    case 'dragon':
      drawDragonShape(ctx, x, y, size);
      break;
    case 'castle':
      drawCastleShape(ctx, x, y, size);
      break;
    case 'mermaid':
      drawMermaidShape(ctx, x, y, size);
      break;
    case 'pirate':
      drawPirateShape(ctx, x, y, size);
      break;
    case 'superhero':
      drawSuperheroShape(ctx, x, y, size);
      break;
    case 'forest':
      drawForestShape(ctx, x, y, size);
      break;
    default:
      drawGenericColoringShape(ctx, x, y, size);
  }
  
  ctx.lineWidth = 3;
  ctx.strokeStyle = '#333';
  ctx.stroke();
};

export {
  drawButterflyShape,
  drawDinosaurShape,
  drawUnicornShape,
  drawRocketShape,
  drawRobotShape,
  drawCrownShape,
  drawGenericColoringShape,
  drawDragonShape,
  drawCastleShape,
  drawMermaidShape,
  drawPirateShape,
  drawSuperheroShape,
  drawForestShape
};
