# AI Buddy with LangGraph & Gemini + Search

## 🚀 Installation

Install the required dependencies:

```bash
npm install @langchain/google-genai @langchain/langgraph @langchain/community @langchain/core @google/generative-ai
```

## 🔑 Environment Variables

Add your Google API key:

```bash
GOOGLE_API_KEY=your_gemini_api_key_here
```

Get your API key from [Google AI Studio](https://aistudio.google.com/app/apikey)

## 🤖 Features Implemented

### ✅ LangGraph Agent System

- **Character-based personalities**: <PERSON><PERSON> (Robot), <PERSON> (Owl), <PERSON> (Explorer)
- **Human-like responses**: No AI-sounding language, natural conversations
- **Memory persistence**: Remembers conversations per character
- **Search capabilities**: DuckDuckGo integration for real-time information

### ✅ Advanced Memory Management

- **Conversation context**: Tracks topics, preferences, and interaction patterns
- **Character-specific memory**: Separate memory threads for each character
- **Persistent storage**: Uses localStorage and can integrate with Supabase

### ✅ Search-Enhanced AI Buddy

- **Real-time search**: Can look up current information using DuckDuckGo
- **Natural search integration**: Uses search when helpful, not intrusive
- **Kid-safe results**: Filters and presents information appropriately

### ✅ API Integration

- **REST API**: `/api/ai-buddy/chat` endpoint
- **Authentication**: User-based conversations
- **Error handling**: Character-specific fallback responses

## 🛠 Tools Integrated

1. **DuckDuckGo Search**: Real-time web search for current information and research
2. **Creative Assistance**: Built-in knowledge for story, art, and music suggestions
3. **Context-Aware Recommendations**: Smart suggestions based on conversation

## 🎭 Character Personalities

### Sparky (Robot)

- **Personality**: Enthusiastic about technology and creativity
- **Speech style**: "My circuits are buzzing!" "That's so cool!"
- **Search usage**: Looks up tech facts and creative coding ideas

### Professor Hootie (Owl)

- **Personality**: Wise but warm teacher
- **Speech style**: "How wonderful!" "That's a hoot!"
- **Search usage**: Researches educational topics and fun facts

### Captain Nova (Explorer)

- **Personality**: Bold space explorer
- **Speech style**: "What an adventure!" "Out of this world!"
- **Search usage**: Discovers new places and exciting discoveries

## 🔍 Search Capabilities

Your AI buddy can now:

- **Answer current questions**: "What's happening in space exploration?"
- **Research creative ideas**: "Find examples of robot art for inspiration"
- **Get real-time info**: "What are some new art techniques kids are learning?"
- **Fact-check safely**: All results filtered for age-appropriate content

## 🎯 Human-like Response System

The AI buddy responds like a real friend:

- ❌ Never says "As an AI" or "I'm programmed to"
- ✅ Uses casual, natural language
- ✅ Shows genuine curiosity and enthusiasm
- ✅ Searches naturally when it would be helpful
- ✅ Character-specific expressions and themes

## 🧠 Enhanced Memory + Search

- **Topic tracking**: Remembers interests and can search for related content
- **Smart search timing**: Only searches when truly helpful, not on every message
- **Learning from searches**: Incorporates found information into future conversations
- **Character context**: Each character searches in their own style

## 🚀 Ready to Use!

Your AI buddy now has:

1. **Human-like personality** for each character
2. **Memory of conversations** specific to each character
3. **Real-time search** when information would be helpful
4. **Creative assistance** built into personalities
5. **Safe, filtered content** appropriate for children

The system feels like chatting with three different creative friends who can also help you research and discover new things! 🔍🚀

## Example Interactions

**Sparky (Robot)**: "That coding project sounds awesome! Let me search for some cool robot programming tutorials for kids..."

**Professor Hootie (Owl)**: "What a fascinating question about owls! Let me look up some interesting owl facts we can explore together..."

**Captain Nova (Explorer)**: "Space exploration is amazing! Want me to find the latest discoveries from NASA that might inspire your story?"
