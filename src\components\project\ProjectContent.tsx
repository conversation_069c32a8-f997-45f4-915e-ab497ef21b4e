"use client";

import React from "react";
import { Info } from "lucide-react";
import { useProject } from "./ProjectContext";
import VoiceEnabledInput from "@/components/shared/VoiceEnabledInput";

const ProjectContent = () => {
    const { content, setContent, learningMode, projectType } = useProject();

    const handleVoiceInput = (text: string) => {
        // Add new line if there's existing content
        const newContent = content 
            ? content.trim() + "\n" + text.trim()
            : text.trim();
        setContent(newContent);
    };

    const getPlaceholder = () => {
        switch (projectType) {
            case "story":
                return "Once upon a time...";
            case "art":
                return "Describe your artistic vision...";
            case "music":
                return "Share your musical ideas...";
            default:
                return "Start describing your project...";
        }
    };

    const getLearningTip = () => {
        switch (projectType) {
            case "story":
                return "Remember to include a beginning, middle, and end in your story. Think about characters, setting, and plot.";
            case "art":
                return "Consider colors, shapes, composition, and the mood you want to create in your artwork.";
            case "music":
                return "Think about rhythm, melody, instruments, and the emotions you want to express through music.";
            default:
                return "Break down your project into smaller steps and think about what materials or tools you might need.";
        }
    };

    return (
        <div className="space-y-4">
            <VoiceEnabledInput
                value={content}
                onChange={(e) => setContent(e.target.value)}
                onTextAdded={handleVoiceInput}
                isTextArea={true}
                placeholder={getPlaceholder()}
                className="min-h-[200px] resize-none py-3 px-4 w-full pr-12 rounded-xl border-2 border-gray-200 focus:border-[#00B7FD] focus:ring-2 focus:ring-[#00B7FD]/20 outline-none transition-colors text-lg"
            />
            {learningMode && (
                <div className="flex items-center gap-2 bg-blue-50 p-4 rounded-lg">
                    <Info className="h-5 w-5 text-blue-500" />
                    <div className="text-blue-600">
                        <h4 className="font-semibold text-sm">Project Development</h4>
                        <p className="text-sm">
                            {getLearningTip()}
                        </p>
                    </div>
                </div>
            )}
        </div>
    );
};

export default ProjectContent;
