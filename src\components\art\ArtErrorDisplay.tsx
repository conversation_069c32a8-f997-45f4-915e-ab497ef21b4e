
import React from 'react';
import { <PERSON>ert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { AlertCircle, Info } from 'lucide-react';

interface ArtErrorDisplayProps {
  errorMessage: string | null;
}

const ArtErrorDisplay = ({ errorMessage }: ArtErrorDisplayProps) => {
  if (!errorMessage) return null;
  
  // Check if it's an Edge Function error and provide a helpful message
  const isEdgeFunctionError = errorMessage.includes("Edge Function") || 
                            errorMessage.includes("Failed to send") ||
                            errorMessage.includes("couldn't connect");
  
  const isDevEnvironment = window.location.hostname.includes('localhost') || 
                          window.location.hostname.includes('lovableproject.com');
  
  return (
    <Alert 
      variant={isDevEnvironment && isEdgeFunctionError ? "default" : "destructive"} 
      className="mb-6"
    >
      {isDevEnvironment && isEdgeFunctionError ? (
        <Info className="h-4 w-4" />
      ) : (
        <AlertCircle className="h-4 w-4" />
      )}
      
      <AlertTitle>
        {isDevEnvironment && isEdgeFunctionError 
          ? "Development Mode" 
          : "Error generating art"}
      </AlertTitle>
      <AlertDescription>
        {isEdgeFunctionError ? (
          <>
            <p>We&apos;re having trouble connecting to our art generation service.</p>
            <p className="mt-1 text-sm">
              {isDevEnvironment 
                ? "This is normal in development. The Edge Function will work when deployed to production." 
                : "Don't worry - we're using a placeholder image for now so you can continue creating!"}
            </p>
          </>
        ) : errorMessage}
        
        {errorMessage.includes("inappropriate") && (
          <div className="mt-2 text-xs">
            <p>Tips for creating appropriate content:</p>
            <ul className="list-disc pl-5 mt-1">
              <li>Avoid descriptions of violent or scary scenes</li>
              <li>Use child-friendly language and themes</li>
              <li>Focus on positive, creative imagery</li>
            </ul>
          </div>
        )}
      </AlertDescription>
    </Alert>
  );
};

export default ArtErrorDisplay;
