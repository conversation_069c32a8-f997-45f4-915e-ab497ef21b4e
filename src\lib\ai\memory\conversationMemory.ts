import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/components/dashboard/ai-mentor/types';

export interface ConversationContext {
  userId: string;
  character: <PERSON><PERSON><PERSON><PERSON><PERSON>;
  characterName: string;
  threadId: string;
  lastInteraction: Date;
  messageCount: number;
  topics: string[];
  preferences: Record<string, unknown>;
}

export class ConversationMemoryManager {
  private contexts: Map<string, ConversationContext> = new Map();
  
  constructor() {
    // Load persisted contexts on initialization
    this.loadPersistedContexts();
  }

  /**
   * Get or create conversation context for a user and character
   */
  getContext(userId: string, character: Mentor<PERSON>haracter, characterName: string): ConversationContext {
    const contextKey = this.getContextKey(userId, character, characterName);
    
    if (!this.contexts.has(contextKey)) {
      const newContext: ConversationContext = {
        userId,
        character,
        characterName,
        threadId: contextKey,
        lastInteraction: new Date(),
        messageCount: 0,
        topics: [],
        preferences: {}
      };
      
      this.contexts.set(contextKey, newContext);
      this.persistContext(contextKey, newContext);
    }
    
    return this.contexts.get(contextKey)!;
  }

  /**
   * Update conversation context after an interaction
   */
  updateContext(
    userId: string, 
    character: MentorCharacter, 
    characterName: string, 
    userMessage: string
  ): void {
    const context = this.getContext(userId, character, characterName);
    
    // Update interaction metadata
    context.lastInteraction = new Date();
    context.messageCount += 1;
    
    // Extract and track topics from messages
    const topics = this.extractTopics(userMessage);
    topics.forEach(topic => {
      if (!context.topics.includes(topic)) {
        context.topics.push(topic);
      }
    });
    
    // Keep only recent topics (last 10)
    context.topics = context.topics.slice(-10);
    
    // Update preferences based on conversation patterns
    this.updatePreferences(context, userMessage);
    
    // Persist the updated context
    this.persistContext(this.getContextKey(userId, character, characterName), context);
  }

  /**
   * Get conversation summary for context injection
   */
  getConversationSummary(userId: string, character: MentorCharacter, characterName: string): string {
    const context = this.getContext(userId, character, characterName);
    
    let summary = '';
    
    if (context.messageCount > 0) {
      summary += `We've chatted ${context.messageCount} times. `;
      
      if (context.topics.length > 0) {
        summary += `You've been interested in: ${context.topics.slice(-5).join(', ')}. `;
      }
      
      if (context.preferences.favoriteActivity) {
        summary += `You seem to really enjoy ${context.preferences.favoriteActivity}. `;
      }
      
      if (context.preferences.creativeFocus) {
        summary += `You've been focusing on ${context.preferences.creativeFocus} lately. `;
      }
    }
    
    return summary.trim();
  }

  /**
   * Clear memory for a specific character (when user switches characters)
   */
  clearCharacterMemory(userId: string, character: MentorCharacter, characterName: string): void {
    const contextKey = this.getContextKey(userId, character, characterName);
    this.contexts.delete(contextKey);
    
    // Remove from localStorage
    if (typeof window !== 'undefined') {
      localStorage.removeItem(`conversation_context_${contextKey}`);
    }
  }

  private getContextKey(userId: string, character: MentorCharacter, characterName: string): string {
    return `${userId}-${character}-${characterName.replace(/\s+/g, '-').toLowerCase()}`;
  }

  private extractTopics(text: string): string[] {
    const topics: string[] = [];
    const lowerText = text.toLowerCase();
    
    // Creative activity keywords
    const activityMap: Record<string, string[]> = {
      'story': ['story', 'stories', 'character', 'plot', 'adventure', 'tale'],
      'art': ['art', 'draw', 'drawing', 'paint', 'painting', 'color', 'sketch'],
      'music': ['music', 'song', 'melody', 'instrument', 'rhythm', 'tune'],
      'game': ['game', 'play', 'puzzle', 'fun', 'challenge'],
      'science': ['science', 'experiment', 'discover', 'explore', 'learn'],
      'animals': ['animal', 'cat', 'dog', 'bird', 'fish', 'pet'],
      'space': ['space', 'planet', 'star', 'rocket', 'astronaut'],
      'nature': ['tree', 'flower', 'garden', 'forest', 'ocean', 'mountain']
    };
    
    Object.entries(activityMap).forEach(([topic, keywords]) => {
      if (keywords.some(keyword => lowerText.includes(keyword))) {
        topics.push(topic);
      }
    });
    
    return topics;
  }

  private updatePreferences(context: ConversationContext, userMessage: string): void {
    const lowerMessage = userMessage.toLowerCase();
    
    // Track favorite activities
    if (lowerMessage.includes('love') || lowerMessage.includes('favorite') || lowerMessage.includes('like')) {
      if (lowerMessage.includes('story')) context.preferences.favoriteActivity = 'storytelling';
      else if (lowerMessage.includes('art') || lowerMessage.includes('draw')) context.preferences.favoriteActivity = 'art';
      else if (lowerMessage.includes('music')) context.preferences.favoriteActivity = 'music';
      else if (lowerMessage.includes('game')) context.preferences.favoriteActivity = 'games';
    }
    
    // Track current creative focus
    const recentTopics = context.topics.slice(-3);
    if (recentTopics.length > 0) {
      const mostCommon = recentTopics.reduce((a, b, i, arr) => 
        arr.filter(v => v === a).length >= arr.filter(v => v === b).length ? a : b
      );
      context.preferences.creativeFocus = mostCommon;
    }
    
    // Track interaction patterns
    context.preferences.messageLength = userMessage.length > 50 ? 'detailed' : 'brief';
    context.preferences.enthusiasm = (lowerMessage.includes('!') || lowerMessage.includes('awesome') || lowerMessage.includes('cool')) ? 'high' : 'medium';
  }

  private persistContext(contextKey: string, context: ConversationContext): void {
    if (typeof window !== 'undefined') {
      try {
        localStorage.setItem(`conversation_context_${contextKey}`, JSON.stringify(context));
      } catch (error) {
        console.warn('Failed to persist conversation context:', error);
      }
    }
  }

  private loadPersistedContexts(): void {
    if (typeof window !== 'undefined') {
      try {
        Object.keys(localStorage).forEach(key => {
          if (key.startsWith('conversation_context_')) {
            const contextKey = key.replace('conversation_context_', '');
            const contextData = JSON.parse(localStorage.getItem(key) || '{}');
            
            // Restore Date objects
            contextData.lastInteraction = new Date(contextData.lastInteraction);
            
            this.contexts.set(contextKey, contextData);
          }
        });
      } catch (error) {
        console.warn('Failed to load persisted conversation contexts:', error);
      }
    }
  }
}

// Export singleton instance
export const conversationMemory = new ConversationMemoryManager(); 