import { NextRequest, NextResponse } from 'next/server';
import { AiAgent } from '@/lib/ai/agents/aiAgent';
import { Mentor<PERSON>haracter } from '@/components/dashboard/ai-mentor/types';
import { createServerSupabaseClient } from '@/lib/supabase/server';
import { moderateContent } from '@/utils/ai/contentModeration';

export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerSupabaseClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse request body
    const {
      message,
      character,
      characterName,
      chatHistory = [],
    } = await request.json();

    // Validate required fields
    if (!message || !character || !characterName) {
      return NextResponse.json(
        { error: 'Missing required fields: message, character, characterName' },
        { status: 400 }
      );
    }

    // Content safety check on user input (after ensuring message is present)
    const moderation = await moderateContent(message, 'prompt', user.id);
    if (!moderation.isAppropriate) {
      return NextResponse.json(
        { error: moderation.reason, blocked: moderation.blocked ?? false },
        { status: 400 },
      );
    }

    // Validate character type
    const validCharacters: MentorCharacter[] = ['robot', 'owl', 'explorer'];
    if (!validCharacters.includes(character)) {
      return NextResponse.json(
        { error: 'Invalid character type' },
        { status: 400 }
      );
    }

    // Create AI agent instance
    const agent = new AiAgent({
      character,
      userId: user.id
    });

    // Add chat history to agent memory if provided
    if (chatHistory && chatHistory.length > 0) {
      console.log(`[AI Memory] Adding ${chatHistory.length} messages to memory`);
      // Add recent chat history to memory
      for (const msg of chatHistory.slice(-8)) { // Only use last 8 messages to avoid overwhelming
        await agent.addToMemory(msg.content, msg.role);
      }
    }

    // Generate response using simplified agent
    const result = await agent.chat(message);

    // Content safety check on AI output (does not impact strikes)
    const outCheck = await moderateContent(result.content, 'text');
    if (!outCheck.isAppropriate) {
      // Replace with neutral child-friendly fallback to avoid delivering unsafe content
      result.content = "I'm sorry, let's try talking about something else!";
    }

    // AI Response for testing
    console.log(`[AI Buddy] ${characterName} (${character}): ${result.content}`);

    return NextResponse.json({
      response: result.content,
      character,
      characterName,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in AI buddy chat:', error);

    // Provide character-specific fallback responses
    const getCharacterFallback = (char: string) => {
      switch (char) {
        case 'robot':
          return "Oops! My circuits got a bit tangled there. What creative project should we work on together?";
        case 'owl':
          return "Oh my! That was quite puzzling. What would you like to explore today?";
        case 'explorer':
          return "Well, that was an unexpected detour! What adventure should we embark on?";
        default:
          return "I'm here to help with your creative projects! What would you like to create?";
      }
    };

    const fallbackResponse = getCharacterFallback(
      (await request.json().catch(() => ({})))?.character || 'robot'
    );

    return NextResponse.json({
      response: fallbackResponse,
      character: (await request.json().catch(() => ({})))?.character || 'robot',
      characterName: (await request.json().catch(() => ({})))?.characterName || 'Buddy',
      timestamp: new Date().toISOString(),
      fallback: true
    });
  }
}

// Health check endpoint
export async function GET() {
  return NextResponse.json({
    status: 'active',
    service: 'AI Buddy Chat',
    timestamp: new Date().toISOString(),
    features: [
      'LangGraph Agent',
      'Gemini Integration',
      'Character Personalities',
      'Conversation Memory',
      'Creative Tools'
    ]
  });
} 