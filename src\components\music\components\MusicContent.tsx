import React, { useState } from 'react';
import MusicGenerator from '../MusicGenerator';
import MusicPlayer from '../components/MusicPlayer';

const MusicContent: React.FC = () => {
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [musicTitle, setMusicTitle] = useState<string>('');

  const handleAudioGenerated = (url: string, title?: string) => {
    console.log('Audio URL received:', url);
    setAudioUrl(url);
    if (title && title.trim()) {
      setMusicTitle(title.trim());
    } else {
      setMusicTitle('Generated Music');
    }
  };

  const noop = () => {};

  return (
    <div className="min-h-screen w-full bg-gray-50 py-6 sm:py-8 lg:py-12 flex items-center justify-center">
      <div className="w-full px-4 max-w-7xl space-y-4 sm:space-y-6">
        <MusicGenerator
          onGenerate={noop}
          onAudioGenerated={handleAudioGenerated}
          onGenerationError={(error) => console.error(error)}
        />
        {audioUrl && (
          <MusicPlayer 
            audioUrl={audioUrl} 
            title={musicTitle} 
          />
        )}
      </div>
    </div>
  );
};

export default MusicContent;
