'use client';

import { useState, useEffect } from 'react';
import { getEducationalResources, CMSEducationalResource, getMediaUrl } from '@/lib/cms-api';
import { useSubscriptionStatus } from '@/hooks/useSubscriptionStatus';

interface CMSEducationalGridProps {
  type?: string;
  subject?: string;
  ageGroup?: string;
  difficulty?: string;
  featured?: boolean;
}

export function CMSEducationalGrid({ 
  type, 
  subject, 
  ageGroup, 
  difficulty, 
  featured 
}: CMSEducationalGridProps) {
  const [resources, setResources] = useState<CMSEducationalResource[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { hasActiveSubscription } = useSubscriptionStatus();

  useEffect(() => {
    async function loadResources() {
      try {
        setLoading(true);
        const data = await getEducationalResources({ 
          type, 
          subject, 
          ageGroup, 
          difficulty 
        });
        setResources(data);
      } catch (err) {
        setError('Failed to load educational resources');
        console.error('Error loading educational resources:', err);
      } finally {
        setLoading(false);
      }
    }

    loadResources();
  }, [type, subject, ageGroup, difficulty, featured]);

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(6)].map((_, i) => (
          <div key={i} className="bg-gray-200 animate-pulse rounded-lg h-64"></div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600">{error}</p>
        <button 
          onClick={() => window.location.reload()} 
          className="mt-4 px-4 py-2 bg-blue-500 text-white rounded"
        >
          Try Again
        </button>
      </div>
    );
  }

  if (resources.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-600">No educational resources found.</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {resources.map((resource) => (
        <CMSEducationalCard 
          key={resource.id} 
          resource={resource} 
          hasSubscription={hasActiveSubscription}
        />
      ))}
    </div>
  );
}

interface CMSEducationalCardProps {
  resource: CMSEducationalResource;
  hasSubscription: boolean;
}

function CMSEducationalCard({ resource, hasSubscription }: CMSEducationalCardProps) {
  const isLocked = resource.subscriptionTier === 'premium' && !hasSubscription;
  
  const handleClick = () => {
    if (isLocked) {
      alert('This educational resource requires a premium subscription!');
      return;
    }
    
    window.location.href = `/learn/${resource.slug}`;
  };

  // Get the main image
  const mainImage = resource.media?.find(m => m.type === 'main');

  // Get type icon
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'tutorial': return '🎓';
      case 'guide': return '📖';
      case 'reference': return '📋';
      case 'video': return '🎥';
      case 'interactive': return '🎮';
      case 'worksheet': return '📝';
      case 'tips': return '💡';
      default: return '📚';
    }
  };

  // Get difficulty color
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'bg-green-100 text-green-800';
      case 'intermediate': return 'bg-yellow-100 text-yellow-800';
      case 'advanced': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div 
      className={`bg-white rounded-lg shadow-md overflow-hidden cursor-pointer hover:shadow-lg transition-shadow ${
        isLocked ? 'opacity-75' : ''
      }`}
      onClick={handleClick}
    >
      {/* Resource Image */}
      {mainImage && (
        <div className="relative">
          <img 
            src={getMediaUrl(mainImage.file.url)} 
            alt={mainImage.file.alt || resource.title}
            className="w-full h-48 object-cover"
          />
          {isLocked && (
            <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
              <div className="text-white text-center">
                <span className="text-4xl">🔒</span>
                <p className="mt-2 text-sm">Premium</p>
              </div>
            </div>
          )}
          {resource.featured && (
            <div className="absolute top-2 right-2 bg-yellow-500 text-white px-2 py-1 rounded text-xs font-bold">
              ⭐ Featured
            </div>
          )}
        </div>
      )}

      <div className="p-4">
        {/* Type Icon and Title */}
        <div className="flex items-start space-x-2 mb-2">
          <span className="text-2xl">{getTypeIcon(resource.type)}</span>
          <h3 className="text-lg font-semibold line-clamp-2 flex-1">{resource.title}</h3>
        </div>
        
        {/* Description */}
        <div 
          className="text-gray-600 text-sm mb-3 line-clamp-3"
          dangerouslySetInnerHTML={{ __html: resource.description }}
        />
        
        {/* Learning Outcomes */}
        {resource.learningOutcomes && resource.learningOutcomes.length > 0 && (
          <div className="mb-3">
            <p className="text-xs font-semibold text-gray-700 mb-1">You'll Learn:</p>
            <ul className="text-xs text-gray-600">
              {resource.learningOutcomes.slice(0, 2).map((outcome, index) => (
                <li key={index} className="flex items-center">
                  <span className="w-1 h-1 bg-blue-500 rounded-full mr-2"></span>
                  {outcome.outcome}
                </li>
              ))}
            </ul>
          </div>
        )}
        
        {/* Downloadable Files Count */}
        {resource.downloadableFiles && resource.downloadableFiles.length > 0 && (
          <div className="mb-3">
            <p className="text-xs text-blue-600">
              📎 {resource.downloadableFiles.length} downloadable file{resource.downloadableFiles.length !== 1 ? 's' : ''}
            </p>
          </div>
        )}
        
        {/* Footer */}
        <div className="flex justify-between items-center text-xs text-gray-500">
          <div className="flex items-center space-x-2">
            {/* Type */}
            <span className="px-2 py-1 rounded bg-blue-100 text-blue-800 capitalize">
              {resource.type}
            </span>
            {/* Difficulty */}
            <span className={`px-2 py-1 rounded capitalize ${getDifficultyColor(resource.difficulty)}`}>
              {resource.difficulty}
            </span>
          </div>
          {/* Read Time */}
          {resource.estimatedReadTime && (
            <span>{resource.estimatedReadTime} min read</span>
          )}
        </div>
        
        {/* Subjects */}
        {resource.subject && resource.subject.length > 0 && (
          <div className="mt-2 flex flex-wrap gap-1">
            {resource.subject.slice(0, 3).map((subj, index) => (
              <span 
                key={index}
                className="text-xs px-2 py-1 bg-gray-100 text-gray-600 rounded"
              >
                {subj.replace('-', ' ')}
              </span>
            ))}
            {resource.subject.length > 3 && (
              <span className="text-xs text-gray-500">
                +{resource.subject.length - 3} more
              </span>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
