
/**
 * Draws a castle shape on the canvas
 */
export const drawCastleShape = (ctx: CanvasRenderingContext2D, x: number, y: number, size: number) => {
  // Main castle body
  ctx.rect(x - size*0.6, y - size*0.2, size*1.2, size*0.8);
  
  // Left tower
  ctx.rect(x - size*0.8, y - size*0.4, size*0.3, size*1);
  
  // Right tower
  ctx.rect(x + size*0.5, y - size*0.4, size*0.3, size*1);
  
  // Center tower
  ctx.rect(x - size*0.15, y - size*0.6, size*0.3, size*0.4);
  
  // Battlements on left tower
  for (let i = 0; i < 3; i++) {
    ctx.rect(x - size*0.8 + i*size*0.1, y - size*0.5, size*0.05, size*0.1);
  }
  
  // Battlements on right tower
  for (let i = 0; i < 3; i++) {
    ctx.rect(x + size*0.5 + i*size*0.1, y - size*0.5, size*0.05, size*0.1);
  }
  
  // Battlements on center tower
  for (let i = 0; i < 3; i++) {
    ctx.rect(x - size*0.15 + i*size*0.1, y - size*0.7, size*0.05, size*0.1);
  }
  
  // Door
  ctx.rect(x - size*0.1, y + size*0.3, size*0.2, size*0.3);
  ctx.moveTo(x - size*0.1, y + size*0.3);
  ctx.quadraticCurveTo(
    x, y + size*0.2,
    x + size*0.1, y + size*0.3
  );
  
  // Windows
  ctx.rect(x - size*0.4, y, size*0.15, size*0.15);
  ctx.rect(x + size*0.25, y, size*0.15, size*0.15);
  
  // Flag
  ctx.moveTo(x + size*0.05, y - size*0.6);
  ctx.lineTo(x + size*0.05, y - size*0.9);
  ctx.lineTo(x + size*0.25, y - size*0.8);
  ctx.lineTo(x + size*0.05, y - size*0.7);
};
