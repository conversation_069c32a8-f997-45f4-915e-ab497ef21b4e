
/**
 * Draws a butterfly shape on the canvas
 */
export const drawButterflyShape = (ctx: CanvasRenderingContext2D, x: number, y: number, size: number) => {
  // Upper left wing
  ctx.moveTo(x, y);
  ctx.quadraticCurveTo(
    x - size*0.5, y - size*0.5,
    x - size*0.7, y - size*0.1
  );
  ctx.quadraticCurveTo(
    x - size*0.3, y + size*0.2,
    x, y
  );
  
  // Upper right wing
  ctx.moveTo(x, y);
  ctx.quadraticCurveTo(
    x + size*0.5, y - size*0.5,
    x + size*0.7, y - size*0.1
  );
  ctx.quadraticCurveTo(
    x + size*0.3, y + size*0.2,
    x, y
  );
  
  // Lower left wing
  ctx.moveTo(x, y);
  ctx.quadraticCurveTo(
    x - size*0.5, y + size*0.5,
    x - size*0.6, y + size*0.1
  );
  ctx.quadraticCurveTo(
    x - size*0.3, y - size*0.2,
    x, y
  );
  
  // Lower right wing
  ctx.moveTo(x, y);
  ctx.quadraticCurveTo(
    x + size*0.5, y + size*0.5,
    x + size*0.6, y + size*0.1
  );
  ctx.quadraticCurveTo(
    x + size*0.3, y - size*0.2,
    x, y
  );
  
  // Body
  ctx.moveTo(x, y - size*0.3);
  ctx.lineTo(x, y + size*0.3);
  
  // Antennae
  ctx.moveTo(x, y - size*0.3);
  ctx.quadraticCurveTo(
    x - size*0.2, y - size*0.5,
    x - size*0.3, y - size*0.6
  );
  
  ctx.moveTo(x, y - size*0.3);
  ctx.quadraticCurveTo(
    x + size*0.2, y - size*0.5,
    x + size*0.3, y - size*0.6
  );
};
