import React from 'react';
import Image from 'next/image';
import { Star } from 'lucide-react';
import { testimonials, testimonialsContent, type Testimonial } from '@/lib/constants';
import { fredoka } from '@/lib/fonts';

const StarRating = ({ rating }: { rating: number }) => {
  return (
    <div className="flex space-x-1 mb-4">
      {[1, 2, 3, 4, 5].map((star) => (
        <Star
          key={star}
          className={`h-5 w-5 ${
            star <= rating 
              ? 'text-littlespark-yellow fill-current' 
              : 'text-gray-300'
          }`}
        />
      ))}
    </div>
  );
};

const TestimonialCard = ({ testimonial, delay }: { testimonial: Testimonial; delay: string }) => {
  return (
    <div
      className="bg-white p-6 rounded-2xl shadow-lg hover:shadow-xl border border-gray-100 group
                 animate-fadeInUp hover:scale-105 hover:-translate-y-1 transition-all duration-300"
      style={{ animationDelay: delay }}
    >
      <StarRating rating={testimonial.rating} />
      
      <blockquote className="text-gray-700 mb-6 leading-relaxed italic">
        &quot;{testimonial.content}&quot;
      </blockquote>
      
      <div className="flex items-center space-x-3">
        <div className="relative w-12 h-12 rounded-full overflow-hidden ring-2 ring-littlespark-primary/20">
          <Image
            src={testimonial.avatar_url}
            alt={testimonial.author}
            fill
            className="object-cover"
          />
        </div>
        <div>
          <div className={`font-semibold text-gray-900 ${fredoka.className}`}>
            {testimonial.author}
          </div>
          <div className="text-sm text-gray-600">
            {testimonial.role}
          </div>
        </div>
      </div>
    </div>
  );
};

const TestimonialsSection = () => {
  return (
    <section className="py-16 bg-gray-50 relative overflow-hidden">
      {/* Background blobs */}
      <div className="absolute top-1/4 right-0 w-80 h-80 bg-littlespark-lavender/20 rounded-full blur-3xl"></div>
      <div className="absolute bottom-1/4 left-0 w-96 h-96 bg-littlespark-primary/15 rounded-full blur-3xl"></div>
      
      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center mb-16">
          <h2 className={`text-3xl text-gray-900 md:text-4xl font-bold mb-6 ${fredoka.className}`}>
            {testimonialsContent.title}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {testimonialsContent.subtitle}
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
          {testimonials.map((testimonial, index) => (
            <TestimonialCard
              key={testimonial.id}
              testimonial={testimonial}
              delay={`${0.2 + (index * 0.1)}s`}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default TestimonialsSection; 