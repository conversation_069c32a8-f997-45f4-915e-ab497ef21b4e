import { NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase/server';

export async function POST() {
  try {
    const supabase = await createServerSupabaseClient();

    // Try to sign out, but handle session missing error gracefully
    const { error } = await supabase.auth.signOut();

    if (error && error.message !== 'Auth session missing!') {
      console.error('Signout error:', error);
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    // Create response and clear auth cookies
    const response = NextResponse.json({
      message: 'Signed out successfully',
      success: true
    });

    // Clear Supabase auth cookies
    const cookiesToClear = [
      'sb-access-token',
      'sb-refresh-token',
      'supabase.auth.token',
      'supabase-auth-token'
    ];

    cookiesToClear.forEach(cookieName => {
      response.cookies.set(cookieName, '', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: 0,
        path: '/'
      });
    });

    return response;

  } catch (error) {
    console.error('Signout API error:', error);
    
    // Even if there's an error, return success and clear cookies
    const response = NextResponse.json({
      message: 'Signed out successfully',
      success: true
    });

    // Clear cookies anyway
    const cookiesToClear = [
      'sb-access-token', 
      'sb-refresh-token',
      'supabase.auth.token',
      'supabase-auth-token'
    ];

    cookiesToClear.forEach(cookieName => {
      response.cookies.set(cookieName, '', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: 0,
        path: '/'
      });
    });

    return response;
  }
} 