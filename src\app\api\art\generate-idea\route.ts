import { NextResponse } from 'next/server';
import { moderateContent } from '@/utils/ai/contentModeration';
import { ChatGoogleGenerativeAI } from "@langchain/google-genai";

const llm = new ChatGoogleGenerativeAI({
  model: "gemini-2.0-flash",
  temperature: 0.9,
  apiKey: process.env.GOOGLE_API_KEY || "",
});

export async function POST(request: Request) {
  try {
    const { style = 'cartoon' } = await request.json().catch(() => ({ style: 'cartoon' }));

    const userKey = request.headers.get('x-forwarded-for') ?? 'anonymous';
    const styleCheck = await moderateContent(style || '', 'prompt', userKey);
    if (!styleCheck.isAppropriate) {
      return NextResponse.json({ success: false, error: styleCheck.reason, blocked: styleCheck.blocked ?? false }, { status: 400 });
    }

    console.log('=== ART GENERATE IDEA API ===');
    console.log('Style:', style);
    console.log('GOOGLE_API_KEY available:', !!process.env.GOOGLE_API_KEY);

    if (!process.env.GOOGLE_API_KEY) {
      console.error('GOOGLE_API_KEY not configured');
      return NextResponse.json(
        { success: false, error: 'Google API key not configured' },
        { status: 500 }
      );
    }

    const generateIdeaPrompt = `You are a creative art idea generator for children's artwork. Generate a completely original, imaginative, and child-friendly art prompt in ${style} style that would inspire kids to create beautiful artwork. Keep it within 25 words.

IMPORTANT GUIDELINES:
1. Create completely original and unique ideas
2. Keep content strictly child-friendly and positive
3. Use vivid, colorful, and imaginative descriptions
4. Focus on themes of wonder, magic, adventure, nature, friendship, and discovery
5. Avoid any scary, violent, or inappropriate content
6. Make it engaging and fun for children
7. Include interesting visual elements like colors, textures, or magical elements
8. Keep it concise but descriptive (1-2 sentences)

Generate a single, original art idea (max 25 words) that would inspire a child to create amazing artwork:`;

    console.log('Calling Gemini API for idea generation...');

    let generatedIdea: string | undefined;

    try {
      const response = await llm.invoke([
        {
          role: "user",
          content: generateIdeaPrompt
        }
      ]);

      generatedIdea = typeof response.content === 'string' ? response.content.trim() : response.content?.toString();
      console.log('Gemini API response:', generatedIdea);

      if (!generatedIdea) {
        throw new Error("Gemini API failed or returned empty result");
      }
    } catch (apiError) {
      console.error('Gemini API error for art idea:', apiError);

      // Provide fallback art ideas based on style
      const fallbackIdeas = {
        cartoon: "A friendly cartoon cat wearing a colorful hat, sitting in a magical garden with rainbow flowers",
        watercolor: "A peaceful watercolor landscape with soft mountains, gentle clouds, and a sparkling river",
        realistic: "A beautiful realistic portrait of a majestic butterfly landing on a blooming sunflower",
        comic: "A superhero comic character with a bright cape flying through a city of colorful buildings",
        pixel: "A retro pixel art castle with a dragon friend and treasure chest in 8-bit style"
      };

      generatedIdea = fallbackIdeas[style as keyof typeof fallbackIdeas] ||
                     "A magical creative artwork that sparks imagination and brings joy to everyone who sees it";

      console.log('Using fallback art idea due to API error:', generatedIdea);
    }

    console.log('Generated art idea:', generatedIdea);

    return NextResponse.json({
      success: true,
      idea: generatedIdea
    });

  } catch (error) {
    console.error('Error generating art idea:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to generate art idea' },
      { status: 500 }
    );
  }
} 

