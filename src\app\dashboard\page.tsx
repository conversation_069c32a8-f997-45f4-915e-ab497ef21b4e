"use client";

import React, { useState, useEffect } from "react";
import { useAuth } from "@/hooks/useAuth";
import { useRouter } from "next/navigation";
import {
    DashboardHeader,
    UserProgress,
    CreativeTools,
    CreativeChallenges,
    AiMentorSection,
    TestimonialManager,
} from "@/components/dashboard";
import { SubscriptionSection } from "@/components/dashboard/subscription";
import { MindSpark } from "@/components/dashboard/creative";
import { CMSCreativeDashboard } from "@/components/dashboard/creative/CMSCreativeDashboard";
import { CMSChallengeGrid } from "@/components/cms/CMSChallengeGrid";

interface UserProfile {
    id?: string;
    username?: string;
    avatar_url?: string;
    age?: number;
}

const isPreviewMode = () => {
    return (
        process.env.NODE_ENV === "development" &&
        !process.env.NEXT_PUBLIC_SUPABASE_URL
    );
};

export default function DashboardPage() {
    const { user, loading: authLoading } = useAuth();
    const router = useRouter();
    const [userData] = useState({
        completedProjects: 0,
        skillLevel: 1,
        streakDays: 0,
        badges: [
            { name: "Storyteller", icon: "📝", earned: false },
            { name: "Artist", icon: "🎨", earned: false },
            { name: "Musician", icon: "🎵", earned: false },
            { name: "Game Designer", icon: "🎮", earned: false },
            { name: "Creative Star", icon: "⭐", earned: false },
        ],
    });
    const [isLoading, setIsLoading] = useState(false);
    const [userAge, setUserAge] = useState(10);
    const inPreviewMode = isPreviewMode();

    // Redirect to auth if not logged in
    useEffect(() => {
        if (!authLoading && !user && !inPreviewMode) {
            router.push("/auth");
        }
    }, [user, authLoading, router, inPreviewMode]);

    useEffect(() => {
        // Skip data fetch in preview mode
        if (inPreviewMode) {
            // Preview mode: Using demo data for dashboard
            return;
        }

        const fetchUserData = async () => {
            if (!user) return;

            try {
                setIsLoading(true);

                // Fetch user profile via our serverless API route
                const response = await fetch('/api/user/profile');
                const result = await response.json();
                if (!response.ok) {
                    console.warn("Error fetching profile:", result.error);
                } else if (result.profile) {
                    const userProfile = result.profile as UserProfile;
                    if (userProfile.age) {
                        setUserAge(userProfile.age);
                    }
                }

                // Reset loading state after profile check
                setIsLoading(false);
            } catch (error) {
                console.error("Error in initial dashboard load:", error);
                setIsLoading(false);
            }
        };

        fetchUserData();
    }, [user, inPreviewMode]);

    // Show loading screen during auth loading to prevent hydration flash
    if (authLoading && !inPreviewMode) {
        return (
            <div className="min-h-screen bg-white flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-[#FF6B35] mx-auto mb-4"></div>
                    <p className="text-gray-600 font-nunito">
                        Loading your dashboard...
                    </p>
                </div>
            </div>
        );
    }

    if (!user && !inPreviewMode) {
        return null; // Will redirect in useEffect
    }

    // Use placeholder test data for development, but with real user data when available
    // Always use test data in preview mode
    const testUserData = {
        ...userData,
        badges: userData.badges.map((badge) => ({ ...badge, earned: true })),
    };

    // Show preview mode indicator
    const PreviewModeIndicator = () =>
        inPreviewMode ? (
            <div className="bg-blue-50 text-blue-700 p-3 rounded-md mb-4 text-sm flex items-center justify-between font-nunito">
                <div>
                    <p className="font-medium">🔍 Preview Mode Active</p>
                    <p>Authentication is bypassed. Using demo data.</p>
                </div>
            </div>
        ) : null;

    return (
        <div className="min-h-screen bg-white">
            <div className="container max-w-6xl py-4 sm:py-6 lg:py-8 px-4 mx-auto space-y-6 sm:space-y-8 lg:space-y-10">
                <PreviewModeIndicator />

                <DashboardHeader />

                {/* Featured CMS Challenges */}
                <div className="mb-8">
                    <h2 className="text-2xl font-bold text-gray-800 mb-4">🌟 Featured Challenges</h2>
                    <CMSChallengeGrid featured={true} />
                </div>

                <CreativeTools />
                <CreativeChallenges />

                <UserProgress
                    isLoading={isLoading && !inPreviewMode}
                    userData={inPreviewMode || !user ? testUserData : userData}
                />

                <div className="mt-8">
                    <MindSpark
                        userAge={userAge}
                        safeMode={true}
                        toolType="general"
                    />
                </div>

                <AiMentorSection />

                <div className="mt-8">
                    <TestimonialManager />
                </div>

                <SubscriptionSection />
            </div>
        </div>
    );
}
