"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/jsox";
exports.ids = ["vendor-chunks/jsox"];
exports.modules = {

/***/ "(rsc)/./node_modules/jsox/lib/jsox.mjs":
/*!****************************************!*\
  !*** ./node_modules/jsox/lib/jsox.mjs ***!
  \****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   JSOX: () => (/* binding */ JSOX)\n/* harmony export */ });\n//\"use strict\";\n// jsox.js\n// JSOX JavaScript Object eXchange. Inherits human features of comments\n// and extended formatting from JSON6; adds macros, big number and date\n// support.  See README.md for details.\n//\n// This file is based off of https://github.com/JSON6/  ./lib/json6.js\n// which is based off of https://github.com/d3x0r/sack  ./src/netlib/html5.websocket/json6_parser.c\n//\n\n//const util = require('util'); // debug inspect.\n//import util from 'util'; \n\nconst _JSON=JSON; // in case someone does something like JSON=JSOX; we still need a primitive _JSON for internal stringification\nif( \"undefined\" === typeof exports )\n\tvar exports = {};\nconst JSOX = exports || {};\nexports.JSOX = JSOX;\n\nJSOX.version = \"1.2.121\";\n\n//function privateizeEverything() {\n//const _DEBUG_LL = false;\n//const _DEBUG_PARSING = false;\n//const _DEBUG_STRINGIFY = false;\n//const _DEBUG_PARSING_STACK = false;\n//const _DEBUG_PARSING_NUMBERS = false;\n//const _DEBUG_PARSING_DETAILS = false;\n//const _DEBUG_PARSING_CONTEXT = false;\n//const _DEBUG_REFERENCES = false; // this tracks folling context stack when the components have not been completed.\n//const _DEBUG_WHITESPACE = false; \nconst hasBigInt = (typeof BigInt === \"function\");\nconst testNonIdentifierCharacters = false; // maybe an option to enable; references otherwise unused table.\nconst VALUE_UNDEFINED = -1\nconst VALUE_UNSET = 0\nconst VALUE_NULL = 1\nconst VALUE_TRUE = 2\nconst VALUE_FALSE = 3\nconst VALUE_STRING = 4\nconst VALUE_NUMBER = 5\nconst VALUE_OBJECT = 6\nconst VALUE_NEG_NAN = 7\nconst VALUE_NAN = 8\nconst VALUE_NEG_INFINITY = 9\nconst VALUE_INFINITY = 10\n//const VALUE_DATE = 11  // unused yet; this is actuall a subType of VALUE_NUMBER\nconst VALUE_EMPTY = 12 // [,] makes an array with 'empty item'\nconst VALUE_ARRAY = 13 //\n// internally arrayType = -1 is a normal array\n// arrayType = -2 is a reference array, which, which closed is resolved to\n//     the specified object.\n// arrayType = -3 is a normal array, that has already had this element pushed.\nconst knownArrayTypeNames = [\"ab\",\"u8\",\"cu8\",\"s8\",\"u16\",\"s16\",\"u32\",\"s32\",\"u64\",\"s64\",\"f32\",\"f64\"];\nlet arrayToJSOX = null;\nlet mapToJSOX = null;\nconst knownArrayTypes = [ArrayBuffer\n                        ,Uint8Array,Uint8ClampedArray,Int8Array\n                        ,Uint16Array,Int16Array\n                        ,Uint32Array,Int32Array\n                        ,null,null//,Uint64Array,Int64Array\n                        ,Float32Array,Float64Array];\n// somehow max isn't used... it would be the NEXT available VALUE_XXX value...\n//const VALUE_ARRAY_MAX = VALUE_ARRAY + knownArrayTypes.length + 1; // 1 type is not typed; just an array.\n\nconst WORD_POS_RESET = 0;\nconst WORD_POS_TRUE_1 = 1;\nconst WORD_POS_TRUE_2 = 2;\nconst WORD_POS_TRUE_3 = 3;\nconst WORD_POS_FALSE_1 = 5;\nconst WORD_POS_FALSE_2 = 6;\nconst WORD_POS_FALSE_3 = 7;\nconst WORD_POS_FALSE_4 = 8;\nconst WORD_POS_NULL_1 = 9;\nconst WORD_POS_NULL_2 = 10;\nconst WORD_POS_NULL_3 = 11;\nconst WORD_POS_UNDEFINED_1 = 12;\nconst WORD_POS_UNDEFINED_2 = 13;\nconst WORD_POS_UNDEFINED_3 = 14;\nconst WORD_POS_UNDEFINED_4 = 15;\nconst WORD_POS_UNDEFINED_5 = 16;\nconst WORD_POS_UNDEFINED_6 = 17;\nconst WORD_POS_UNDEFINED_7 = 18;\nconst WORD_POS_UNDEFINED_8 = 19;\nconst WORD_POS_NAN_1 = 20;\nconst WORD_POS_NAN_2 = 21;\nconst WORD_POS_INFINITY_1 = 22;\nconst WORD_POS_INFINITY_2 = 23;\nconst WORD_POS_INFINITY_3 = 24;\nconst WORD_POS_INFINITY_4 = 25;\nconst WORD_POS_INFINITY_5 = 26;\nconst WORD_POS_INFINITY_6 = 27;\nconst WORD_POS_INFINITY_7 = 28;\n\nconst WORD_POS_FIELD = 29;\nconst WORD_POS_AFTER_FIELD = 30;\nconst WORD_POS_END = 31;\nconst WORD_POS_AFTER_FIELD_VALUE = 32;\n//const WORD_POS_BINARY = 32;\n\nconst CONTEXT_UNKNOWN = 0\nconst CONTEXT_IN_ARRAY = 1\nconst CONTEXT_OBJECT_FIELD = 2\nconst CONTEXT_OBJECT_FIELD_VALUE = 3\nconst CONTEXT_CLASS_FIELD = 4\nconst CONTEXT_CLASS_VALUE = 5\nconst CONTEXT_CLASS_FIELD_VALUE = 6\nconst keywords = {\t[\"true\"]:true,[\"false\"]:false,[\"null\"]:null,[\"NaN\"]:NaN,[\"Infinity\"]:Infinity,[\"undefined\"]:undefined }\n\n/*\nExtend Date type with a nanosecond field.\n*/\nclass DateNS extends Date {\n\tconstructor(a,b ) {\n\t\tsuper(a);\n\t\tthis.ns = b||0;\n\t}\t\n}\n\nJSOX.DateNS = DateNS;\n\nconst contexts = [];\nfunction getContext() {\n\tlet ctx = contexts.pop();\n\tif( !ctx )\n\t\tctx = { context : CONTEXT_UNKNOWN\n\t\t      , current_proto : null\n\t\t      , current_class : null\n\t\t      , current_class_field : 0\n\t\t      , arrayType : -1\n\t\t      , valueType : VALUE_UNSET\n\t\t      , elements : null\n\t\t      };\n\treturn ctx;\n}\nfunction dropContext(ctx) { \n\tcontexts.push( ctx ) \n}\n\nJSOX.updateContext = function() {\n    //if( toProtoTypes.get( Map.prototype ) ) return;\n    //console.log( \"Do init protoypes for new context objects...\" );\n    //initPrototypes();\n}\n\nconst buffers = [];\nfunction getBuffer() { let buf = buffers.pop(); if( !buf ) buf = { buf:null, n:0 }; else buf.n = 0; return buf; }\nfunction dropBuffer(buf) { buffers.push( buf ); }\n\n/**\n * @param {string} string \n * @returns {string}\n */\nJSOX.escape = function(string) {\n\tlet n;\n\tlet output = '';\n\tif( !string ) return string;\n\tfor( n = 0; n < string.length; n++ ) {\n\t\tif( ( string[n] == '\"' ) || ( string[n] == '\\\\' ) || ( string[n] == '`' )|| ( string[n] == '\\'' )) {\n\t\t\toutput += '\\\\';\n\t\t}\n\t\toutput += string[n];\n\t}\n\treturn output;\n}\n\n\nlet toProtoTypes = new WeakMap();\nlet toObjectTypes = new Map();\nlet fromProtoTypes = new Map();\nlet commonClasses = [];\n\nJSOX.reset = resetJSOX;\n\nfunction resetJSOX() {\n\ttoProtoTypes = new WeakMap();\n\ttoObjectTypes = new Map();\n\tfromProtoTypes = new Map();\n\tcommonClasses = [];\t\n}\n\n/**\n * @param {(value:any)} [cb]\n * @param {(this: unknown, key: string, value: unknown) => any} [reviver] \n * @returns {none}\n*/\nJSOX.begin = function( cb, reviver ) {\n\n\tconst val = { name : null,\t  // name of this value (if it's contained in an object)\n\t\t\tvalue_type: VALUE_UNSET, // value from above indiciating the type of this value\n\t\t\tstring : '',   // the string value of this value (strings and number types only)\n\t\t\tcontains : null,\n\t\t\tclassName : null,\n\t\t};\n\t\n\tconst pos = { line:1, col:1 };\n\tlet\tn = 0;\n\tlet     str;\n\tlet\tlocalFromProtoTypes = new Map();\n\tlet\tword = WORD_POS_RESET,\n\t\tstatus = true,\n\t\tredefineClass = false,\n\t\tnegative = false,\n\t\tresult = null,\n\t\trootObject = null,\n\t\telements = undefined,\n\t\tcontext_stack = {\n\t\t\tfirst : null,\n\t\t\tlast : null,\n\t\t\tsaved : null,\n\t\t\tpush(node) {\n\t\t\t\t//_DEBUG_PARSING_CONTEXT && console.log( \"pushing context:\", node );\n\t\t\t\tlet recover = this.saved;\n\t\t\t\tif( recover ) { this.saved = recover.next; \n\t\t\t\t\trecover.node = node; \n\t\t\t\t\trecover.next = null; \n\t\t\t\t\trecover.prior = this.last; }\n\t\t\t\telse { recover = { node : node, next : null, prior : this.last }; }\n\t\t\t\tif( !this.last ) this.first = recover;\n\t\t\t\telse this.last.next = recover;\n\t\t\t\tthis.last = recover;\n\t\t\t\tthis.length++;\n\t\t\t},\n\t\t\tpop() {\n\t\t\t\tlet result = this.last;\n\t\t\t\t// through normal usage this line can never be used.\n\t\t\t\t//if( !result ) return null;\n\t\t\t\tif( !(this.last = result.prior ) ) this.first = null;\n\t\t\t\tresult.next = this.saved;\n\t\t\t\tif( this.last ) this.last.next = null;\n\t\t\t\tif( !result.next ) result.first = null;\n\t\t\t\tthis.saved = result;\n\t\t\t\tthis.length--;\n\t\t\t\t//_DEBUG_PARSING_CONTEXT && console.log( \"popping context:\", result.node );\n\t\t\t\treturn result.node;\n\t\t\t},\n\t\t\tlength : 0,\n\t\t\t/*dump() {  // //_DEBUG_CONTEXT_STACK\n\t\t\t\tconsole.log( \"STACK LENGTH:\", this.length );\n\t\t\t\tlet cur= this.first;\n\t\t\t\tlet level = 0;\n\t\t\t\twhile( cur ) {\n\t\t\t\t\tconsole.log( \"Context:\", level, cur.node );\n\t\t\t\t\tlevel++;\n\t\t\t\t\tcur = cur.next;\n\t\t\t\t}\n\t\t\t}*/\n\t\t},\n\t\tclasses = [],  // class templates that have been defined.\n\t\tprotoTypes = {},\n\t\tcurrent_proto = null,  // the current class being defined or being referenced.\n\t\tcurrent_class = null,  // the current class being defined or being referenced.\n\t\tcurrent_class_field = 0,\n\t\tarrayType = -1,  // the current class being defined or being referenced.\n\t\tparse_context = CONTEXT_UNKNOWN,\n\t\tcomment = 0,\n\t\tfromHex = false,\n\t\tdecimal = false,\n\t\texponent = false,\n\t\texponent_sign = false,\n\t\texponent_digit = false,\n\t\tinQueue = {\n\t\t\tfirst : null,\n\t\t\tlast : null,\n\t\t\tsaved : null,\n\t\t\tpush(node) {\n\t\t\t\tlet recover = this.saved;\n\t\t\t\tif( recover ) { this.saved = recover.next; recover.node = node; recover.next = null; recover.prior = this.last; }\n\t\t\t\telse { recover = { node : node, next : null, prior : this.last }; }\n\t\t\t\tif( !this.last ) this.first = recover;\n\t\t\t\telse this.last.next = recover;\n\t\t\t\tthis.last = recover;\n\t\t\t},\n\t\t\tshift() {\n\t\t\t\tlet result = this.first;\n\t\t\t\tif( !result ) return null;\n\t\t\t\tif( !(this.first = result.next ) ) this.last = null;\n\t\t\t\tresult.next = this.saved; this.saved = result;\n\t\t\t\treturn result.node;\n\t\t\t},\n\t\t\tunshift(node) {\n\t\t\t\tlet recover = this.saved;\n\t\t\t\t// this is always true in this usage.\n\t\t\t\t//if( recover ) { \n\t\t\t\t\tthis.saved = recover.next; recover.node = node; recover.next = this.first; recover.prior = null; \n\t\t\t\t//}\n\t\t\t\t//else { recover = { node : node, next : this.first, prior : null }; }\n\t\t\t\tif( !this.first ) this.last = recover;\n\t\t\t\tthis.first = recover;\n\t\t\t}\n\t\t},\n\t\tgatheringStringFirstChar = null,\n\t\tgatheringString = false,\n\t\tgatheringNumber = false,\n\t\tstringEscape = false,\n\t\tcr_escaped = false,\n\t\tunicodeWide = false,\n\t\tstringUnicode = false,\n\t\tstringHex = false,\n\t\thex_char = 0,\n\t\thex_char_len = 0,\n\t\tcompleted = false,\n\t\tdate_format = false,\n\t\tisBigInt = false\n\t\t;\n\n\tfunction throwEndError( leader ) {\n\t\tthrow new Error( `${leader} at ${n} [${pos.line}:${pos.col}]`);\n\t}\n\n\treturn {\n\t\t/**\n\t\t * Define a class that can be used to deserialize objects of this type.\n\t\t * @param {string} prototypeName \n\t\t * @param {type} o \n\t\t * @param {(any)=>any} f \n\t\t */\n\t\tfromJSOX( prototypeName, o, f ) {\n\t\t\tif( localFromProtoTypes.get(prototypeName) ) throw new Error( \"Existing fromJSOX has been registered for prototype\" );\n\t\t\tfunction privateProto() { }\n\t\t\tif( !o ) o = privateProto;\n\t\t\tif( o && !(\"constructor\" in o )){\n\t\t\t\tthrow new Error( \"Please pass a prototype like thing...\");\n\t\t\t}\n\t\t\tlocalFromProtoTypes.set( prototypeName, { protoCon:o.prototype.constructor, cb:f } );\n\t\t},\n\t\tregisterFromJSOX( prototypeName, o/*, f*/ ) {\n\t\t\tthrow new Error( \"registerFromJSOX is deprecated, please update to use fromJSOX instead:\" + prototypeName + o.toString() );\n\t\t},\n\t\tfinalError() {\n\t\t\tif( comment !== 0 ) { // most of the time everything's good.\n\t\t\t\tif( comment === 1 ) throwEndError( \"Comment began at end of document\" );\n\t\t\t\tif( comment === 2 ) /*console.log( \"Warning: '//' comment without end of line ended document\" )*/;\n\t\t\t\tif( comment === 3 ) throwEndError( \"Open comment '/*' is missing close at end of document\" );\n\t\t\t\tif( comment === 4 ) throwEndError( \"Incomplete '/* *' close at end of document\" );\n\t\t\t}\n\t\t\tif( gatheringString ) throwEndError( \"Incomplete string\" );\n\t\t},\n\t\tvalue() {\n\t\t\tthis.finalError();\n\t\t\tlet r = result;\n\t\t\tresult = undefined;\n\t\t\treturn r;\n\t\t},\n\t\t/**\n\t\t * Reset the parser to a blank state.\n\t\t */\n\t\treset() {\n\t\t\tword = WORD_POS_RESET;\n\t\t\tstatus = true;\n\t\t\tif( inQueue.last ) inQueue.last.next = inQueue.save;\n\t\t\tinQueue.save = inQueue.first;\n\t\t\tinQueue.first = inQueue.last = null;\n\t\t\tif( context_stack.last ) context_stack.last.next = context_stack.save;\n\t\t\tcontext_stack.length = 0;\n\t\t\tcontext_stack.save = inQueue.first;\n\t\t\tcontext_stack.first = context_stack.last = null;//= [];\n\t\t\telements = undefined;\n\t\t\tparse_context = CONTEXT_UNKNOWN;\n\t\t\tclasses = [];\n\t\t\tprotoTypes = {};\n\t\t\tcurrent_proto = null;\n\t\t\tcurrent_class = null;\n\t\t\tcurrent_class_field = 0;\n\t\t\tval.value_type = VALUE_UNSET;\n\t\t\tval.name = null;\n\t\t\tval.string = '';\n\t\t\tval.className = null;\n\t\t\tpos.line = 1;\n\t\t\tpos.col = 1;\n\t\t\tnegative = false;\n\t\t\tcomment = 0;\n\t\t\tcompleted = false;\n\t\t\tgatheringString = false;\n\t\t\tstringEscape = false;  // string stringEscape intro\n\t\t\tcr_escaped = false;   // carraige return escaped\n\t\t\tdate_format = false;\n\t\t\t//stringUnicode = false;  // reading \\u\n\t\t\t//unicodeWide = false;  // reading \\u{} in string\n\t\t\t//stringHex = false;  // reading \\x in string\n\t\t},\n\t\tusePrototype(className,protoType ) { protoTypes[className] = protoType; },\n\t\t/**\n\t\t * Add input to the parser to get parsed.\n\t\t * @param {string} msg \n\t\t */\n\t\twrite(msg) {\n\t\t\tlet retcode;\n\t\t\tif (typeof msg !== \"string\" && typeof msg !== \"undefined\") msg = String(msg);\n\t\t\tif( !status ) throw new Error( \"Parser is still in an error state, please reset before resuming\" );\n\t\t\tfor( retcode = this._write(msg,false); retcode > 0; retcode = this._write() ) {\n\t\t\t\tif( typeof reviver === 'function' ) (function walk(holder, key) {\n\t\t\t\t\tlet k, v, value = holder[key];\n\t\t\t\t\tif (value && typeof value === 'object') {\n\t\t\t\t\t\tfor (k in value) {\n\t\t\t\t\t\t\tif (Object.prototype.hasOwnProperty.call(value, k)) {\n\t\t\t\t\t\t\t\tv = walk(value, k);\n\t\t\t\t\t\t\t\tif (v !== undefined) {\n\t\t\t\t\t\t\t\t\tvalue[k] = v;\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tdelete value[k];\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\treturn reviver.call(holder, key, value);\n\t\t\t\t}({'': result}, ''));\n\t\t\t\tresult = cb( result );\n\n\t\t\t\tif( retcode < 2 )\n\t\t\t\t\tbreak;\n\t\t\t}\n\t\t},\n\t\t/**\n\t\t * Parse a string and return the result.\n\t\t * @param {string} msg\n\t\t * @param {(key:string,value:any)=>any} [reviver]\n\t\t * @returns {any}\n\t\t */\n\t\tparse(msg,reviver) {\n\t\t\tif (typeof msg !== \"string\") msg = String(msg);\n\t\t\tthis.reset();\n\t\t\tconst writeResult = this._write( msg, true );\n\t\t\tif( writeResult > 0 ) {\n\t\t\t\tif( writeResult > 1 ){\n\t\t\t\t\t// probably a carriage return.\n\t\t\t\t\t//console.log( \"Extra data at end of message\");\n\t\t\t\t}\n\t\t\t\tlet result = this.value();\n\t\t\t\tif( ( \"undefined\" === typeof result ) && writeResult > 1 ){\n\t\t\t\t\tthrow new Error( \"Pending value could not complete\");\n\t\t\t\t}\n\t                \n\t\t\t\tresult = typeof reviver === 'function' ? (function walk(holder, key) {\n\t\t\t\t\tlet k, v, value = holder[key];\n\t\t\t\t\tif (value && typeof value === 'object') {\n\t\t\t\t\t\tfor (k in value) {\n\t\t\t\t\t\t\tif (Object.prototype.hasOwnProperty.call(value, k)) {\n\t\t\t\t\t\t\t\tv = walk(value, k);\n\t\t\t\t\t\t\t\tif (v !== undefined) {\n\t\t\t\t\t\t\t\t\tvalue[k] = v;\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tdelete value[k];\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\treturn reviver.call(holder, key, value);\n\t\t\t\t}({'': result}, '')) : result;\n\t\t\t\treturn result;\n\t\t\t}\n\t\t\tthis.finalError();\n\t\t\treturn undefined;\n\n\t\t\t\n\t\t\treturn this.write(msg );\n\t\t},\n\t\t_write(msg,complete_at_end) {\n\t\t\tlet cInt;\n\t\t\tlet input;\n\t\t\tlet buf;\n\t\t\tlet retval = 0;\n\t\t\tfunction throwError( leader, c ) {\n\t\t\t\tthrow new Error( `${leader} '${String.fromCodePoint( c )}' unexpected at ${n} (near '${buf.substr(n>4?(n-4):0,n>4?3:(n-1))}[${String.fromCodePoint( c )}]${buf.substr(n, 10)}') [${pos.line}:${pos.col}]`);\n\t\t\t}\n\n\t\t\tfunction RESET_VAL()  {\n\t\t\t\tval.value_type = VALUE_UNSET;\n\t\t\t\tval.string = '';\n\t\t\t\tval.contains = null;\n\t\t\t\t//val.className = null;\n\t\t\t}\n\n\t\t\tfunction convertValue() {\n\t\t\t\tlet fp = null;\n\t\t\t\t//_DEBUG_PARSING && console.log( \"CONVERT VAL:\", val );\n\t\t\t\tswitch( val.value_type ){\n\t\t\t\tcase VALUE_NUMBER:\n\t\t\t\t\t//1502678337047\n\t\t\t\t\tif( ( ( val.string.length > 13 ) || ( val.string.length == 13 && val[0]>'2' ) )\n\t\t\t\t\t    && !date_format && !exponent_digit && !exponent_sign && !decimal ) {\n\t\t\t\t\t\tisBigInt = true;\n\t\t\t\t\t}\n\t\t\t\t\tif( isBigInt ) { if( hasBigInt ) return BigInt(val.string); else throw new Error( \"no builtin BigInt()\", 0 ) }\n\t\t\t\t\tif( date_format ) { \n\t\t\t\t\t\tconst r = val.string.match(/\\.(\\d\\d\\d\\d*)/ );\n\t\t\t\t\t\tconst frac = ( r )?( r )[1]:null;\n\t\t\t\t\t\tif( !frac || (frac.length < 4) ) {\n\t\t\t\t\t\t\tconst r = new Date( val.string ); \n\t\t\t\t\t\t\tif(isNaN(r.getTime())) throwError( \"Bad Date format\", cInt ); return r;  \n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tlet ns = frac.substr( 3 );\n\t\t\t\t\t\t\twhile( ns.length < 6 ) ns = ns+'0';\n\t\t\t\t\t\t\tconst r = new DateNS( val.string, Number(ns ) ); \n\t\t\t\t\t\t\tif(isNaN(r.getTime())) throwError( \"Bad DateNS format\" + r+r.getTime(), cInt ); return r;  \n\t\t\t\t\t\t}\n\t\t\t\t\t\t//const r = new Date( val.string ); if(isNaN(r.getTime())) throwError( \"Bad number format\", cInt ); return r;  \n\t\t\t\t\t}\n\t\t\t\t\treturn  (negative?-1:1) * Number( val.string );\n\t\t\t\tcase VALUE_STRING:\n\t\t\t\t\tif( val.className ) {\n\t\t\t\t\t\tfp = localFromProtoTypes.get( val.className );\n\t\t\t\t\t\tif( !fp )\n\t\t\t\t\t\t\tfp = fromProtoTypes.get( val.className );\n\t\t\t\t\t\tif( fp && fp.cb ) {\n\t\t\t\t\t\t\tval.className = null;\n\t\t\t\t\t\t\treturn fp.cb.call( val.string );\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// '[object Object]' throws this error.\n\t\t\t\t\t\t\tthrow new Error( \"Double string error, no constructor for: new \" + val.className + \"(\"+val.string+\")\" )\n\t\t\t\t\t\t}\t\n\t\t\t\t\t}\n\t\t\t\t\treturn val.string;\n\t\t\t\tcase VALUE_TRUE:\n\t\t\t\t\treturn true;\n\t\t\t\tcase VALUE_FALSE:\n\t\t\t\t\treturn false;\n\t\t\t\tcase VALUE_NEG_NAN:\n\t\t\t\t\treturn -NaN;\n\t\t\t\tcase VALUE_NAN:\n\t\t\t\t\treturn NaN;\n\t\t\t\tcase VALUE_NEG_INFINITY:\n\t\t\t\t\treturn -Infinity;\n\t\t\t\tcase VALUE_INFINITY:\n\t\t\t\t\treturn Infinity;\n\t\t\t\tcase VALUE_NULL:\n\t\t\t\t\treturn null;\n\t\t\t\tcase VALUE_UNDEFINED:\n\t\t\t\t\treturn undefined;\n\t\t\t\tcase VALUE_EMPTY:\n\t\t\t\t\treturn undefined;\n\t\t\t\tcase VALUE_OBJECT:\n\t\t\t\t\tif( val.className ) { \n\t\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"class reviver\" );\n\t\t\t\t\t\tfp = localFromProtoTypes.get( val.className );\n\t\t\t\t\t\tif( !fp )\n\t\t\t\t\t\t\tfp = fromProtoTypes.get( val.className );\n\t\t\t\t\t\tval.className = null;\n\t\t\t\t\t\tif( fp && fp.cb ) return val.contains = fp.cb.call( val.contains ); \n\t\t\t\t\t}\n\t\t\t\t\treturn val.contains;\n\t\t\t\tcase VALUE_ARRAY:\n\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"Array conversion:\", arrayType, val.contains );\n\t\t\t\t\tif( arrayType >= 0 ) {\n\t\t\t\t\t\tlet ab;\n\t\t\t\t\t\tif( val.contains.length )\n\t\t\t\t\t\t\tab = DecodeBase64( val.contains[0] )\n\t\t\t\t\t\telse ab = DecodeBase64( val.string );\n\t\t\t\t\t\tif( arrayType === 0 ) {\n\t\t\t\t\t\t\tarrayType = -1;\n\t\t\t\t\t\t\treturn ab;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tconst newab = new knownArrayTypes[arrayType]( ab );\n\t\t\t\t\t\t\tarrayType = -1;\n\t\t\t\t\t\t\treturn newab;\n\t\t\t\t\t\t}\n\t\t\t\t\t} else if( arrayType === -2 ) {\n\t\t\t\t\t\tlet obj = rootObject;\n\t\t\t\t\t\t//let ctx = context_stack.first;\n\t\t\t\t\t\tlet lvl;\n\t\t\t\t\t\t//console.log( \"Resolving Reference...\", context_stack.length );\n\t\t\t\t\t\t//console.log( \"--elements and array\", elements );\n\t\t\t\t\t\t\n\t\t\t\t\t\tconst pathlen = val.contains.length;\n\t\t\t\t\t\tfor( lvl = 0; lvl < pathlen; lvl++ ) {\n\t\t\t\t\t\t\tconst idx = val.contains[lvl];\n\t\t\t\t\t\t\t//_DEBUG_REFERENCES && console.log( \"Looking up idx:\", idx, \"of\", val.contains, \"in\", obj );\n\t\t\t\t\t\t\tlet nextObj = obj[idx];\n\n\t\t\t\t\t\t\t//_DEBUG_REFERENCES  && console.log( \"Resolve path:\", lvl, idx,\"in\", obj, context_stack.length, val.contains.toString() );\n\t\t\t\t\t\t\t//_DEBUG_REFERENCES && console.log( \"NEXT OBJECT:\", nextObj );\n\t\t\t\t\t\t\tif( !nextObj ) {\n\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\tlet ctx = context_stack.first;\n\t\t\t\t\t\t\t\t\tlet p = 0;\n\t\t\t\t\t\t\t\t\t//_DEBUG_PARSING_CONTEXT && context_stack.dump();\n\t\t\t\t\t\t\t\t\twhile( ctx && p < pathlen && p < context_stack.length ) {\n\t\t\t\t\t\t\t\t\t\tconst thisKey = val.contains[p];\n\t\t\t\t\t\t\t\t\t\tif( !ctx.next || thisKey !== ctx.next.node.name ) {\n\t\t\t\t\t\t\t\t\t\t\tbreak;  // can't follow context stack any further.... \n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t//_DEBUG_REFERENCES && console.log( \"Checking context:\", obj, \"p=\",p, \"key=\",thisKey, \"ctx(and .next)=\",util.inspect(ctx));\n\t\t\t\t\t\t\t\t\t\t//console.dir(ctx, { depth: null })\n\t\t\t\t\t\t\t\t\t\tif( ctx.next ) {\n\t\t\t\t\t\t\t\t\t\t\tif( \"number\" === typeof thisKey ) {\n\t\t\t\t\t\t\t\t\t\t\t\tconst actualObject = ctx.next.node.elements;\n\t\t\t\t\t\t\t\t\t\t\t\t//_DEBUG_REFERENCES && console.log( \"Number in index... tracing stack...\", obj, actualObject, ctx && ctx.next && ctx.next.next && ctx.next.next.node );\n\n\t\t\t\t\t\t\t\t\t\t\t\tif( actualObject && thisKey >= actualObject.length ) {\n\t\t\t\t\t\t\t\t\t\t\t\t\t//_DEBUG_REFERENCES && console.log( \"AT \", p, actualObject.length, val.contains.length );\n\t\t\t\t\t\t\t\t\t\t\t\t\tif( p === (context_stack.length-1) ) {\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t//_DEBUG_REFERENCES && \n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tconsole.log( \"This is actually at the current object so use that\", p, val.contains, elements );\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tnextObj = elements;\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tp++;\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tctx = ctx.next;\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t//_DEBUG_REFERENCES && console.log( \"is next... \", thisKey, actualObject.length )\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tif( ctx.next.next && thisKey === actualObject.length ) {\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t//_DEBUG_REFERENCES && console.log( \"is next... \")\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tnextObj = ctx.next.next.node.elements;\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tctx = ctx.next;\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tp++;\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tobj = nextObj;\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t//_DEBUG_REFERENCES && console.log( \"FAILING HERE\", ctx.next, ctx.next.next, elements, obj );\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t//_DEBUG_REFERENCES && console.log( \"Nothing after, so this is just THIS?\" );\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tnextObj = elements;\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tp++; // make sure to exit.\n\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t//obj = next\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\t\t\t//_DEBUG_REFERENCES && console.log( \"field AT index\", p,\"of\", val.contains.length );\n\t\t\t\t\t\t\t\t\t\t\t\tif( thisKey !== ctx.next.node.name ){\n\t\t\t\t\t\t\t\t\t\t\t\t\t//_DEBUG_REFERENCES && console.log( \"Expect:\", thisKey, ctx.next.node.name, ctx.next.node.elements );\n\t\t\t\t\t\t\t\t\t\t\t\t\tnextObj = ( ctx.next.node.elements[thisKey] );\n\t\t\t\t\t\t\t\t\t\t\t\t\t//throw new Error( \"Unexpected path-context relationship\" );\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\tlvl = p;\n\t\t\t\t\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\t\t\t\t//_DEBUG_REFERENCES && console.log( \"Updating next object(NEW) to\", ctx.next.node, elements, thisKey)\n\t\t\t\t\t\t\t\t\t\t\t\t\tif( ctx.next.next )\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tnextObj = ctx.next.next.node.elements;\n\t\t\t\t\t\t\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t//_DEBUG_REFERENCES && console.log( \"Nothing after, so this is just THIS?\" );\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tnextObj = elements;\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\t\t//_DEBUG_REFERENCES && console.log( \"using named element from\", ctx.next.node.elements, \"=\", nextObj )\n\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t//if( //_DEBUG_REFERENCES )  {\n\t\t\t\t\t\t\t\t\t\t\t//\tconst a = ctx.next.node.elements;\n\t\t\t\t\t\t\t\t\t\t\t//\tconsole.log( \"Stack Dump:\"\n\t\t\t\t\t\t\t\t\t\t\t//\t\t, a?a.length:a\n\t\t\t\t\t\t\t\t\t\t\t//\t\t, ctx.next.node.name\n\t\t\t\t\t\t\t\t\t\t\t//\t\t, thisKey\n\t\t\t\t\t\t\t\t\t\t\t//\t\t);\n\t\t\t\t\t\t\t\t\t\t\t//}\n\t\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\t\tnextObj = nextObj[thisKey];\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t//_DEBUG_REFERENCES && console.log( \"Doing next context??\", p, context_stack.length, val.contains.length );\n\t\t\t\t\t\t\t\t\t\tctx = ctx.next;\n\t\t\t\t\t\t\t\t\t\tp++;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t//_DEBUG_REFERENCES && console.log( \"Done with context stack...level\", lvl, \"p\", p );\n\t\t\t\t\t\t\t\t\tif( p < pathlen )\n\t\t\t\t\t\t\t\t\t\tlvl = p-1;\n\t\t\t\t\t\t\t\t\telse lvl = p;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t//_DEBUG_REFERENCES && console.log( \"End of processing level:\", lvl );\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif( (\"object\" === typeof nextObj ) && !nextObj ) {\n\t\t\t\t\t\t\t\tthrow new Error( \"Path did not resolve properly:\" +  val.contains + \" at \" + idx + '(' + lvl + ')' );\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tobj = nextObj;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t//_DEBUG_PARSING && console.log( \"Resulting resolved object:\", obj );\n\t\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"SETTING MODE TO -3 (resolved -2)\" );\n\t\t\t\t\t\tarrayType = -3;\n\t\t\t\t\t\treturn obj;\n\t\t\t\t\t}\n\t\t\t\t\tif( val.className ) { \n\t\t\t\t\t\tfp = localFromProtoTypes.get( val.className );\n\t\t\t\t\t\tif( !fp )\n\t\t\t\t\t\t\tfp = fromProtoTypes.get( val.className );\n\t\t\t\t\t\tval.className = null; \n\t\t\t\t\t\tif( fp && fp.cb ) return fp.cb.call( val.contains ); \n\t\t\t\t\t}\n\t\t\t\t\treturn val.contains;\n\t\t\t\tdefault:\n\t\t\t\t\tconsole.log( \"Unhandled value conversion.\", val );\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tfunction arrayPush() {\n\t\t\t\t//_DEBUG_PARSING && console.log( \"PUSH TO ARRAY:\", val );\n\t\t\t\tif( arrayType == -3 )  {\n\t\t\t\t\t//_DEBUG_PARSING && console.log(\" Array type -3?\", val.value_type, elements );\n\t\t\t\t\tif( val.value_type === VALUE_OBJECT ) {\n\t\t\t\t\t\telements.push( val.contains );\n\t\t\t\t\t}\n\t\t\t\t\tarrayType = -1; // next one should be allowed?\n\t\t\t\t\treturn;\n\t\t\t\t} //else\n\t\t\t\t//\tconsole.log( \"Finally a push that's not already pushed!\", );\n\t\t\t\tswitch( val.value_type ){\n\t\t\t\tcase VALUE_EMPTY:\n\t\t\t\t\telements.push( undefined );\n\t\t\t\t\tdelete elements[elements.length-1];\n\t\t\t\t\tbreak;\n\t\t\t\tdefault:\n\t\t\t\t\telements.push( convertValue() );\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t\tRESET_VAL();\n\t\t\t}\n\n\t\t\tfunction objectPush() {\n\t\t\t\tif( arrayType === -3 && val.value_type === VALUE_ARRAY ) {\n\t\t\t\t\t//console.log( \"Array has already been set in object.\" );\n\t\t\t\t\t//elements[val.name] = val.contains;\n\t\t\t\t\tRESET_VAL();\n\t\t\t\t\tarrayType = -1;\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tif( val.value_type === VALUE_EMPTY ) return;\n\t\t\t\tif( !val.name && current_class ) {\n\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"A Stepping current class field:\", current_class_field, val.name );\n\t\t\t\t\tval.name = current_class.fields[current_class_field++];\n\t\t\t\t}\n\t\t\t\tlet value = convertValue();\n\n\t\t\t\tif( current_proto && current_proto.protoDef && current_proto.protoDef.cb ) {\n\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"SOMETHING SHOULD AHVE BEEN REPLACED HERE??\", current_proto );\n\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"(need to do fromprototoypes here) object:\", val, value );\n\t\t\t\t\tvalue = current_proto.protoDef.cb.call( elements, val.name, value );\n\t\t\t\t\tif( value ) elements[val.name] = value;\n\t\t\t\t\t//elements = new current_proto.protoCon( elements );\n\t\t\t\t}else {\n\t\t\t\t        //_DEBUG_PARSING_DETAILS && console.log( \"Default no special class reviver\", val.name, value );\n\t\t\t\t\telements[val.name] = value;\n\t\t\t\t}\n\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"Updated value:\", current_class_field, val.name, elements[val.name] );\n\t\t\t\n\t\t\t\t//_DEBUG_PARSING && console.log( \"+++ Added object field:\", val.name, elements, elements[val.name], rootObject );\n\t\t\t\tRESET_VAL();\n\t\t\t}\n\n\t\t\tfunction recoverIdent(cInt) {\n\t\t\t\t//_DEBUG_PARSING&&console.log( \"Recover Ident char:\", cInt, val, String.fromCodePoint(cInt), \"word:\", word );\n\t\t\t\tif( word !== WORD_POS_RESET ) {\n\t\t\t\t\tif( negative ) { \n\t\t\t\t\t\t//val.string += \"-\"; negative = false; \n\t\t\t\t\t\tthrowError( \"Negative outside of quotes, being converted to a string (would lose count of leading '-' characters)\", cInt );\n\t\t\t\t\t}\n\t\t\t\t\tswitch( word ) {\n\t\t\t\t\tcase WORD_POS_END:\n\t\t\t\t\t\tswitch( val.value_type ) {\n\t\t\t\t\t\tcase VALUE_TRUE:  val.string += \"true\"; break\n\t\t\t\t\t\tcase VALUE_FALSE:  val.string += \"false\"; break\n\t\t\t\t\t\tcase VALUE_NULL:  val.string += \"null\"; break\n\t\t\t\t\t\tcase VALUE_INFINITY:  val.string += \"Infinity\"; break\n\t\t\t\t\t\tcase VALUE_NEG_INFINITY:  val.string += \"-Infinity\"; throwError( \"Negative outside of quotes, being converted to a string\", cInt ); break\n\t\t\t\t\t\tcase VALUE_NAN:  val.string += \"NaN\"; break\n\t\t\t\t\t\tcase VALUE_NEG_NAN:  val.string += \"-NaN\"; throwError( \"Negative outside of quotes, being converted to a string\", cInt ); break\n\t\t\t\t\t\tcase VALUE_UNDEFINED:  val.string += \"undefined\"; break\n\t\t\t\t\t\tcase VALUE_STRING: break;\n\t\t\t\t\t\tcase VALUE_UNSET: break;\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\tconsole.log( \"Value of type \" + val.value_type + \" is not restored...\" );\n\t\t\t\t\t\t}\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase WORD_POS_TRUE_1 :  val.string += \"t\"; break;\n\t\t\t\t\tcase WORD_POS_TRUE_2 :  val.string += \"tr\"; break;\n\t\t\t\t\tcase WORD_POS_TRUE_3 : val.string += \"tru\"; break;\n\t\t\t\t\tcase WORD_POS_FALSE_1 : val.string += \"f\"; break;\n\t\t\t\t\tcase WORD_POS_FALSE_2 : val.string += \"fa\"; break;\n\t\t\t\t\tcase WORD_POS_FALSE_3 : val.string += \"fal\"; break;\n\t\t\t\t\tcase WORD_POS_FALSE_4 : val.string += \"fals\"; break;\n\t\t\t\t\tcase WORD_POS_NULL_1 : val.string += \"n\"; break;\n\t\t\t\t\tcase WORD_POS_NULL_2 : val.string += \"nu\"; break;\n\t\t\t\t\tcase WORD_POS_NULL_3 : val.string += \"nul\"; break;\n\t\t\t\t\tcase WORD_POS_UNDEFINED_1 : val.string += \"u\"; break;\n\t\t\t\t\tcase WORD_POS_UNDEFINED_2 : val.string += \"un\"; break;\n\t\t\t\t\tcase WORD_POS_UNDEFINED_3 : val.string += \"und\"; break;\n\t\t\t\t\tcase WORD_POS_UNDEFINED_4 : val.string += \"unde\"; break;\n\t\t\t\t\tcase WORD_POS_UNDEFINED_5 : val.string += \"undef\"; break;\n\t\t\t\t\tcase WORD_POS_UNDEFINED_6 : val.string += \"undefi\"; break;\n\t\t\t\t\tcase WORD_POS_UNDEFINED_7 : val.string += \"undefin\"; break;\n\t\t\t\t\tcase WORD_POS_UNDEFINED_8 : val.string += \"undefine\"; break;\n\t\t\t\t\tcase WORD_POS_NAN_1 : val.string += \"N\"; break;\n\t\t\t\t\tcase WORD_POS_NAN_2 : val.string += \"Na\"; break;\n\t\t\t\t\tcase WORD_POS_INFINITY_1 : val.string += \"I\"; break;\n\t\t\t\t\tcase WORD_POS_INFINITY_2 : val.string += \"In\"; break;\n\t\t\t\t\tcase WORD_POS_INFINITY_3 : val.string += \"Inf\"; break;\n\t\t\t\t\tcase WORD_POS_INFINITY_4 : val.string += \"Infi\"; break;\n\t\t\t\t\tcase WORD_POS_INFINITY_5 : val.string += \"Infin\"; break;\n\t\t\t\t\tcase WORD_POS_INFINITY_6 : val.string += \"Infini\"; break;\n\t\t\t\t\tcase WORD_POS_INFINITY_7 : val.string += \"Infinit\"; break;\n\t\t\t\t\tcase WORD_POS_RESET : break;\n\t\t\t\t\tcase WORD_POS_FIELD : break;\n\t\t\t\t\tcase WORD_POS_AFTER_FIELD:\n\t\t\t\t\t    //throwError( \"String-keyword recovery fail (after whitespace)\", cInt);\n\t\t\t\t\t    break;\n\t\t\t\t\tcase WORD_POS_AFTER_FIELD_VALUE:\n\t\t\t\t\t    throwError( \"String-keyword recovery fail (after whitespace)\", cInt );\n\t\t\t\t\t    break;\n\t\t\t\t\tdefault:\n\t\t\t\t\t\t//console.log( \"Word context: \" + word + \" unhandled\" );\n\t\t\t\t\t}\n\t\t\t\t\tval.value_type = VALUE_STRING;\t\t\t\t\t\t\t\t\t\n\t\t\t\t\tif( word < WORD_POS_FIELD)\n\t\t\t\t\t    word = WORD_POS_END;\n\t\t\t\t} else {\n\t\t\t\t\tword = WORD_POS_END;\n\t\t\t\t\t//if( val.value_type === VALUE_UNSET && val.string.length )\n\t\t\t\t\t\tval.value_type = VALUE_STRING\n\t\t\t\t}\n\t\t\t\tif( cInt == 123/*'{'*/ )\n\t\t\t\t\topenObject();\n\t\t\t\telse if( cInt == 91/*'['*/ )\n\t\t\t\t\topenArray();\n\t\t\t\telse if( cInt == 44/*','*/ ) {\n\t\t\t\t\t// comma separates the string, it gets consumed.\n\t\t\t\t} else {\n\t\t\t\t\t// ignore white space.\n\t\t\t\t\tif( cInt == 32/*' '*/ || cInt == 13 || cInt == 10 || cInt == 9 || cInt == 0xFEFF || cInt == 0x2028 || cInt == 0x2029 ) {\n\t\t\t\t\t\t//_DEBUG_WHITESPACE && console.log( \"IGNORE WHITESPACE\" );\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tif( cInt == 44/*','*/ || cInt == 125/*'}'*/ || cInt == 93/*']'*/ || cInt == 58/*':'*/ )\n\t\t\t\t\t\tthrowError( \"Invalid character near identifier\", cInt );\n\t\t\t\t\telse //if( typeof cInt === \"number\")\n\t\t\t\t\t\tval.string += str;\n\t\t\t\t}\n\t\t\t\t//console.log( \"VAL STRING IS:\", val.string, str );\n\t\t\t}\n\n\t\t\t// gather a string from an input stream; start_c is the opening quote to find a related close quote.\n\t\t\tfunction gatherString( start_c ) {\n\t\t\t\tlet retval = 0;\n\t\t\t\twhile( retval == 0 && ( n < buf.length ) ) {\n\t\t\t\t\tstr = buf.charAt(n);\n\t\t\t\t\tlet cInt = buf.codePointAt(n++);\n\t\t\t\t\tif( cInt >= 0x10000 ) { str += buf.charAt(n); n++; }\n\t\t\t\t\t//console.log( \"gathering....\", stringEscape, str, cInt, unicodeWide, stringHex, stringUnicode, hex_char_len );\n\t\t\t\t\tpos.col++;\n\t\t\t\t\tif( cInt == start_c ) { //( cInt == 34/*'\"'*/ ) || ( cInt == 39/*'\\''*/ ) || ( cInt == 96/*'`'*/ ) )\n\t\t\t\t\t\tif( stringEscape ) { \n\t\t\t\t\t\t\tif( stringHex )\n\t\t\t\t\t\t\t\tthrowError( \"Incomplete hexidecimal sequence\", cInt );\n\t\t\t\t\t\t\telse if( stringUnicode )\n\t\t\t\t\t\t\t\tthrowError( \"Incomplete long unicode sequence\", cInt );\n\t\t\t\t\t\t\telse if( unicodeWide )\n\t\t\t\t\t\t\t\tthrowError( \"Incomplete unicode sequence\", cInt );\n\t\t\t\t\t\t\tif( cr_escaped ) {\n\t\t\t\t\t\t\t\tcr_escaped = false;\n\t\t\t\t\t\t\t\tretval = 1; // complete string, escaped \\r\n\t\t\t\t\t\t\t} else val.string += str;\n\t\t\t\t\t\t\tstringEscape = false; }\n\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t// quote matches, and is not processing an escape sequence.\n\t\t\t\t\t\t\tretval = 1;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\telse if( stringEscape ) {\n\t\t\t\t\t\tif( unicodeWide ) {\n\t\t\t\t\t\t\tif( cInt == 125/*'}'*/ ) {\n\t\t\t\t\t\t\t\tval.string += String.fromCodePoint( hex_char );\n\t\t\t\t\t\t\t\tunicodeWide = false;\n\t\t\t\t\t\t\t\tstringUnicode = false;\n\t\t\t\t\t\t\t\tstringEscape = false;\n\t\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\thex_char *= 16;\n\t\t\t\t\t\t\tif( cInt >= 48/*'0'*/ && cInt <= 57/*'9'*/ )      hex_char += cInt - 0x30;\n\t\t\t\t\t\t\telse if( cInt >= 65/*'A'*/ && cInt <= 70/*'F'*/ ) hex_char += ( cInt - 65 ) + 10;\n\t\t\t\t\t\t\telse if( cInt >= 97/*'a'*/ && cInt <= 102/*'f'*/ ) hex_char += ( cInt - 97 ) + 10;\n\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\tthrowError( \"(escaped character, parsing hex of \\\\u)\", cInt );\n\t\t\t\t\t\t\t\tretval = -1;\n\t\t\t\t\t\t\t\tunicodeWide = false;\n\t\t\t\t\t\t\t\tstringEscape = false;\n\t\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse if( stringHex || stringUnicode ) {\n\t\t\t\t\t\t\tif( hex_char_len === 0 && cInt === 123/*'{'*/ ) {\n\t\t\t\t\t\t\t\tunicodeWide = true;\n\t\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif( hex_char_len < 2 || ( stringUnicode && hex_char_len < 4 ) ) {\n\t\t\t\t\t\t\t\thex_char *= 16;\n\t\t\t\t\t\t\t\tif( cInt >= 48/*'0'*/ && cInt <= 57/*'9'*/ )      hex_char += cInt - 0x30;\n\t\t\t\t\t\t\t\telse if( cInt >= 65/*'A'*/ && cInt <= 70/*'F'*/ ) hex_char += ( cInt - 65 ) + 10;\n\t\t\t\t\t\t\t\telse if( cInt >= 97/*'a'*/ && cInt <= 102/*'f'*/ ) hex_char += ( cInt - 97 ) + 10;\n\t\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\t\tthrowError( stringUnicode?\"(escaped character, parsing hex of \\\\u)\":\"(escaped character, parsing hex of \\\\x)\", cInt );\n\t\t\t\t\t\t\t\t\tretval = -1;\n\t\t\t\t\t\t\t\t\tstringHex = false;\n\t\t\t\t\t\t\t\t\tstringEscape = false;\n\t\t\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\thex_char_len++;\n\t\t\t\t\t\t\t\tif( stringUnicode ) {\n\t\t\t\t\t\t\t\t\tif( hex_char_len == 4 ) {\n\t\t\t\t\t\t\t\t\t\tval.string += String.fromCodePoint( hex_char );\n\t\t\t\t\t\t\t\t\t\tstringUnicode = false;\n\t\t\t\t\t\t\t\t\t\tstringEscape = false;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\telse if( hex_char_len == 2 ) {\n\t\t\t\t\t\t\t\t\tval.string += String.fromCodePoint( hex_char );\n\t\t\t\t\t\t\t\t\tstringHex = false;\n\t\t\t\t\t\t\t\t\tstringEscape = false;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tswitch( cInt ) {\n\t\t\t\t\t\tcase 13/*'\\r'*/:\n\t\t\t\t\t\t\tcr_escaped = true;\n\t\t\t\t\t\t\tpos.col = 1;\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\tcase 0x2028: // LS (Line separator)\n\t\t\t\t\t\tcase 0x2029: // PS (paragraph separate)\n\t\t\t\t\t\t\tpos.col = 1;\n\t\t\t\t\t\t\t// falls through\n\t\t\t\t\t\tcase 10/*'\\n'*/:\n\t\t\t\t\t\t\tif( !cr_escaped ) { // \\\\ \\n\n\t\t\t\t\t\t\t\tpos.col = 1;\n\t\t\t\t\t\t\t} else { // \\\\ \\r \\n\n\t\t\t\t\t\t\t\tcr_escaped = false;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tpos.line++;\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 116/*'t'*/:\n\t\t\t\t\t\t\tval.string += '\\t';\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 98/*'b'*/:\n\t\t\t\t\t\t\tval.string += '\\b';\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 110/*'n'*/:\n\t\t\t\t\t\t\tval.string += '\\n';\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 114/*'r'*/:\n\t\t\t\t\t\t\tval.string += '\\r';\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 102/*'f'*/:\n\t\t\t\t\t\t\tval.string += '\\f';\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 118/*'v'*/:\n\t\t\t\t\t\t\tval.string += '\\v';\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 48/*'0'*/: \n\t\t\t\t\t\t\tval.string += '\\0';\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 120/*'x'*/:\n\t\t\t\t\t\t\tstringHex = true;\n\t\t\t\t\t\t\thex_char_len = 0;\n\t\t\t\t\t\t\thex_char = 0;\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\tcase 117/*'u'*/:\n\t\t\t\t\t\t\tstringUnicode = true;\n\t\t\t\t\t\t\thex_char_len = 0;\n\t\t\t\t\t\t\thex_char = 0;\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t//case 47/*'/'*/:\n\t\t\t\t\t\t//case 92/*'\\\\'*/:\n\t\t\t\t\t\t//case 34/*'\"'*/:\n\t\t\t\t\t\t//case 39/*\"'\"*/:\n\t\t\t\t\t\t//case 96/*'`'*/:\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\tval.string += str;\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t//console.log( \"other...\" );\n\t\t\t\t\t\tstringEscape = false;\n\t\t\t\t\t}\n\t\t\t\t\telse if( cInt === 92/*'\\\\'*/ ) {\n\t\t\t\t\t\tif( stringEscape ) {\n\t\t\t\t\t\t\tval.string += '\\\\';\n\t\t\t\t\t\t\tstringEscape = false\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse {\n\t\t\t\t\t\t\tstringEscape = true;\n\t\t\t\t\t\t\thex_char = 0;\n\t\t\t\t\t\t\thex_char_len = 0;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\telse { /* any other character */\n\t\t\t\t\t\tif( cr_escaped ) {\n\t\t\t\t\t\t\t// \\\\ \\r <any char>\n\t\t\t\t\t\t\tcr_escaped = false;\n\t\t\t\t\t\t\tpos.line++;\n\t\t\t\t\t\t\tpos.col = 2; // this character is pos 1; and increment to be after it.\n\t\t\t\t\t\t}\n\t\t\t\t\t\tval.string += str;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn retval;\n\t\t\t}\n\n\t\t\t// gather a number from the input stream.\n\t\t\tfunction collectNumber() {\n\t\t\t\tlet _n;\n\t\t\t\twhile( (_n = n) < buf.length ) {\n\t\t\t\t\tstr = buf.charAt(_n);\n\t\t\t\t\tlet cInt = buf.codePointAt(n++);\n\t\t\t\t\tif( cInt >= 256 ) { \n\t\t\t\t\t\t\tpos.col -= n - _n;\n\t\t\t\t\t\t\tn = _n; // put character back in queue to process.\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t} else {\n\t\t\t\t\t\t//_DEBUG_PARSING_NUMBERS  && console.log( \"in getting number:\", n, cInt, String.fromCodePoint(cInt) );\n\t\t\t\t\t\tif( cInt == 95 /*_*/ )\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\tpos.col++;\n\t\t\t\t\t\t// leading zeros should be forbidden.\n\t\t\t\t\t\tif( cInt >= 48/*'0'*/ && cInt <= 57/*'9'*/ ) {\n\t\t\t\t\t\t\tif( exponent ) {\n\t\t\t\t\t\t\t\texponent_digit = true;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tval.string += str;\n\t\t\t\t\t\t} else if( cInt == 45/*'-'*/ || cInt == 43/*'+'*/ ) {\n\t\t\t\t\t\t\tif( val.string.length == 0 || ( exponent && !exponent_sign && !exponent_digit ) ) {\n\t\t\t\t\t\t\t\tif( cInt == 45/*'-'*/ && !exponent ) negative = !negative;\n\t\t\t\t\t\t\t\tval.string += str;\n\t\t\t\t\t\t\t\texponent_sign = true;\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tif( negative ) { val.string = '-' + val.string; negative = false; }\n\t\t\t\t\t\t\t\tval.string += str;\n\t\t\t\t\t\t\t\tdate_format = true;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else if( cInt == 78/*'N'*/ ) {\n\t\t\t\t\t\t\tif( word == WORD_POS_RESET ) {\n\t\t\t\t\t\t\t\tgatheringNumber = false;\n\t\t\t\t\t\t\t\tword = WORD_POS_NAN_1;\n\t\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tthrowError( \"fault while parsing number;\", cInt );\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t} else if( cInt == 73/*'I'*/ ) {\n\t\t\t\t\t\t\tif( word == WORD_POS_RESET ) {\n\t\t\t\t\t\t\t\tgatheringNumber = false;\n\t\t\t\t\t\t\t\tword = WORD_POS_INFINITY_1;\n\t\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tthrowError( \"fault while parsing number;\", cInt );\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t} else if( cInt == 58/*':'*/ && date_format ) {\n\t\t\t\t\t\t\tif( negative ) { val.string = '-' + val.string; negative = false; }\n\t\t\t\t\t\t\tval.string += str;\n\t\t\t\t\t\t\tdate_format = true;\n\t\t\t\t\t\t} else if( cInt == 84/*'T'*/ && date_format ) {\n\t\t\t\t\t\t\tif( negative ) { val.string = '-' + val.string; negative = false; }\n\t\t\t\t\t\t\tval.string += str;\n\t\t\t\t\t\t\tdate_format = true;\n\t\t\t\t\t\t} else if( cInt == 90/*'Z'*/ && date_format ) {\n\t\t\t\t\t\t\tif( negative ) { val.string = '-' + val.string; negative = false; }\n\t\t\t\t\t\t\tval.string += str;\n\t\t\t\t\t\t\tdate_format = true;\n\t\t\t\t\t\t} else if( cInt == 46/*'.'*/ ) {\n\t\t\t\t\t\t\tif( !decimal && !fromHex && !exponent ) {\n\t\t\t\t\t\t\t\tval.string += str;\n\t\t\t\t\t\t\t\tdecimal = true;\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tstatus = false;\n\t\t\t\t\t\t\t\tthrowError( \"fault while parsing number;\", cInt );\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else if( cInt == 110/*'n'*/ ) {\n\t\t\t\t\t\t\tisBigInt = true;\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t} else if( fromHex && ( ( ( cInt >= 95/*'a'*/ ) && ( cInt <= 102/*'f'*/ ) ) ||\n\t\t\t\t\t\t           ( ( cInt >= 65/*'A'*/ ) && ( cInt <= 70/*'F'*/ ) ) ) ) {\n\t\t\t\t\t\t\tval.string += str;\n\t\t\t\t\t\t} else if( cInt == 120/*'x'*/ || cInt == 98/*'b'*/ || cInt == 111/*'o'*/\n\t\t\t\t\t\t\t\t|| cInt == 88/*'X'*/ || cInt == 66/*'B'*/ || cInt == 79/*'O'*/ ) {\n\t\t\t\t\t\t\t// hex conversion.\n\t\t\t\t\t\t\tif( !fromHex && val.string == '0' ) {\n\t\t\t\t\t\t\t\tfromHex = true;\n\t\t\t\t\t\t\t\tval.string += str;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\tstatus = false;\n\t\t\t\t\t\t\t\tthrowError( \"fault while parsing number;\", cInt );\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else if( ( cInt == 101/*'e'*/ ) || ( cInt == 69/*'E'*/ ) ) {\n\t\t\t\t\t\t\tif( !exponent ) {\n\t\t\t\t\t\t\t\tval.string += str;\n\t\t\t\t\t\t\t\texponent = true;\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tstatus = false;\n\t\t\t\t\t\t\t\tthrowError( \"fault while parsing number;\", cInt );\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tif( cInt == 32/*' '*/ || cInt == 13 || cInt == 10 || cInt == 9 || cInt == 47/*'/'*/ || cInt ==  35/*'#'*/\n\t\t\t\t\t\t\t || cInt == 44/*','*/ || cInt == 125/*'}'*/ || cInt == 93/*']'*/\n\t\t\t\t\t\t\t || cInt == 123/*'{'*/ || cInt == 91/*'['*/ || cInt == 34/*'\"'*/ || cInt == 39/*'''*/ || cInt == 96/*'`'*/\n\t\t\t\t\t\t\t || cInt == 58/*':'*/ ) {\n\t\t\t\t\t\t\t\tpos.col -= n - _n;\n\t\t\t\t\t\t\t\tn = _n; // put character back in queue to process.\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\tif( complete_at_end ) {\n\t\t\t\t\t\t\t\t\tstatus = false;\n\t\t\t\t\t\t\t\t\tthrowError( \"fault while parsing number;\", cInt );\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif( (!complete_at_end) && n == buf.length ) {\n\t\t\t\t\tgatheringNumber = true;\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tgatheringNumber = false;\n\t\t\t\t\tval.value_type = VALUE_NUMBER;\n\t\t\t\t\tif( parse_context == CONTEXT_UNKNOWN ) {\n\t\t\t\t\t\tcompleted = true;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// begin parsing an object type\n\t\t\tfunction openObject() {\n\t\t\t\tlet nextMode = CONTEXT_OBJECT_FIELD;\n\t\t\t\tlet cls = null;\n\t\t\t\tlet tmpobj = {};\n\t\t\t\t//_DEBUG_PARSING && console.log( \"opening object:\", val.string, val.value_type, word, parse_context );\n\t\t\t\tif( word > WORD_POS_RESET && word < WORD_POS_FIELD )\n\t\t\t\t\trecoverIdent( 123 /* '{' */ );\n\t\t\t\tlet protoDef;\n\t\t\t\tprotoDef = getProto(); // lookup classname using val.string and get protodef(if any)\n\t\t\t\tif( parse_context == CONTEXT_UNKNOWN ) {\n\t\t\t\t\tif( word == WORD_POS_FIELD /*|| word == WORD_POS_AFTER_FIELD*/ \n\t\t\t\t\t   || word == WORD_POS_END\n\t\t\t\t\t     && ( protoDef || val.string.length ) ) {\n\t\t\t\t\t\t\tif( protoDef && protoDef.protoDef && protoDef.protoDef.protoCon ) {\n\t\t\t\t\t\t\t\ttmpobj = new protoDef.protoDef.protoCon();\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\tif( !protoDef || !protoDef.protoDef && val.string ) // class creation is redundant...\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tcls = classes.find( cls=>cls.name===val.string );\n\t\t\t\t\t\t\t//console.log( \"Probably creating the Macro-Tag here?\", cls )\n\t\t\t\t\t\t\tif( !cls ) {\n\t\t\t\t\t\t\t\t/* eslint-disable no-inner-declarations */\n\t\t\t\t\t\t\t\tfunction privateProto() {} \n\t\t\t\t\t\t\t\t// this just uses the tmpobj {} container to store the values collected for this class...\n\t\t\t\t\t\t\t\t// this does not generate the instance of the class.\n\t\t\t\t\t\t\t\t// if this tag type is also a prototype, use that prototype, else create a unique proto\n\t\t\t\t\t\t\t\t// for this tagged class type.\n\t\t\t\t\t\t\t\tclasses.push( cls = { name : val.string\n\t\t\t\t\t\t\t\t, protoCon: (protoDef && protoDef.protoDef && protoDef.protoDef.protoCon) || privateProto.constructor\n\t\t\t\t\t\t\t\t , fields : [] } );\n\t\t\t\t\t\t\t\t nextMode = CONTEXT_CLASS_FIELD;\n\t\t\t\t\t\t\t} else if( redefineClass ) {\n\t\t\t\t\t\t\t\t//_DEBUG_PARSING && console.log( \"redefine class...\" );\n\t\t\t\t\t\t\t\t// redefine this class\n\t\t\t\t\t\t\t\tcls.fields.length = 0;\n\t\t\t\t\t\t\t\tnextMode = CONTEXT_CLASS_FIELD;\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t//_DEBUG_PARSING && console.log( \"found existing class, using it....\");\n\t\t\t\t\t\t\t\ttmpobj = new cls.protoCon();\n\t\t\t\t\t\t\t\t//tmpobj = Object.assign( tmpobj, cls.protoObject );\n\t\t\t\t\t\t\t\t//Object.setPrototypeOf( tmpobj, Object.getPrototypeOf( cls.protoObject ) );\n\t\t\t\t\t\t\t\tnextMode = CONTEXT_CLASS_VALUE;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tredefineClass = false;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tcurrent_class = cls\n\t\t\t\t\t\tword = WORD_POS_RESET;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tword = WORD_POS_FIELD;\n\t\t\t\t\t}\n\t\t\t\t} else if( word == WORD_POS_FIELD /*|| word == WORD_POS_AFTER_FIELD*/ \n\t\t\t\t\t\t|| parse_context === CONTEXT_IN_ARRAY \n\t\t\t\t\t\t|| parse_context === CONTEXT_OBJECT_FIELD_VALUE \n\t\t\t\t\t\t|| parse_context == CONTEXT_CLASS_VALUE ) {\n\t\t\t\t\tif( word != WORD_POS_RESET || val.value_type == VALUE_STRING ) {\n\t\t\t\t\t\tif( protoDef && protoDef.protoDef ) {\n\t\t\t\t\t\t\t// need to collect the object,\n\t\t\t\t\t\t\ttmpobj = new protoDef.protoDef.protoCon();\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// look for a class type (shorthand) to recover.\n\t\t\t\t\t\t\tcls = classes.find( cls=>cls.name === val.string );\n\t\t\t\t\t\t\tif( !cls )\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t/* eslint-disable no-inner-declarations */\n\t\t\t\t\t\t\t   function privateProto(){}\n\t\t\t\t\t\t\t\t//sconsole.log( \"privateProto has no proto?\", privateProto.prototype.constructor.name );\n\t\t\t\t\t\t\t\tlocalFromProtoTypes.set( val.string,\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{ protoCon:privateProto.prototype.constructor\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t, cb: null }\n\t\t\t\t\t\t\t\t\t\t\t\t\t   );\n\t\t\t\t\t\t\t\ttmpobj = new privateProto();\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\tnextMode = CONTEXT_CLASS_VALUE;\n\t\t\t\t\t\t\t\ttmpobj = {};\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\t//nextMode = CONTEXT_CLASS_VALUE;\n\t\t\t\t\t\tword = WORD_POS_RESET;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tword = WORD_POS_RESET;\n\t\t\t\t\t}\n\t\t\t\t} else if( ( parse_context == CONTEXT_OBJECT_FIELD && word == WORD_POS_RESET ) ) {\n\t\t\t\t\tthrowError( \"fault while parsing; getting field name unexpected \", cInt );\n\t\t\t\t\tstatus = false;\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\n\t\t\t\t// common code to push into next context\n\t\t\t\tlet old_context = getContext();\n\t\t\t\t//_DEBUG_PARSING && console.log( \"Begin a new object; previously pushed into elements; but wait until trailing comma or close previously \", val.value_type, val.className );\n\n\t\t\t\tval.value_type = VALUE_OBJECT;\n\t\t\t\tif( parse_context === CONTEXT_UNKNOWN ){\n\t\t\t\t\telements = tmpobj;\n\t\t\t\t} else if( parse_context == CONTEXT_IN_ARRAY ) {\n\t\t\t\t\tif( arrayType == -1 ) {\n\t\t\t\t\t\t// this is pushed later... \n\t\t\t\t\t\t//console.log( \"PUSHING OPEN OBJECT INTO EXISTING ARRAY - THIS SHOULD BE RE-SET?\", JSOX.stringify(context_stack.first.node) );\n\t\t\t\t\t\t//elements.push( tmpobj );\n\t\t\t\t\t}\n\t\t\t\t\tval.name = elements.length;\n\t\t\t\t\t//else if( //_DEBUG_PARSING && arrayType !== -3 )\n\t\t\t\t\t//\tconsole.log( \"This is an invalid parsing state, typed array with sub-object elements\" );\n\t\t\t\t} else if( parse_context == CONTEXT_OBJECT_FIELD_VALUE || parse_context == CONTEXT_CLASS_VALUE ) {\n\t\t\t\t\tif( !val.name && current_class ){\n\t\t\t\t\t\tval.name = current_class.fields[current_class_field++];\n\t\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"B Stepping current class field:\", val, current_class_field, val.name );\n\t\t\t\t\t}\n\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"Setting element:\", val.name, tmpobj );\n\t\t\t\t\telements[val.name] = tmpobj;\n\t\t\t\t}\n\n\t\t\t\told_context.context = parse_context;\n\t\t\t\told_context.elements = elements;\n\t\t\t\t//old_context.element_array = element_array;\n\t\t\t\told_context.name = val.name;\n\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"pushing val.name:\", val.name, arrayType );\n\t\t\t\told_context.current_proto = current_proto;\n\t\t\t\told_context.current_class = current_class;\n\t\t\t\told_context.current_class_field = current_class_field;\n\t\t\t\told_context.valueType = val.value_type;\n\t\t\t\told_context.arrayType = arrayType; // pop that we don't want to have this value re-pushed.\n\t\t\t\told_context.className = val.className;\n\t\t\t\t//arrayType = -3; // this doesn't matter, it's an object state, and a new array will reset to -1\n\t\t\t\tval.className = null;\n\t\t\t\tval.name = null;\n\t\t\t\tcurrent_proto = protoDef;\n\t\t\t\tcurrent_class = cls;\n\t\t\t\t//console.log( \"Setting current class:\", current_class.name );\n\t\t\t\tcurrent_class_field = 0;\n\t\t\t\telements = tmpobj;\n\t\t\t\tif( !rootObject ) rootObject = elements;\n\t\t\t\t//_DEBUG_PARSING_STACK && console.log( \"push context (open object): \", context_stack.length, \" new mode:\", nextMode );\n\t\t\t\tcontext_stack.push( old_context );\n\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"RESET OBJECT FIELD\", old_context, context_stack );\n\t\t\t\tRESET_VAL();\n\t\t\t\tparse_context = nextMode;\n\t\t\t\treturn true;\n\t\t\t}\n\n\t\t\tfunction openArray() {\n\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"openArray()...\" );\n\t\t\t\tif( word > WORD_POS_RESET && word < WORD_POS_FIELD )\n\t\t\t\t\trecoverIdent( 91 );\n\n\t\t\t\tif( word == WORD_POS_END && val.string.length ) {\n\t\t\t\t\t//_DEBUG_PARSING && console.log( \"recover arrayType:\", arrayType, val.string );\n\t\t\t\t\tlet typeIndex = knownArrayTypeNames.findIndex( type=>(type === val.string) );\n\t\t\t\t\tword = WORD_POS_RESET;\n\t\t\t\t\tif( typeIndex >= 0 ) {\n\t\t\t\t\t\tarrayType = typeIndex;\n\t\t\t\t\t\tval.className = val.string;\n\t\t\t\t\t\tval.string = null;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tif( val.string === \"ref\" ) {\n\t\t\t\t\t\t\tval.className = null;\n\t\t\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"This will be a reference recovery for key:\", val );\n\t\t\t\t\t\t\tarrayType = -2;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tif( localFromProtoTypes.get( val.string ) ) {\n\t\t\t\t\t\t\t\tval.className = val.string;\n\t\t\t\t\t\t\t} \n\t\t\t\t\t\t\telse if( fromProtoTypes.get( val.string ) ) {\n\t\t\t\t\t\t\t\tval.className = val.string;\n\t\t\t\t\t\t\t} else\n\t\t\t\t\t\t\t\tthrowError( `Unknown type '${val.string}' specified for array`, cInt );\n\t\t\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \" !!!!!A Set Classname:\", val.className );\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} else if( parse_context == CONTEXT_OBJECT_FIELD || word == WORD_POS_FIELD || word == WORD_POS_AFTER_FIELD ) {\n\t\t\t\t\tthrowError( \"Fault while parsing; while getting field name unexpected\", cInt );\n\t\t\t\t\tstatus = false;\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\t{\n\t\t\t\t\tlet old_context = getContext();\n\t\t\t\t\t//_DEBUG_PARSING && console.log( \"Begin a new array; previously pushed into elements; but wait until trailing comma or close previously \", val.value_type );\n\n\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"Opening array:\", val, parse_context );\n\t\t\t\t\tval.value_type = VALUE_ARRAY;\n\t\t\t\t\tlet tmparr = [];\n\t\t\t\t\tif( parse_context == CONTEXT_UNKNOWN )\n\t\t\t\t\t\telements = tmparr;\n\t\t\t\t\telse if( parse_context == CONTEXT_IN_ARRAY ) {\n\t\t\t\t\t\tif( arrayType == -1 ){\n\t\t\t\t\t\t\t//console.log( \"Pushing new opening array into existing array already RE-SET\" );\n\t\t\t\t\t\t\telements.push( tmparr );\n\t\t\t\t\t\t} //else if( //_DEBUG_PARSING && arrayType !== -3 )\n\t\t\t\t\t\tval.name = elements.length;\n\t\t\t\t\t\t//\tconsole.log( \"This is an invalid parsing state, typed array with sub-array elements\" );\n\t\t\t\t\t} else if( parse_context == CONTEXT_OBJECT_FIELD_VALUE ) {\n\t\t\t\t\t\tif( !val.name ) {\n\t\t\t\t\t\t\tconsole.log( \"This says it's resolved.......\" );\n\t\t\t\t\t\t\tarrayType = -3;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif( current_proto && current_proto.protoDef ) {\n\t\t\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"SOMETHING SHOULD HAVE BEEN REPLACED HERE??\", current_proto );\n\t\t\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"(need to do fromprototoypes here) object:\", val, value );\n\t\t\t\t\t\t\tif( current_proto.protoDef.cb ){\n\t\t\t\t\t\t\t\tconst newarr = current_proto.protoDef.cb.call( elements, val.name, tmparr );\n\t\t\t\t\t\t\t\tif( newarr !== undefined ) tmparr = elements[val.name] = newarr;\n\t\t\t\t\t\t\t\t//else console.log( \"Warning: Received undefined for an array; keeping original array, not setting field\" );\n\t\t\t\t\t\t\t}else\n\t\t\t\t\t\t\t\telements[val.name] = tmparr;\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse\n\t\t\t\t\t\t\telements[val.name] = tmparr;\n\t\t\t\t\t}\n\t\t\t\t\told_context.context = parse_context;\n\t\t\t\t\told_context.elements = elements;\n\t\t\t\t\t//old_context.element_array = element_array;\n\t\t\t\t\told_context.name = val.name;\n\t\t\t\t\told_context.current_proto = current_proto;\n\t\t\t\t\told_context.current_class = current_class;\n\t\t\t\t\told_context.current_class_field = current_class_field;\n\t\t\t\t\t// already pushed?\n\t\t\t\t\told_context.valueType = val.value_type;\n\t\t\t\t\told_context.arrayType = (arrayType==-1)?-3:arrayType; // pop that we don't want to have this value re-pushed.\n\t\t\t\t\told_context.className = val.className;\n\t\t\t\t\tarrayType = -1;\n\t\t\t\t\tval.className = null;\n\n\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \" !!!!!B Clear Classname:\", old_context, val.className, old_context.className, old_context.name );\n\t\t\t\t\tval.name = null;\n\t\t\t\t\tcurrent_proto = null;\n\t\t\t\t\tcurrent_class = null;\n\t\t\t\t\tcurrent_class_field = 0;\n\t\t\t\t\t//element_array = tmparr;\n\t\t\t\t\telements = tmparr;\n\t\t\t\t\tif( !rootObject ) rootObject = tmparr;\n\t\t\t\t\t//_DEBUG_PARSING_STACK && console.log( \"push context (open array): \", context_stack.length );\n\t\t\t\t\tcontext_stack.push( old_context );\n\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"RESET ARRAY FIELD\", old_context, context_stack );\n\n\t\t\t\t\tRESET_VAL();\n\t\t\t\t\tparse_context = CONTEXT_IN_ARRAY;\n\t\t\t\t}\n\t\t\t\treturn true;\n\t\t\t}\n\n\t\t\tfunction getProto() {\n\t\t\t\tconst result = {protoDef:null,cls:null};\n\t\t\t\tif( ( result.protoDef = localFromProtoTypes.get( val.string ) ) ) {\n\t\t\t\t\tif( !val.className ){\n\t\t\t\t\t\tval.className = val.string;\n\t\t\t\t\t\tval.string = null;\n\t\t\t\t\t}\n\t\t\t\t\t// need to collect the object, \n\t\t\t\t}\n\t\t\t\telse if( ( result.protoDef = fromProtoTypes.get( val.string ) ) ) {\n\t\t\t\t\tif( !val.className ){\n\t\t\t\t\t\tval.className = val.string;\n\t\t\t\t\t\tval.string = null;\n\t\t\t\t\t}\n\t\t\t\t} \n\t\t\t\tif( val.string )\n\t\t\t\t{\n\t\t\t\t\tresult.cls = classes.find( cls=>cls.name === val.string );\n\t\t\t\t\tif( !result.protoDef && !result.cls ) {\n\t\t\t\t\t    // this will creaet a class def with a new proto to cover when we don't KNOW.\n\t\t\t\t\t    //throwError( \"Referenced class \" + val.string + \" has not been defined\", cInt );\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn (result.protoDef||result.cls)?result:null;\n\t\t\t}\n\n\t\t\tif( !status )\n\t\t\t\treturn -1;\n\n\t\t\tif( msg && msg.length ) {\n\t\t\t\tinput = getBuffer();\n\t\t\t\tinput.buf = msg;\n\t\t\t\tinQueue.push( input );\n\t\t\t} else {\n\t\t\t\tif( gatheringNumber ) {\n\t\t\t\t\t//console.log( \"Force completed.\")\n\t\t\t\t\tgatheringNumber = false;\n\t\t\t\t\tval.value_type = VALUE_NUMBER;\n\t\t\t\t\tif( parse_context == CONTEXT_UNKNOWN ) {\n\t\t\t\t\t\tcompleted = true;\n\t\t\t\t\t}\n\t\t\t\t\tretval = 1;  // if returning buffers, then obviously there's more in this one.\n\t\t\t\t}\n\t\t\t\tif( parse_context !== CONTEXT_UNKNOWN )\n\t\t\t\t\tthrowError( \"Unclosed object at end of stream.\", cInt );\n\t\t\t}\n\n\t\t\twhile( status && ( input = inQueue.shift() ) ) {\n\t\t\t\tn = input.n;\n\t\t\t\tbuf = input.buf;\n\t\t\t\tif( gatheringString ) {\n\t\t\t\t\tlet string_status = gatherString( gatheringStringFirstChar );\n\t\t\t\t\tif( string_status < 0 )\n\t\t\t\t\t\tstatus = false;\n\t\t\t\t\telse if( string_status > 0 ) {\n\t\t\t\t\t\tgatheringString = false;\n\t\t\t\t\t\tif( status ) val.value_type = VALUE_STRING;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif( gatheringNumber ) {\n\t\t\t\t\tcollectNumber();\n\t\t\t\t}\n\n\t\t\t\twhile( !completed && status && ( n < buf.length ) ) {\n\t\t\t\t\tstr = buf.charAt(n);\n\t\t\t\t\tcInt = buf.codePointAt(n++);\n\t\t\t\t\tif( cInt >= 0x10000 ) { str += buf.charAt(n); n++; }\n\t\t\t\t\t//_DEBUG_PARSING && console.log( \"parsing at \", cInt, str );\n\t\t\t\t\t//_DEBUG_LL && console.log( \"processing: \", cInt, n, str, pos, comment, parse_context, word );\n\t\t\t\t\tpos.col++;\n\t\t\t\t\tif( comment ) {\n\t\t\t\t\t\tif( comment == 1 ) {\n\t\t\t\t\t\t\tif( cInt == 42/*'*'*/ ) comment = 3;\n\t\t\t\t\t\t\telse if( cInt != 47/*'/'*/ ) return throwError( \"fault while parsing;\", cInt );\n\t\t\t\t\t\t\telse comment = 2;\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse if( comment == 2 ) {\n\t\t\t\t\t\t\tif( cInt == 10/*'\\n'*/ || cInt == 13/*'\\r'*/  ) comment = 0;\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse if( comment == 3 ) {\n\t\t\t\t\t\t\tif( cInt == 42/*'*'*/ ) comment = 4;\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse {\n\t\t\t\t\t\t\tif( cInt == 47/*'/'*/ ) comment = 0;\n\t\t\t\t\t\t\telse comment = 3;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t\tswitch( cInt ) {\n\t\t\t\t\tcase 35/*'#'*/:\n\t\t\t\t\t\tcomment = 2; // pretend this is the second slash.\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 47/*'/'*/:\n\t\t\t\t\t\tcomment = 1;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 123/*'{'*/:\n\t\t\t\t\t\topenObject();\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 91/*'['*/:\n\t\t\t\t\t\topenArray();\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 58/*':'*/:\n\t\t\t\t\t\t//_DEBUG_PARSING && console.log( \"colon received...\")\n\t\t\t\t\t\tif( parse_context == CONTEXT_CLASS_VALUE ) {\n\t\t\t\t\t\t\tword = WORD_POS_RESET;\n\t\t\t\t\t\t\tval.name = val.string;\n\t\t\t\t\t\t\tval.string = '';\n\t\t\t\t\t\t\tval.value_type = VALUE_UNSET;\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t} else if( parse_context == CONTEXT_OBJECT_FIELD\n\t\t\t\t\t\t\t|| parse_context == CONTEXT_CLASS_FIELD  ) {\n\t\t\t\t\t\t\tif( parse_context == CONTEXT_CLASS_FIELD ) {\n\t\t\t\t\t\t\t\tif( !Object.keys( elements).length ) {\n\t\t\t\t\t\t\t\t\t console.log( \"This is a full object, not a class def...\", val.className );\n\t\t\t\t\t\t\t\tconst privateProto = ()=>{} \n\t\t\t\t\t\t\t\tlocalFromProtoTypes.set( context_stack.last.node.current_class.name,\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{ protoCon:privateProto.prototype.constructor\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t, cb: null }\n\t\t\t\t\t\t\t\t\t\t\t\t\t   );\n\t\t\t\t\t\t\t\telements = new privateProto();\n\t\t\t\t\t\t\t\tparse_context = CONTEXT_OBJECT_FIELD_VALUE\n\t\t\t\t\t\t\t\tval.name = val.string;\n\t\t\t\t\t\t\t\tword = WORD_POS_RESET;\n\t\t\t\t\t\t\t\tval.string = ''\n\t\t\t\t\t\t\t\tval.value_type = VALUE_UNSET;\n\t\t\t\t\t\t\t\tconsole.log( \"don't do default;s do a revive...\" );\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tif( word != WORD_POS_RESET\n\t\t\t\t\t\t\t\t   && word != WORD_POS_END\n\t\t\t\t\t\t\t\t   && word != WORD_POS_FIELD\n\t\t\t\t\t\t\t\t   && word != WORD_POS_AFTER_FIELD ) {\n\t\t\t\t\t\t\t\t\trecoverIdent( 32 );\n\t\t\t\t\t\t\t\t\t// allow starting a new word\n\t\t\t\t\t\t\t\t\t//status = false;\n\t\t\t\t\t\t\t\t\t//throwError( `fault while parsing; unquoted keyword used as object field name (state:${word})`, cInt );\n\t\t\t\t\t\t\t\t\t//break;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tword = WORD_POS_RESET;\n\t\t\t\t\t\t\t\tval.name = val.string;\n\t\t\t\t\t\t\t\tval.string = '';\n\t\t\t\t\t\t\t\tparse_context = (parse_context===CONTEXT_OBJECT_FIELD)?CONTEXT_OBJECT_FIELD_VALUE:CONTEXT_CLASS_FIELD_VALUE;\n\t\t\t\t\t\t\t\tval.value_type = VALUE_UNSET;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse if( parse_context == CONTEXT_UNKNOWN ){\n\t\t\t\t\t\t\tconsole.log( \"Override colon found, allow class redefinition\", parse_context );\n\t\t\t\t\t\t\tredefineClass = true;\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tif( parse_context == CONTEXT_IN_ARRAY )\n\t\t\t\t\t\t\t\tthrowError(  \"(in array, got colon out of string):parsing fault;\", cInt );\n\t\t\t\t\t\t\telse if( parse_context == CONTEXT_OBJECT_FIELD_VALUE ){\n\t\t\t\t\t\t\t\tthrowError( \"String unexpected\", cInt );\n\t\t\t\t\t\t\t} else\n\t\t\t\t\t\t\t\tthrowError( \"(outside any object, got colon out of string):parsing fault;\", cInt );\n\t\t\t\t\t\t\tstatus = false;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 125/*'}'*/:\n\t\t\t\t\t\t//_DEBUG_PARSING && console.log( \"close bracket context:\", word, parse_context, val.value_type, val.string );\n\t\t\t\t\t\tif( word == WORD_POS_END ) {\n\t\t\t\t\t\t\t// allow starting a new word\n\t\t\t\t\t\t\tword = WORD_POS_RESET;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// coming back after pushing an array or sub-object will reset the contxt to FIELD, so an end with a field should still push value.\n\t\t\t\t\t\tif( parse_context == CONTEXT_CLASS_FIELD ) {\n\t\t\t\t\t\t\tif( current_class ) {\n\t\t\t\t\t\t\t\t// allow blank comma at end to not be a field\n\t\t\t\t\t\t\t\tif(val.string) { current_class.fields.push( val.string ); }\n\n\t\t\t\t\t\t\t\tRESET_VAL();\n\t\t\t\t\t\t\t\tlet old_context = context_stack.pop();\n\t\t\t\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"close object:\", old_context, context_stack );\n\t\t\t\t\t\t\t\t//_DEBUG_PARSING_STACK && console.log( \"object pop stack (close obj)\", context_stack.length, old_context );\n\t\t\t\t\t\t\t\tparse_context = CONTEXT_UNKNOWN; // this will restore as IN_ARRAY or OBJECT_FIELD\n\t\t\t\t\t\t\t\tword = WORD_POS_RESET;\n\t\t\t\t\t\t\t\tval.name = old_context.name;\n\t\t\t\t\t\t\t\telements = old_context.elements;\n\t\t\t\t\t\t\t\t//element_array = old_context.element_array;\n\t\t\t\t\t\t\t\tcurrent_class = old_context.current_class;\n\t\t\t\t\t\t\t\tcurrent_class_field = old_context.current_class_field;\n\t\t\t\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"A Pop old class field counter:\", current_class_field, val.name );\n\t\t\t\t\t\t\t\tarrayType = old_context.arrayType;\n\t\t\t\t\t\t\t\tval.value_type = old_context.valueType;\n\t\t\t\t\t\t\t\tval.className = old_context.className;\n\t\t\t\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \" !!!!!C Pop Classname:\", val.className );\n\t\t\t\t\t\t\t\trootObject = null;\n\n\t\t\t\t\t\t\t\tdropContext( old_context );\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tthrowError( \"State error; gathering class fields, and lost the class\", cInt );\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else if( ( parse_context == CONTEXT_OBJECT_FIELD ) || ( parse_context == CONTEXT_CLASS_VALUE ) ) {\n\t\t\t\t\t\t\tif( val.value_type != VALUE_UNSET ) {\n\t\t\t\t\t\t\t\tif( current_class ) {\n\t\t\t\t\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"C Stepping current class field:\", current_class_field, val.name, arrayType );\n\t\t\t\t\t\t\t\t\tval.name = current_class.fields[current_class_field++];\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t//_DEBUG_PARSING && console.log( \"Closing object; set value name, and push...\", current_class_field, val );\n\t\t\t\t\t\t\t\tobjectPush();\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t//_DEBUG_PARSING && console.log( \"close object; empty object\", val, elements );\n\n\t\t\t\t\t\t\t\tval.value_type = VALUE_OBJECT;\n\t\t\t\t\t\t\t\tif( current_proto && current_proto.protoDef ) {\n\t\t\t\t\t\t\t\t\tconsole.log( \"SOMETHING SHOULD AHVE BEEN REPLACED HERE??\", current_proto );\n\t\t\t\t\t\t\t\t\tconsole.log( \"The other version only revives on init\" );\n\t\t\t\t\t\t\t\t\telements = new current_proto.protoDef.cb( elements, undefined, undefined );\n\t\t\t\t\t\t\t\t\t//elements = new current_proto.protoCon( elements );\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tval.contains = elements;\n\t\t\t\t\t\t\t\tval.string = \"\";\n\n\t\t\t\t\t\t\tlet old_context = context_stack.pop();\n\t\t\t\t\t\t\t//_DEBUG_PARSING_STACK && console.log( \"object pop stack (close obj)\", context_stack.length, old_context );\n\t\t\t\t\t\t\tparse_context = old_context.context; // this will restore as IN_ARRAY or OBJECT_FIELD\n\t\t\t\t\t\t\tval.name = old_context.name;\n\t\t\t\t\t\t\telements = old_context.elements;\n\t\t\t\t\t\t\t//element_array = old_context.element_array;\n\t\t\t\t\t\t\tcurrent_class = old_context.current_class;\n\t\t\t\t\t\t\tcurrent_proto = old_context.current_proto;\n\t\t\t\t\t\t\tcurrent_class_field = old_context.current_class_field;\n\t\t\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"B Pop old class field counter:\", context_stack, current_class_field, val.name );\n\t\t\t\t\t\t\tarrayType = old_context.arrayType;\n\t\t\t\t\t\t\tval.value_type = old_context.valueType;\n\t\t\t\t\t\t\tval.className = old_context.className;\n\t\t\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \" !!!!!D Pop Classname:\", val.className );\n\t\t\t\t\t\t\tdropContext( old_context );\n\n\t\t\t\t\t\t\tif( parse_context == CONTEXT_UNKNOWN ) {\n\t\t\t\t\t\t\t\tcompleted = true;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse if( ( parse_context == CONTEXT_OBJECT_FIELD_VALUE ) ) {\n\t\t\t\t\t\t\t// first, add the last value\n\t\t\t\t\t\t\t//_DEBUG_PARSING && console.log( \"close object; push item '%s' %d\", val.name, val.value_type );\n\t\t\t\t\t\t\tif( val.value_type === VALUE_UNSET ) {\n\t\t\t\t\t\t\t\tthrowError( \"Fault while parsing; unexpected\", cInt );\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tobjectPush();\n\t\t\t\t\t\t\tval.value_type = VALUE_OBJECT;\n\t\t\t\t\t\t\tval.contains = elements;\n\t\t\t\t\t\t\tword = WORD_POS_RESET;\n\n\t\t\t\t\t\t\t//let old_context = context_stack.pop();\n\t\t\t\t\t\t\tlet old_context = context_stack.pop();\n\t\t\t\t\t\t\t//_DEBUG_PARSING_STACK  && console.log( \"object pop stack (close object)\", context_stack.length, old_context );\n\t\t\t\t\t\t\tparse_context = old_context.context; // this will restore as IN_ARRAY or OBJECT_FIELD\n\t\t\t\t\t\t\tval.name = old_context.name;\n\t\t\t\t\t\t\telements = old_context.elements;\n\t\t\t\t\t\t\tcurrent_proto = old_context.current_proto;\n\t\t\t\t\t\t\tcurrent_class = old_context.current_class;\n\t\t\t\t\t\t\tcurrent_class_field = old_context.current_class_field;\n\t\t\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"C Pop old class field counter:\", context_stack, current_class_field, val.name );\n\t\t\t\t\t\t\tarrayType = old_context.arrayType;\n\t\t\t\t\t\t\tval.value_type = old_context.valueType;\n\t\t\t\t\t\t\tval.className = old_context.className;\n\t\t\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \" !!!!!E Pop Classname:\", val.className );\n\t\t\t\t\t\t\t//element_array = old_context.element_array;\n\t\t\t\t\t\t\tdropContext( old_context );\n\t\t\t\t\t\t\tif( parse_context == CONTEXT_UNKNOWN ) {\n\t\t\t\t\t\t\t\tcompleted = true;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse {\n\t\t\t\t\t\t\tthrowError( \"Fault while parsing; unexpected\", cInt );\n\t\t\t\t\t\t\tstatus = false;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tnegative = false;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 93/*']'*/:\n\t\t\t\t\t\tif( word >= WORD_POS_AFTER_FIELD ) {\n\t\t\t\t\t\t\tword = WORD_POS_RESET;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif( parse_context == CONTEXT_IN_ARRAY ) {\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t//_DEBUG_PARSING  && console.log( \"close array, push last element: %d\", val.value_type );\n\t\t\t\t\t\t\tif( val.value_type != VALUE_UNSET ) {\n\t\t\t\t\t\t\t\t// name is set when saving a context.\n\t\t\t\t\t\t\t\t// a better sanity check would be val.name === elements.length;\n\t\t\t\t\t\t\t\t//if( val.name ) if( val.name !== elements.length ) console.log( \"Ya this should blow up\" );\n\t\t\t\t\t\t\t\tarrayPush();\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tval.contains = elements;\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tlet old_context = context_stack.pop();\n\t\t\t\t\t\t\t\t//_DEBUG_PARSING_STACK  && console.log( \"object pop stack (close array)\", context_stack.length );\n\t\t\t\t\t\t\t\tval.name = old_context.name;\n\t\t\t\t\t\t\t\tval.className = old_context.className;\n\t\t\t\t\t\t\t\tparse_context = old_context.context;\n\t\t\t\t\t\t\t\telements = old_context.elements;\n\t\t\t\t\t\t\t\t//element_array = old_context.element_array;\n\t\t\t\t\t\t\t\tcurrent_proto = old_context.current_proto;\n\t\t\t\t\t\t\t\tcurrent_class = old_context.current_class;\n\t\t\t\t\t\t\t\tcurrent_class_field = old_context.current_class_field;\n\t\t\t\t\t\t\t\tarrayType = old_context.arrayType;\n\t\t\t\t\t\t\t\tval.value_type = old_context.valueType;\n\t\t\t\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"close array:\", old_context );\n\t\t\t\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"D Pop old class field counter:\", context_stack, current_class_field, val );\n\t\t\t\t\t\t\t\tdropContext( old_context );\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tval.value_type = VALUE_ARRAY;\n\t\t\t\t\t\t\tif( parse_context == CONTEXT_UNKNOWN ) {\n\t\t\t\t\t\t\t\tcompleted = true;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthrowError( `bad context ${parse_context}; fault while parsing`, cInt );// fault\n\t\t\t\t\t\t\tstatus = false;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tnegative = false;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 44/*','*/:\n\t\t\t\t\t\tif( word < WORD_POS_AFTER_FIELD && word != WORD_POS_RESET ) {\n\t\t\t\t\t\t\trecoverIdent(cInt);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif( word == WORD_POS_END || word == WORD_POS_FIELD ) word = WORD_POS_RESET;  // allow collect new keyword\n\t\t\t\t\t\t//if(//_DEBUG_PARSING) \n\t\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"comma context:\", parse_context, val );\n\t\t\t\t\t\tif( parse_context == CONTEXT_CLASS_FIELD ) {\n\t\t\t\t\t\t\tif( current_class ) {\n\t\t\t\t\t\t\t\t//console.log( \"Saving field name(set word to IS A FIELD):\", val.string );\n\t\t\t\t\t\t\t\tcurrent_class.fields.push( val.string );\n\t\t\t\t\t\t\t\tval.string = '';\n\t\t\t\t\t\t\t\tword = WORD_POS_FIELD;\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tthrowError( \"State error; gathering class fields, and lost the class\", cInt );\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else if( parse_context == CONTEXT_OBJECT_FIELD ) {\n\t\t\t\t\t\t\tif( current_class ) {\n\t\t\t\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"D Stepping current class field:\", current_class_field, val.name );\n\t\t\t\t\t\t\t\tval.name = current_class.fields[current_class_field++];\n\t\t\t\t\t\t\t\t//_DEBUG_PARSING && console.log( \"should have a completed value at a comma.:\", current_class_field, val );\n\t\t\t\t\t\t\t\tif( val.value_type != VALUE_UNSET ) {\n\t\t\t\t\t\t\t\t\t//_DEBUG_PARSING  && console.log( \"pushing object field:\", val );\n\t\t\t\t\t\t\t\t\tobjectPush();\n\t\t\t\t\t\t\t\t\tRESET_VAL();\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t// this is an empty comma...\n\t\t\t\t\t\t\t\tif( val.string || val.value_type )\n\t\t\t\t\t\t\t\t\tthrowError( \"State error; comma in field name and/or lost the class\", cInt );\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else if( parse_context == CONTEXT_CLASS_VALUE ) {\n\t\t\t\t\t\t\tif( current_class ) {\n\t\t\t\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"reviving values in class...\", arrayType, current_class.fields[current_class_field ], val );\n\t\t\t\t\t\t\t\tif( arrayType != -3 && !val.name ) {\n\t\t\t\t\t\t\t\t\t// this should have still had a name....\n\t\t\t\t\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"E Stepping current class field:\", current_class_field, val, arrayType );\n\t\t\t\t\t\t\t\t\tval.name = current_class.fields[current_class_field++];\n\t\t\t\t\t\t\t\t\t//else val.name = current_class.fields[current_class_field++];\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t//_DEBUG_PARSING && console.log( \"should have a completed value at a comma.:\", current_class_field, val );\n\t\t\t\t\t\t\t\tif( val.value_type != VALUE_UNSET ) {\n\t\t\t\t\t\t\t\t\tif( arrayType != -3 )\n\t\t\t\t\t\t\t\t\t\tobjectPush();\n\t\t\t\t\t\t\t\t\tRESET_VAL();\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\tif( val.value_type != VALUE_UNSET ) {\n\t\t\t\t\t\t\t\t\tobjectPush();\n\t\t\t\t\t\t\t\t\tRESET_VAL();\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t//throwError( \"State error; gathering class values, and lost the class\", cInt );\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tval.name = null;\n\t\t\t\t\t\t} else if( parse_context == CONTEXT_IN_ARRAY ) {\n\t\t\t\t\t\t\tif( val.value_type == VALUE_UNSET )\n\t\t\t\t\t\t\t\tval.value_type = VALUE_EMPTY; // in an array, elements after a comma should init as undefined...\n\n\t\t\t\t\t\t\t//_DEBUG_PARSING  && console.log( \"back in array; push item %d\", val.value_type );\n\t\t\t\t\t\t\tarrayPush();\n\t\t\t\t\t\t\tRESET_VAL();\n\t\t\t\t\t\t\tword = WORD_POS_RESET;\n\t\t\t\t\t\t\t// undefined allows [,,,] to be 4 values and [1,2,3,] to be 4 values with an undefined at end.\n\t\t\t\t\t\t} else if( parse_context == CONTEXT_OBJECT_FIELD_VALUE && val.value_type != VALUE_UNSET ) {\n\t\t\t\t\t\t\t// after an array value, it will have returned to OBJECT_FIELD anyway\n\t\t\t\t\t\t\t//_DEBUG_PARSING  && console.log( \"comma after field value, push field to object: %s\", val.name, val.value_type );\n\t\t\t\t\t\t\tparse_context = CONTEXT_OBJECT_FIELD;\n\t\t\t\t\t\t\tif( val.value_type != VALUE_UNSET ) {\n\t\t\t\t\t\t\t\tobjectPush();\n\t\t\t\t\t\t\t\tRESET_VAL();\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tword = WORD_POS_RESET;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tstatus = false;\n\t\t\t\t\t\t\tthrowError( \"bad context; excessive commas while parsing;\", cInt );// fault\n\t\t\t\t\t\t}\n\t\t\t\t\t\tnegative = false;\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tdefault:\n\t\t\t\t\t\tswitch( cInt ) {\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\tif( ( parse_context == CONTEXT_UNKNOWN )\n\t\t\t\t\t\t  || ( parse_context == CONTEXT_OBJECT_FIELD_VALUE && word == WORD_POS_FIELD )\n\t\t\t\t\t\t  || ( ( parse_context == CONTEXT_OBJECT_FIELD ) || word == WORD_POS_FIELD )\n\t\t\t\t\t\t  || ( parse_context == CONTEXT_CLASS_FIELD ) ) {\n\t\t\t\t\t\t\tswitch( cInt ) {\n\t\t\t\t\t\t\tcase 96://'`':\n\t\t\t\t\t\t\tcase 34://'\"':\n\t\t\t\t\t\t\tcase 39://'\\'':\n\t\t\t\t\t\t\t\tif( word == WORD_POS_RESET || word == WORD_POS_FIELD ) {\n\t\t\t\t\t\t\t\t\tif( val.string.length ) {\n\t\t\t\t\t\t\t\t\t\tconsole.log( \"IN ARRAY AND FIXING?\" );\n\t\t\t\t\t\t\t\t\t\tval.className = val.string;\n\t\t\t\t\t\t\t\t\t\tval.string = '';\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tlet string_status = gatherString(cInt );\n\t\t\t\t\t\t\t\t\t//_DEBUG_PARSING && console.log( \"string gather for object field name :\", val.string, string_status );\n\t\t\t\t\t\t\t\t\tif( string_status ) {\n\t\t\t\t\t\t\t\t\t\tval.value_type = VALUE_STRING;\n\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\tgatheringStringFirstChar = cInt;\n\t\t\t\t\t\t\t\t\t\tgatheringString = true;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tthrowError( \"fault while parsing; quote not at start of field name\", cInt );\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\tcase 10://'\\n':\n\t\t\t\t\t\t\t\tpos.line++;\n\t\t\t\t\t\t\t\tpos.col = 1;\n\t\t\t\t\t\t\t\t// fall through to normal space handling - just updated line/col position\n\t\t\t\t\t\t\tcase 13://'\\r':\n\t\t\t\t\t\t\tcase 32://' ':\n\t\t\t\t\t\t\tcase 0x2028://' ':\n\t\t\t\t\t\t\tcase 0x2029://' ':\n\t\t\t\t\t\t\tcase 9://'\\t':\n\t\t\t\t\t\t\tcase 0xFEFF: // ZWNBS is WS though\n\t\t\t\t\t\t\t\t //_DEBUG_WHITESPACE  && console.log( \"THIS SPACE\", word, parse_context, val );\n\t\t\t\t\t\t\t\tif( parse_context === CONTEXT_UNKNOWN && word === WORD_POS_END ) { // allow collect new keyword\n\t\t\t\t\t\t\t\t\tword = WORD_POS_RESET;\n\t\t\t\t\t\t\t\t\tif( parse_context === CONTEXT_UNKNOWN ) {\n\t\t\t\t\t\t\t\t\t\tcompleted = true;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif( word === WORD_POS_RESET || word === WORD_POS_AFTER_FIELD ) { // ignore leading and trailing whitepsace\n\t\t\t\t\t\t\t\t\tif( parse_context == CONTEXT_UNKNOWN && val.value_type ) {\n\t\t\t\t\t\t\t\t\t\tcompleted = true;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\telse if( word === WORD_POS_FIELD ) {\n\t\t\t\t\t\t\t\t\tif( parse_context === CONTEXT_UNKNOWN ) {\n\t\t\t\t\t\t\t\t\t\tword = WORD_POS_RESET;\n\t\t\t\t\t\t\t\t\t\tcompleted = true;\n\t\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tif( val.string.length )\n\t\t\t\t\t\t\t\t\t\tconsole.log( \"STEP TO NEXT TOKEN.\" );\n\t\t\t\t\t\t\t\t\t\tword = WORD_POS_AFTER_FIELD;\n\t\t\t\t\t\t\t\t\t\t//val.className = val.string; val.string = '';\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\t\tstatus = false;\n\t\t\t\t\t\t\t\t\tthrowError( \"fault while parsing; whitepsace unexpected\", cInt );\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t// skip whitespace\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\t\t//if( /((\\n|\\r|\\t)|s|S|[ \\{\\}\\(\\)\\<\\>\\!\\+-\\*\\/\\.\\:\\, ])/.\n\t\t\t\t\t\t\t\tif( testNonIdentifierCharacters ) {\n\t\t\t\t\t\t\t\tlet identRow = nonIdent.find( row=>(row.firstChar >= cInt )&& (row.lastChar > cInt) )\n\t\t\t\t\t\t\t\tif( identRow && ( identRow.bits[(cInt - identRow.firstChar) / 24]\n\t\t\t\t\t\t\t\t    & (1 << ((cInt - identRow.firstChar) % 24)))) {\n\t\t\t\t\t\t\t\t//if( nonIdent[(cInt/(24*16))|0] && nonIdent[(cInt/(24*16))|0][(( cInt % (24*16) )/24)|0] & ( 1 << (cInt%24)) ) {\n\t\t\t\t\t\t\t\t\t// invalid start/continue\n\t\t\t\t\t\t\t\t\tstatus = false;\n\t\t\t\t\t\t\t\t\tthrowError( `fault while parsing object field name; \\\\u${cInt}`, cInt );\t// fault\n\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t//console.log( \"TICK\" );\n\t\t\t\t\t\t\t\tif( word == WORD_POS_RESET && ( ( cInt >= 48/*'0'*/ && cInt <= 57/*'9'*/ ) || ( cInt == 43/*'+'*/ ) || ( cInt == 46/*'.'*/ ) || ( cInt == 45/*'-'*/ ) ) ) {\n\t\t\t\t\t\t\t\t\tfromHex = false;\n\t\t\t\t\t\t\t\t\texponent = false;\n\t\t\t\t\t\t\t\t\tdate_format = false;\n\t\t\t\t\t\t\t\t\tisBigInt = false;\n\n\t\t\t\t\t\t\t\t\texponent_sign = false;\n\t\t\t\t\t\t\t\t\texponent_digit = false;\n\t\t\t\t\t\t\t\t\tdecimal = false;\n\t\t\t\t\t\t\t\t\tval.string = str;\n\t\t\t\t\t\t\t\t\tinput.n = n;\n\t\t\t\t\t\t\t\t\tcollectNumber();\n\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\tif( word === WORD_POS_AFTER_FIELD ) {\n\t\t\t\t\t\t\t\t\tstatus = false;\n\t\t\t\t\t\t\t\t\tthrowError( \"fault while parsing; character unexpected\", cInt );\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif( word === WORD_POS_RESET ) {\n\t\t\t\t\t\t\t\t\tword = WORD_POS_FIELD;\n\t\t\t\t\t\t\t\t\tval.value_type = VALUE_STRING;\n\t\t\t\t\t\t\t\t\tval.string += str;\n\t\t\t\t\t\t\t\t\t//_DEBUG_PARSING  && console.log( \"START/CONTINUE IDENTIFER\" );\n\t\t\t\t\t\t\t\t\tbreak;\n\n\t\t\t\t\t\t\t\t}     \n\t\t\t\t\t\t\t\tif( val.value_type == VALUE_UNSET ) {\n\t\t\t\t\t\t\t\t\tif( word !== WORD_POS_RESET && word !== WORD_POS_END )\n\t\t\t\t\t\t\t\t\t\trecoverIdent( cInt );\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tif( word === WORD_POS_END || word === WORD_POS_FIELD ) {\n\t\t\t\t\t\t\t\t\t\t// final word of the line... \n\t\t\t\t\t\t\t\t\t\t// whispace changes the 'word' state to not 'end'\n\t\t\t\t\t\t\t\t\t\t// until the next character, which may restore it to\n\t\t\t\t\t\t\t\t\t\t// 'end' and this will resume collecting the same string.\n\t\t\t\t\t\t\t\t\t\tval.string += str;\n\t\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tif( parse_context == CONTEXT_OBJECT_FIELD ) {\n\t\t\t\t\t\t\t\t\t\tif( word == WORD_POS_FIELD ) {\n\t\t\t\t\t\t\t\t\t\t\tval.string+=str;\n\t\t\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\tthrowError( \"Multiple values found in field name\", cInt );\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tif( parse_context == CONTEXT_OBJECT_FIELD_VALUE ) {\n\t\t\t\t\t\t\t\t\t\tthrowError( \"String unexpected\", cInt );\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tbreak; // default\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t}else {\n\t\t\t\t\t\t\tif( word == WORD_POS_RESET && ( ( cInt >= 48/*'0'*/ && cInt <= 57/*'9'*/ ) || ( cInt == 43/*'+'*/ ) || ( cInt == 46/*'.'*/ ) || ( cInt == 45/*'-'*/ ) ) ) {\n\t\t\t\t\t\t\t\tfromHex = false;\n\t\t\t\t\t\t\t\texponent = false;\n\t\t\t\t\t\t\t\tdate_format = false;\n\t\t\t\t\t\t\t\tisBigInt = false;\n\n\t\t\t\t\t\t\t\texponent_sign = false;\n\t\t\t\t\t\t\t\texponent_digit = false;\n\t\t\t\t\t\t\t\tdecimal = false;\n\t\t\t\t\t\t\t\tval.string = str;\n\t\t\t\t\t\t\t\tinput.n = n;\n\t\t\t\t\t\t\t\tcollectNumber();\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t//console.log( \"TICK\")\n\t\t\t\t\t\t\t\tif( val.value_type == VALUE_UNSET ) {\n\t\t\t\t\t\t\t\t\tif( word != WORD_POS_RESET ) {\n\t\t\t\t\t\t\t\t\t\trecoverIdent( cInt );\n\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\tword = WORD_POS_END;\n\t\t\t\t\t\t\t\t\t\tval.string += str;\n\t\t\t\t\t\t\t\t\t\tval.value_type = VALUE_STRING;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tif( parse_context == CONTEXT_OBJECT_FIELD ) {\n\t\t\t\t\t\t\t\t\t\tthrowError( \"Multiple values found in field name\", cInt );\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\telse if( parse_context == CONTEXT_OBJECT_FIELD_VALUE ) {\n\n\t\t\t\t\t\t\t\t\t\tif( val.value_type != VALUE_STRING ) {\n\t\t\t\t\t\t\t\t\t\t\tif( val.value_type == VALUE_OBJECT || val.value_type == VALUE_ARRAY ){\n\t\t\t\t\t\t\t\t\t\t\t\tthrowError( \"String unexpected\", cInt );\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\trecoverIdent(cInt);\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\tif( word == WORD_POS_AFTER_FIELD ){\n\t\t\t\t\t\t\t\t\t\t\tconst  protoDef = getProto();\n\t\t\t\t\t\t\t\t\t\t\tif( protoDef){\n\t\t\t\t\t\t\t\t\t\t\t\tword == WORD_POS_END; // last string.\n\t\t\t\t\t\t\t\t\t\t\t\tval.string = str;\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\telse \n\t\t\t\t\t\t\t\t\t\t\t\tthrowError( \"String unexpected\", cInt );\n\t\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\t\tif( word == WORD_POS_END ) {\n\t\t\t\t\t\t\t\t\t\t\t\tval.string += str;\n\t\t\t\t\t\t\t\t\t\t\t}else\n\t\t\t\t\t\t\t\t\t\t\t\tthrowError( \"String unexpected\", cInt );\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\telse if( parse_context == CONTEXT_IN_ARRAY ) {\n\t\t\t\t\t\t\t\t\t\tif( word == WORD_POS_AFTER_FIELD ){\n\t\t\t\t\t\t\t\t\t\t\tif( !val.className ){\n\t\t\t\t\t\t\t\t\t\t\t\t//\tgetProto()\n\t\t\t\t\t\t\t\t\t\t\t\tval.className = val.string;\n\t\t\t\t\t\t\t\t\t\t\t\tval.string = '';\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\tval.string += str;\n\t\t\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\t\tif( word == WORD_POS_END )\n\t\t\t\t\t\t\t\t\t\t\t\tval.string += str;\n\t\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t//recoverIdent(cInt);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tbreak; // default\n\t\t\t\t\t\t}\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 96://'`':\n\t\t\t\t\t\tcase 34://'\"':\n\t\t\t\t\t\tcase 39://'\\'':\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tif( val.string ) val.className = val.string; val.string = '';\n\t\t\t\t\t\t\tlet string_status = gatherString( cInt );\n\t\t\t\t\t\t\t//_DEBUG_PARSING && console.log( \"string gather for object field value :\", val.string, string_status, completed, input.n, buf.length );\n\t\t\t\t\t\t\tif( string_status ) {\n\t\t\t\t\t\t\t\tval.value_type = VALUE_STRING;\n\t\t\t\t\t\t\t\tword = WORD_POS_END;\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tgatheringStringFirstChar = cInt;\n\t\t\t\t\t\t\t\tgatheringString = true;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tcase 10://'\\n':\n\t\t\t\t\t\t\tpos.line++;\n\t\t\t\t\t\t\tpos.col = 1;\n\t\t\t\t\t\t\t//falls through\n\t\t\t\t\t\tcase 32://' ':\n\t\t\t\t\t\tcase 9://'\\t':\n\t\t\t\t\t\tcase 13://'\\r':\n\t\t\t\t\t\tcase 0x2028: // LS (Line separator)\n\t\t\t\t\t\tcase 0x2029: // PS (paragraph separate)\n\t\t\t\t\t\tcase 0xFEFF://'\\uFEFF':\n\t\t\t\t\t\t\t//_DEBUG_WHITESPACE && console.log( \"Whitespace...\", word, parse_context );\n\t\t\t\t\t\t\tif( word == WORD_POS_END ) {\n\t\t\t\t\t\t\t\tif( parse_context == CONTEXT_UNKNOWN ) {\n\t\t\t\t\t\t\t\t\tword = WORD_POS_RESET;\n\t\t\t\t\t\t\t\t\tcompleted = true;\n\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t} else if( parse_context == CONTEXT_OBJECT_FIELD_VALUE ) {\n\t\t\t\t\t\t\t\t\tword = WORD_POS_AFTER_FIELD_VALUE;\n\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t} else if( parse_context == CONTEXT_OBJECT_FIELD ) {\n\t\t\t\t\t\t\t\t\tword = WORD_POS_AFTER_FIELD;\n\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t} else if( parse_context == CONTEXT_IN_ARRAY ) {\n\t\t\t\t\t\t\t\t\tword = WORD_POS_AFTER_FIELD;\n\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif( word == WORD_POS_RESET || ( word == WORD_POS_AFTER_FIELD ))\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\telse if( word == WORD_POS_FIELD ) {\n\t\t\t\t\t\t\t\tif( val.string.length )\n\t\t\t\t\t\t\t\t\tword = WORD_POS_AFTER_FIELD;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\tif( word < WORD_POS_END ) \n\t\t\t\t\t\t\t\t\trecoverIdent( cInt );\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t//----------------------------------------------------------\n\t\t\t\t\t//  catch characters for true/false/null/undefined which are values outside of quotes\n\t\t\t\t\t\tcase 116://'t':\n\t\t\t\t\t\t\tif( word == WORD_POS_RESET ) word = WORD_POS_TRUE_1;\n\t\t\t\t\t\t\telse if( word == WORD_POS_INFINITY_6 ) word = WORD_POS_INFINITY_7;\n\t\t\t\t\t\t\telse { recoverIdent(cInt); }// fault\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 114://'r':\n\t\t\t\t\t\t\tif( word == WORD_POS_TRUE_1 ) word = WORD_POS_TRUE_2;\n\t\t\t\t\t\t\telse { recoverIdent(cInt); }// fault\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 117://'u':\n\t\t\t\t\t\t\tif( word == WORD_POS_TRUE_2 ) word = WORD_POS_TRUE_3;\n\t\t\t\t\t\t\telse if( word == WORD_POS_NULL_1 ) word = WORD_POS_NULL_2;\n\t\t\t\t\t\t\telse if( word == WORD_POS_RESET ) word = WORD_POS_UNDEFINED_1;\n\t\t\t\t\t\t\telse { recoverIdent(cInt); }// fault\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 101://'e':\n\t\t\t\t\t\t\tif( word == WORD_POS_TRUE_3 ) {\n\t\t\t\t\t\t\t\tval.value_type = VALUE_TRUE;\n\t\t\t\t\t\t\t\tword = WORD_POS_END;\n\t\t\t\t\t\t\t} else if( word == WORD_POS_FALSE_4 ) {\n\t\t\t\t\t\t\t\tval.value_type = VALUE_FALSE;\n\t\t\t\t\t\t\t\tword = WORD_POS_END;\n\t\t\t\t\t\t\t} else if( word == WORD_POS_UNDEFINED_3 ) word = WORD_POS_UNDEFINED_4;\n\t\t\t\t\t\t\telse if( word == WORD_POS_UNDEFINED_7 ) word = WORD_POS_UNDEFINED_8;\n\t\t\t\t\t\t\telse { recoverIdent(cInt); }// fault\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 110://'n':\n\t\t\t\t\t\t\tif( word == WORD_POS_RESET ) word = WORD_POS_NULL_1;\n\t\t\t\t\t\t\telse if( word == WORD_POS_UNDEFINED_1 ) word = WORD_POS_UNDEFINED_2;\n\t\t\t\t\t\t\telse if( word == WORD_POS_UNDEFINED_6 ) word = WORD_POS_UNDEFINED_7;\n\t\t\t\t\t\t\telse if( word == WORD_POS_INFINITY_1 ) word = WORD_POS_INFINITY_2;\n\t\t\t\t\t\t\telse if( word == WORD_POS_INFINITY_4 ) word = WORD_POS_INFINITY_5;\n\t\t\t\t\t\t\telse { recoverIdent(cInt); }// fault\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 100://'d':\n\t\t\t\t\t\t\tif( word == WORD_POS_UNDEFINED_2 ) word = WORD_POS_UNDEFINED_3;\n\t\t\t\t\t\t\telse if( word == WORD_POS_UNDEFINED_8 ) { val.value_type=VALUE_UNDEFINED; word = WORD_POS_END; }\n\t\t\t\t\t\t\telse { recoverIdent(cInt); }// fault\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 105://'i':\n\t\t\t\t\t\t\tif( word == WORD_POS_UNDEFINED_5 ) word = WORD_POS_UNDEFINED_6;\n\t\t\t\t\t\t\telse if( word == WORD_POS_INFINITY_3 ) word = WORD_POS_INFINITY_4;\n\t\t\t\t\t\t\telse if( word == WORD_POS_INFINITY_5 ) word = WORD_POS_INFINITY_6;\n\t\t\t\t\t\t\telse { recoverIdent(cInt); }// fault\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 108://'l':\n\t\t\t\t\t\t\tif( word == WORD_POS_NULL_2 ) word = WORD_POS_NULL_3;\n\t\t\t\t\t\t\telse if( word == WORD_POS_NULL_3 ) {\n\t\t\t\t\t\t\t\tval.value_type = VALUE_NULL;\n\t\t\t\t\t\t\t\tword = WORD_POS_END;\n\t\t\t\t\t\t\t} else if( word == WORD_POS_FALSE_2 ) word = WORD_POS_FALSE_3;\n\t\t\t\t\t\t\telse { recoverIdent(cInt); }// fault\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 102://'f':\n\t\t\t\t\t\t\tif( word == WORD_POS_RESET ) word = WORD_POS_FALSE_1;\n\t\t\t\t\t\t\telse if( word == WORD_POS_UNDEFINED_4 ) word = WORD_POS_UNDEFINED_5;\n\t\t\t\t\t\t\telse if( word == WORD_POS_INFINITY_2 ) word = WORD_POS_INFINITY_3;\n\t\t\t\t\t\t\telse { recoverIdent(cInt); }// fault\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 97://'a':\n\t\t\t\t\t\t\tif( word == WORD_POS_FALSE_1 ) word = WORD_POS_FALSE_2;\n\t\t\t\t\t\t\telse if( word == WORD_POS_NAN_1 ) word = WORD_POS_NAN_2;\n\t\t\t\t\t\t\telse { recoverIdent(cInt); }// fault\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 115://'s':\n\t\t\t\t\t\t\tif( word == WORD_POS_FALSE_3 ) word = WORD_POS_FALSE_4;\n\t\t\t\t\t\t\telse { recoverIdent(cInt); }// fault\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 73://'I':\n\t\t\t\t\t\t\tif( word == WORD_POS_RESET ) word = WORD_POS_INFINITY_1;\n\t\t\t\t\t\t\telse { recoverIdent(cInt); }// fault\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 78://'N':\n\t\t\t\t\t\t\tif( word == WORD_POS_RESET ) word = WORD_POS_NAN_1;\n\t\t\t\t\t\t\telse if( word == WORD_POS_NAN_2 ) { val.value_type = negative ? VALUE_NEG_NAN : VALUE_NAN; negative = false; word = WORD_POS_END; }\n\t\t\t\t\t\t\telse { recoverIdent(cInt); }// fault\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 121://'y':\n\t\t\t\t\t\t\tif( word == WORD_POS_INFINITY_7 ) { val.value_type = negative ? VALUE_NEG_INFINITY : VALUE_INFINITY; negative = false; word = WORD_POS_END; }\n\t\t\t\t\t\t\telse { recoverIdent(cInt); }// fault\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 45://'-':\n\t\t\t\t\t\t\tif( word == WORD_POS_RESET ) negative = !negative;\n\t\t\t\t\t\t\telse { recoverIdent(cInt); }// fault\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 43://'+':\n\t\t\t\t\t\t\tif( word !== WORD_POS_RESET ) { recoverIdent(cInt); }\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tbreak; // default of high level switch\n\t\t\t\t\t//\n\t\t\t\t\t//----------------------------------------------------------\n\t\t\t\t\t}\n\t\t\t\t\tif( completed ) {\n\t\t\t\t\t\tif( word == WORD_POS_END ) {\n\t\t\t\t\t\t\tword = WORD_POS_RESET;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif( n == buf.length ) {\n\t\t\t\t\tdropBuffer( input );\n\t\t\t\t\tif( gatheringString || gatheringNumber || parse_context == CONTEXT_OBJECT_FIELD ) {\n\t\t\t\t\t\tretval = 0;\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tif( parse_context == CONTEXT_UNKNOWN && ( val.value_type != VALUE_UNSET || result ) ) {\n\t\t\t\t\t\t\tcompleted = true;\n\t\t\t\t\t\t\tretval = 1;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\t// put these back into the stack.\n\t\t\t\t\tinput.n = n;\n\t\t\t\t\tinQueue.unshift( input );\n\t\t\t\t\tretval = 2;  // if returning buffers, then obviously there's more in this one.\n\t\t\t\t}\n\t\t\t\tif( completed ) {\n\t\t\t\t\trootObject = null;\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif( !status ) return -1;\n\t\t\tif( completed && val.value_type != VALUE_UNSET ) {\n\t\t\t\tword = WORD_POS_RESET;\n\t\t\t\tresult = convertValue();\n\t\t\t\t//_DEBUG_PARSING && console.log( \"Result(3):\", result );\n\t\t\t\tnegative = false;\n\t\t\t\tval.string = '';\n\t\t\t\tval.value_type = VALUE_UNSET;\n\t\t\t}\n\t\t\tcompleted = false;\n\t\t\treturn retval;\n\t\t}\n\t}\n}\n\n\n\nconst _parser = [Object.freeze( JSOX.begin() )];\nlet _parse_level = 0;\n/**\n * @param {string} msg \n * @param {(this: unknown, key: string, value: unknown) => any} [reviver] \n * @returns {unknown}\n */\nJSOX.parse = function( msg, reviver ) {\n\tlet parse_level = _parse_level++;\n\tlet parser;\n\tif( _parser.length <= parse_level )\n\t\t_parser.push( Object.freeze( JSOX.begin() ) );\n\tparser = _parser[parse_level];\n\tif (typeof msg !== \"string\") msg = String(msg);\n\tparser.reset();\n\tconst writeResult = parser._write( msg, true );\n\tif( writeResult > 0 ) {\n\t\tif( writeResult > 1 ){\n\t\t\t// probably a carriage return.\n\t\t\t//console.log( \"Extra data at end of message\");\n\t\t}\n\t\tlet result = parser.value();\n\t\tif( ( \"undefined\" === typeof result ) && writeResult > 1 ){\n\t\t\tthrow new Error( \"Pending value could not complete\");\n\t\t}\n\n\t\tresult = typeof reviver === 'function' ? (function walk(holder, key) {\n\t\t\tlet k, v, value = holder[key];\n\t\t\tif (value && typeof value === 'object') {\n\t\t\t\tfor (k in value) {\n\t\t\t\t\tif (Object.prototype.hasOwnProperty.call(value, k)) {\n\t\t\t\t\t\tv = walk(value, k);\n\t\t\t\t\t\tif (v !== undefined) {\n\t\t\t\t\t\t\tvalue[k] = v;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tdelete value[k];\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn reviver.call(holder, key, value);\n\t\t}({'': result}, '')) : result;\n\t\t_parse_level--;\n\t\treturn result;\n\t}\n\tparser.finalError();\n\treturn undefined;\n}\n\n\nfunction this_value() {/*//_DEBUG_STRINGIFY&&console.log( \"this:\", this, \"valueof:\", this&&this.valueOf() );*/ \n\treturn this&&this.valueOf();\n}\n\n/**\n * Define a class to be used for serialization; the class allows emitting the class fields ahead of time, and just provide values later.\n * @param {string} name \n * @param {object} obj \n */\nJSOX.defineClass = function( name, obj ) {\n\tlet cls;\n\tlet denormKeys = Object.keys(obj);\n\tfor( let i = 1; i < denormKeys.length; i++ ) {\n\t\tlet a, b;\n\t\tif( ( a = denormKeys[i-1] ) > ( b = denormKeys[i] ) ) {\n\t\t\tdenormKeys[i-1] = b;\n\t\t\tdenormKeys[i] = a;\n\t\t\tif( i ) i-=2; // go back 2, this might need to go further pack.\n\t\t\telse i--; // only 1 to check.\n\t\t}\n\t}\n\t//console.log( \"normalized:\", denormKeys );\n\tcommonClasses.push( cls = { name : name\n\t\t   , tag:denormKeys.toString()\n\t\t   , proto : Object.getPrototypeOf(obj)\n\t\t   , fields : Object.keys(obj) } );\n\tfor(let n = 1; n < cls.fields.length; n++) {\n\t\tif( cls.fields[n] < cls.fields[n-1] ) {\n\t\t\tlet tmp = cls.fields[n-1];\n\t\t\tcls.fields[n-1] = cls.fields[n];\n\t\t\tcls.fields[n] = tmp;\n\t\t\tif( n > 1 )\n\t\t\t\tn-=2;\n\t\t}\n\t}\n\tif( cls.proto === Object.getPrototypeOf( {} ) ) cls.proto = null;\n}\n\n/**\n * define a class to be used for serialization\n * @param {string} named\n * @param {class} ptype\n * @param {(any)=>any} f\n */\nJSOX.toJSOX =\nJSOX.registerToJSOX = function( name, ptype, f ) {\n\t//console.log( \"SET OBJECT TYPE:\", ptype, ptype.prototype, Object.prototype, ptype.constructor );\n\tif( !ptype.prototype || ptype.prototype !== Object.prototype ) {\n\t\tif( toProtoTypes.get(ptype.prototype) ) throw new Error( \"Existing toJSOX has been registered for prototype\" );\n\t\t//_DEBUG_PARSING && console.log( \"PUSH PROTOTYPE\" );\n\t\ttoProtoTypes.set( ptype.prototype, { external:true, name:name||f.constructor.name, cb:f } );\n\t} else {\n\t\tlet key = Object.keys( ptype ).toString();\n\t\tif( toObjectTypes.get(key) ) throw new Error( \"Existing toJSOX has been registered for object type\" );\n\t\t//console.log( \"TEST SET OBJECT TYPE:\", key );\n\t\ttoObjectTypes.set( key, { external:true, name:name, cb:f } );\n\t}\n}\n/**\n * define a class to be used for deserialization\n * @param {string} prototypeName \n * @param {class} o \n * @param {(any)=>any} f \n */\nJSOX.fromJSOX = function( prototypeName, o, f ) {\n\tfunction privateProto() { }\n\t\tif( !o ) o = privateProto.prototype;\n\t\tif( fromProtoTypes.get(prototypeName) ) throw new Error( \"Existing fromJSOX has been registered for prototype\" );\n\t\tif( o && !(\"constructor\" in o )){\n\t\t\tthrow new Error( \"Please pass a prototype like thing...\");\n\t}\n\tfromProtoTypes.set( prototypeName, {protoCon: o.prototype.constructor, cb:f } );\n\n}\nJSOX.registerFromJSOX = function( prototypeName, o /*, f*/ ) {\n\tthrow new Error( \"deprecated; please adjust code to use fromJSOX:\" + prototypeName + o.toString() );\n}\nJSOX.addType = function( prototypeName, prototype, to, from ) {\n\tJSOX.toJSOX( prototypeName, prototype, to );\n\tJSOX.fromJSOX( prototypeName, prototype, from );\n}\n\nJSOX.registerToFrom = function( prototypeName, prototype/*, to, from*/ ) {\n\tthrow new Error( \"registerToFrom deprecated; please use addType:\" + prototypeName + prototype.toString() );\n}\n\n/**\n * Create a stringifier to convert objects to JSOX text.  Allows defining custom serialization for objects.\n * @returns {Stringifier}\n */\nJSOX.stringifier = function() {\n\tlet classes = [];\n\tlet useQuote = '\"';\n\n\tlet fieldMap = new WeakMap();\n\tconst path = [];\n\tlet encoding = [];\n\tconst localToProtoTypes = new WeakMap();\n\tconst localToObjectTypes = new Map();\n\tlet objectToJSOX = null;\n\tconst stringifying = []; // things that have been stringified through external toJSOX; allows second pass to skip this toJSOX pass and encode 'normally'\n\tlet ignoreNonEnumerable = false;\n\tfunction getIdentifier(s) {\n\t\n\t\tif( ( \"string\" === typeof s ) && s === '' ) return '\"\"';\n\t\tif( ( \"number\" === typeof s ) && !isNaN( s ) ) {\n\t\t\treturn [\"'\",s.toString(),\"'\"].join('');\n\t\t}\n\t\t// should check also for if any non ident in string...\n\t\tif( s.includes( \"\\u{FEFF}\" ) ) return (useQuote + JSOX.escape(s) +useQuote);\n\t\treturn ( ( s in keywords /* [ \"true\",\"false\",\"null\",\"NaN\",\"Infinity\",\"undefined\"].find( keyword=>keyword===s )*/\n\t\t\t|| /[0-9\\-]/.test(s[0])\n\t\t\t|| /[\\n\\r\\t #\\[\\]{}()<>\\~!+*/.:,\\-\"'`]/.test( s ) )?(useQuote + JSOX.escape(s) +useQuote):s )\n\t}\n\n\n\t/* init prototypes */\n\tif( !toProtoTypes.get( Object.prototype ) )\n\t{\n\t\ttoProtoTypes.set( Object.prototype, { external:false, name:Object.prototype.constructor.name, cb:null } );\n\t   \n\t\t// function https://stackoverflow.com/a/17415677/4619267\n\t\ttoProtoTypes.set( Date.prototype, { external:false,\n\t\t\tname : \"Date\",\n\t\t\tcb : function () {\n\t\t\t\t\tif( this.getTime()=== -62167219200000) \n\t\t\t\t\t{\n\t\t\t\t\t\treturn \"0000-01-01T00:00:00.000Z\";\n\t\t\t\t\t}\n\t\t\t\t\tlet tzo = -this.getTimezoneOffset(),\n\t\t\t\t\tdif = tzo >= 0 ? '+' : '-',\n\t\t\t\t\tpad = function(num) {\n\t\t\t\t\t\tlet norm = Math.floor(Math.abs(num));\n\t\t\t\t\t\treturn (norm < 10 ? '0' : '') + norm;\n\t\t\t\t\t},\n\t\t\t\t\tpad3 = function(num) {\n\t\t\t\t\t\tlet norm = Math.floor(Math.abs(num));\n\t\t\t\t\t\treturn (norm < 100 ? '0' : '') + (norm < 10 ? '0' : '') + norm;\n\t\t\t\t\t};\n\t\t\t\treturn [this.getFullYear() ,\n\t\t\t\t\t'-' , pad(this.getMonth() + 1) ,\n\t\t\t\t\t'-' , pad(this.getDate()) ,\n\t\t\t\t\t'T' , pad(this.getHours()) ,\n\t\t\t\t\t':' , pad(this.getMinutes()) ,\n\t\t\t\t\t':' , pad(this.getSeconds()) ,\n\t\t\t\t\t'.' + pad3(this.getMilliseconds()) +\n\t\t\t\t\tdif , pad(tzo / 60) ,\n\t\t\t\t\t':' , pad(tzo % 60)].join(\"\");\n\t\t\t} \n\t\t} );\n\t\ttoProtoTypes.set( DateNS.prototype, { external:false,\n\t\t\tname : \"DateNS\",\n\t\t\tcb : function () {\n\t\t\t\tlet tzo = -this.getTimezoneOffset(),\n\t\t\t\t\tdif = tzo >= 0 ? '+' : '-',\n\t\t\t\t\tpad = function(num) {\n\t\t\t\t\t\tlet norm = Math.floor(Math.abs(num));\n\t\t\t\t\t\treturn (norm < 10 ? '0' : '') + norm;\n\t\t\t\t\t},\n\t\t\t\t\tpad3 = function(num) {\n\t\t\t\t\t\tlet norm = Math.floor(Math.abs(num));\n\t\t\t\t\t\treturn (norm < 100 ? '0' : '') + (norm < 10 ? '0' : '') + norm;\n\t\t\t\t\t},\n\t\t\t\t\tpad6 = function(num) {\n\t\t\t\t\t\tlet norm = Math.floor(Math.abs(num));\n\t\t\t\t\t\treturn (norm < 100000 ? '0' : '') + (norm < 10000 ? '0' : '') + (norm < 1000 ? '0' : '') + (norm < 100 ? '0' : '') + (norm < 10 ? '0' : '') + norm;\n\t\t\t\t\t};\n\t\t\t\treturn [this.getFullYear() ,\n\t\t\t\t\t'-' , pad(this.getMonth() + 1) ,\n\t\t\t\t\t'-' , pad(this.getDate()) ,\n\t\t\t\t\t'T' , pad(this.getHours()) ,\n\t\t\t\t\t':' , pad(this.getMinutes()) ,\n\t\t\t\t\t':' , pad(this.getSeconds()) ,\n\t\t\t\t\t'.' + pad3(this.getMilliseconds()) + pad6(this.ns) +\n\t\t\t\t\tdif , pad(tzo / 60) ,\n\t\t\t\t\t':' , pad(tzo % 60)].join(\"\");\n\t\t\t} \n\t\t} );\n\t\ttoProtoTypes.set( Boolean.prototype, { external:false, name:\"Boolean\", cb:this_value  } );\n\t\ttoProtoTypes.set( Number.prototype, { external:false, name:\"Number\"\n\t\t    , cb:function(){ \n\t\t\t\tif( isNaN(this) )  return \"NaN\";\n\t\t\t\treturn (isFinite(this))\n\t\t\t\t\t? String(this)\n\t\t\t\t\t: (this<0)?\"-Infinity\":\"Infinity\";\n\t\t    }\n\t\t} );\n\t\ttoProtoTypes.set( String.prototype, { external:false\n\t\t    , name : \"String\"\n\t\t    , cb:function(){ return '\"' + JSOX.escape(this_value.apply(this)) + '\"' } } );\n\t\tif( typeof BigInt === \"function\" )\n\t\t\ttoProtoTypes.set( BigInt.prototype\n\t\t\t     , { external:false, name:\"BigInt\", cb:function() { return this + 'n' } } );\n\t   \n\t\ttoProtoTypes.set( ArrayBuffer.prototype, { external:true, name:\"ab\"\n\t\t    , cb:function() { return \"[\"+getIdentifier(base64ArrayBuffer(this))+\"]\" }\n\t\t} );\n\t   \n\t\ttoProtoTypes.set( Uint8Array.prototype, { external:true, name:\"u8\"\n\t\t    , cb:function() { return \"[\"+getIdentifier(base64ArrayBuffer(this.buffer))+\"]\" }\n\t\t} );\n\t\ttoProtoTypes.set( Uint8ClampedArray.prototype, { external:true, name:\"uc8\"\n\t\t    , cb:function() { return \"[\"+getIdentifier(base64ArrayBuffer(this.buffer))+\"]\" }\n\t\t} );\n\t\ttoProtoTypes.set( Int8Array.prototype, { external:true, name:\"s8\"\n\t\t    , cb:function() { return \"[\"+getIdentifier(base64ArrayBuffer(this.buffer))+\"]\" }\n\t\t} );\n\t\ttoProtoTypes.set( Uint16Array.prototype, { external:true, name:\"u16\"\n\t\t    , cb:function() { return \"[\"+getIdentifier(base64ArrayBuffer(this.buffer))+\"]\" }\n\t\t} );\n\t\ttoProtoTypes.set( Int16Array.prototype, { external:true, name:\"s16\"\n\t\t    , cb:function() { return \"[\"+getIdentifier(base64ArrayBuffer(this.buffer))+\"]\" }\n\t\t} );\n\t\ttoProtoTypes.set( Uint32Array.prototype, { external:true, name:\"u32\"\n\t\t    , cb:function() { return \"[\"+getIdentifier(base64ArrayBuffer(this.buffer))+\"]\" }\n\t\t} );\n\t\ttoProtoTypes.set( Int32Array.prototype, { external:true, name:\"s32\"\n\t\t    , cb:function() { return \"[\"+getIdentifier(base64ArrayBuffer(this.buffer))+\"]\" }\n\t\t} );\n\t\t/*\n\t\tif( typeof Uint64Array != \"undefined\" )\n\t\t\ttoProtoTypes.set( Uint64Array.prototype, { external:true, name:\"u64\"\n\t\t\t    , cb:function() { return \"[\"+getIdentifier(base64ArrayBuffer(this.buffer))+\"]\" }\n\t\t\t} );\n\t\tif( typeof Int64Array != \"undefined\" )\n\t\t\ttoProtoTypes.set( Int64Array.prototype, { external:true, name:\"s64\"\n\t\t\t    , cb:function() { return \"[\"+getIdentifier(base64ArrayBuffer(this.buffer))+\"]\" }\n\t\t\t} );\n\t\t*/\n\t\ttoProtoTypes.set( Float32Array.prototype, { external:true, name:\"f32\"\n\t\t    , cb:function() { return \"[\"+getIdentifier(base64ArrayBuffer(this.buffer))+\"]\" }\n\t\t} );\n\t\ttoProtoTypes.set( Float64Array.prototype, { external:true, name:\"f64\"\n\t\t    , cb:function() { return \"[\"+getIdentifier(base64ArrayBuffer(this.buffer))+\"]\" }\n\t\t} );\n\t\ttoProtoTypes.set( Float64Array.prototype, { external:true, name:\"f64\"\n\t\t    , cb:function() { return \"[\"+getIdentifier(base64ArrayBuffer(this.buffer))+\"]\" }\n\t\t} );\n\t   \n\t\ttoProtoTypes.set( RegExp.prototype, mapToJSOX = { external:true, name:\"regex\"\n\t\t    , cb:function(o,stringifier){\n\t\t\t\treturn \"'\"+escape(this.source)+\"'\";\n\t\t\t}\n\t\t} );\n\t\tfromProtoTypes.set( \"regex\", { protoCon:RegExp, cb:function (field,val){\n\t\t\treturn new RegExp( this );\n\t\t} } );\n\n\t\ttoProtoTypes.set( Map.prototype, mapToJSOX = { external:true, name:\"map\"\n\t\t    , cb:null\n\t\t} );\n\t\tfromProtoTypes.set( \"map\", { protoCon:Map, cb:function (field,val){\n\t\t\tif( field ) {\n\t\t\t\tthis.set( field, val );\n\t\t\t\treturn undefined;\n\t\t\t}\n\t\t\treturn this;\n\t\t} } );\n\t   \n\t\ttoProtoTypes.set( Array.prototype, arrayToJSOX = { external:false, name:Array.prototype.constructor.name\n\t\t    , cb: null\t\t    \n\t\t} );\n\n\t}\n\n\tconst stringifier = {\n\t\tdefineClass(name,obj) { \n\t\t\tlet cls; \n\t\t\tlet denormKeys = Object.keys(obj);\n\t\t\tfor( let i = 1; i < denormKeys.length; i++ ) {\n\t\t\t\t// normalize class key order\n\t\t\t\tlet a, b;\n\t\t\t\tif( ( a = denormKeys[i-1] ) > ( b = denormKeys[i] ) ) {\n\t\t\t\t\tdenormKeys[i-1] = b;\n\t\t\t\t\tdenormKeys[i] = a;\n\t\t\t\t\tif( i ) i-=2; // go back 2, this might need to go further pack.\n\t\t\t\t\telse i--; // only 1 to check.\n\t\t\t\t}\n\t\t\t}\n\t\t\tclasses.push( cls = { name : name\n\t\t\t       , tag:denormKeys.toString()\n\t\t\t       , proto : Object.getPrototypeOf(obj)\n\t\t\t       , fields : Object.keys(obj) } );\n\n\t\t\tfor(let n = 1; n < cls.fields.length; n++) {\n\t\t\t\tif( cls.fields[n] < cls.fields[n-1] ) {\n\t\t\t\t\tlet tmp = cls.fields[n-1];\n\t\t\t\t\tcls.fields[n-1] = cls.fields[n];\n\t\t\t\t\tcls.fields[n] = tmp;\n\t\t\t\t\tif( n > 1 )\n\t\t\t\t\t\tn-=2;\n\t\t\t\t}\n\t\t\t}\n\t\t\tif( cls.proto === Object.getPrototypeOf( {} ) ) cls.proto = null;\n\t\t},\n\t\tsetDefaultObjectToJSOX( cb ) { objectToJSOX = cb },\n\t\tisEncoding(o) {\n\t\t\t//console.log( \"is object encoding?\", encoding.length, o, encoding );\n\t\t\treturn !!encoding.find( (eo,i)=>eo===o && i < (encoding.length-1) )\n\t\t},\n\t\tencodeObject(o) {\n\t\t\tif( objectToJSOX ) \n\t\t\t\treturn objectToJSOX.apply(o, [this]);\n\t\t\treturn o;\n\t\t},\n\t\tstringify(o,r,s) { return stringify(o,r,s) },\n\t\tsetQuote(q) { useQuote = q; },\n\t\tregisterToJSOX(n,p,f) { return this.toJSOX( n,p,f ) },\n\t\ttoJSOX( name, ptype, f ) {\n\t\t\tif( ptype.prototype && ptype.prototype !== Object.prototype ) {\n\t\t\t\tif( localToProtoTypes.get(ptype.prototype) ) throw new Error( \"Existing toJSOX has been registered for prototype\" );\n\t\t\t\tlocalToProtoTypes.set( ptype.prototype, { external:true, name:name||f.constructor.name, cb:f } );\n\t\t\t} else {\n\t\t\t\tlet key = Object.keys( ptype ).toString();\n\t\t\t\tif( localToObjectTypes.get(key) ) throw new Error( \"Existing toJSOX has been registered for object type\" );\n\t\t\t\tlocalToObjectTypes.set( key, { external:true, name:name, cb:f } );\n\t\t\t}\n\t\t},\n\t\tget ignoreNonEnumerable() { return ignoreNonEnumerable; },\n\t\tset ignoreNonEnumerable(val) { ignoreNonEnumerable = val; },\n\t}\n\treturn stringifier;\n\n\t/**\n\t * get a reference to a previously seen object\n\t * @param {any} here \n\t * @returns reference to existing object, or undefined if not found.\n\t */\n\tfunction getReference( here ) {\n\t\tif( here === null ) return undefined;\n\t\tlet field = fieldMap.get( here );\n\t\t//_DEBUG_STRINGIFY && console.log( \"path:\", _JSON.stringify(path), field );\n\t\tif( !field ) {\n\t\t\tfieldMap.set( here, _JSON.stringify(path) );\n\t\t\treturn undefined;\n\t\t}\n\t\treturn \"ref\"+field;\n\t}\n\n\n\t/**\n\t * find the prototype definition for a class\n\t * @param {object} o \n\t * @param {map} useK \n\t * @returns object\n\t */\n\tfunction matchObject(o,useK) {\n\t\tlet k;\n\t\tlet cls;\n\t\tlet prt = Object.getPrototypeOf(o);\n\t\tcls = classes.find( cls=>{\n\t\t\tif( cls.proto && cls.proto === prt ) return true;\n\t\t} );\n\t\tif( cls ) return cls;\n\n\t\tif( classes.length || commonClasses.length ) {\n\t\t\tif( useK )  {\n\t\t\t\tuseK = useK.map( v=>{ if( typeof v === \"string\" ) return v; else return undefined; } );\n\t\t\t\tk = useK.toString();\n\t\t\t} else {\n\t\t\t\tlet denormKeys = Object.keys(o);\n\t\t\t\tfor( let i = 1; i < denormKeys.length; i++ ) {\n\t\t\t\t\tlet a, b;\n\t\t\t\t\tif( ( a = denormKeys[i-1] ) > ( b = denormKeys[i] ) ) {\n\t\t\t\t\t\tdenormKeys[i-1] = b;\n\t\t\t\t\t\tdenormKeys[i] = a;\n\t\t\t\t\t\tif( i ) i-=2; // go back 2, this might need to go further pack.\n\t\t\t\t\t\telse i--; // only 1 to check.\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tk = denormKeys.toString();\n\t\t\t}\n\t\t\tcls = classes.find( cls=>{\n\t\t\t\tif( cls.tag === k ) return true;\n\t\t\t} );\n\t\t\tif( !cls )\n\t\t\t\tcls = commonClasses.find( cls=>{\n\t\t\t\t\tif( cls.tag === k ) return true;\n\t\t\t\t} );\n\t\t}\n\t\treturn cls;\n\t}\n\n\t/**\n\t * Serialize an object to JSOX text.\n\t * @param {any} object \n\t * @param {(key:string,value:any)=>string} replacer \n\t * @param {string|number} space \n\t * @returns \n\t */\n\tfunction stringify( object, replacer, space ) {\n\t\tif( object === undefined ) return \"undefined\";\n\t\tif( object === null ) return;\n\t\tlet gap;\n\t\tlet indent;\n\t\tlet rep;\n\n\t\tlet i;\n\t\tconst spaceType = typeof space;\n\t\tconst repType = typeof replacer;\n\t\tgap = \"\";\n\t\tindent = \"\";\n\n\t\t// If the space parameter is a number, make an indent string containing that\n\t\t// many spaces.\n\n\t\tif (spaceType === \"number\") {\n\t\t\tfor (i = 0; i < space; i += 1) {\n\t\t\t\tindent += \" \";\n\t\t\t}\n\n\t\t// If the space parameter is a string, it will be used as the indent string.\n\t\t} else if (spaceType === \"string\") {\n\t\t\tindent = space;\n\t\t}\n\n\t\t// If there is a replacer, it must be a function or an array.\n\t\t// Otherwise, throw an error.\n\n\t\trep = replacer;\n\t\tif( replacer && repType !== \"function\"\n\t\t    && ( repType !== \"object\"\n\t\t       || typeof replacer.length !== \"number\"\n\t\t   )) {\n\t\t\tthrow new Error(\"JSOX.stringify\");\n\t\t}\n\n\t\tpath.length = 0;\n\t\tfieldMap = new WeakMap();\n\n\t\tconst finalResult = str( \"\", {\"\":object} );\n\t\tcommonClasses.length = 0;\n\t\treturn finalResult;\n\n\t\t// from https://github.com/douglascrockford/JSON-js/blob/master/json2.js#L181\n\t\tfunction str(key, holder) {\n\t\t\tvar mind = gap;\n\t\t\tconst doArrayToJSOX_ = arrayToJSOX.cb;\n\t\t\tconst mapToObject_ = mapToJSOX.cb;\t\t \n\t\t\tarrayToJSOX.cb = doArrayToJSOX;\n\t\t\tmapToJSOX.cb = mapToObject;\n\t\t\tconst v = str_(key,holder);\n\t\t\tarrayToJSOX.cb = doArrayToJSOX_;\n\t\t\tmapToJSOX.cb = mapToObject_;\n\t\t\treturn v;\n\n\t\t\tfunction doArrayToJSOX() {\n\t\t\t\tlet v;\n\t\t\t\tlet partial = [];\n\t\t\t\tlet thisNodeNameIndex = path.length;\n\n\t\t\t\t// The value is an array. Stringify every element. Use null as a placeholder\n\t\t\t\t// for non-JSOX values.\n\t\t\t\n\t\t\t\tfor (let i = 0; i < this.length; i += 1) {\n\t\t\t\t\tpath[thisNodeNameIndex] = i;\n\t\t\t\t\tpartial[i] = str(i, this) || \"null\";\n\t\t\t\t}\n\t\t\t\tpath.length = thisNodeNameIndex;\n\t\t\t\t//console.log( \"remove encoding item\", thisNodeNameIndex, encoding.length);\n\t\t\t\tencoding.length = thisNodeNameIndex;\n\t\t\t\n\t\t\t\t// Join all of the elements together, separated with commas, and wrap them in\n\t\t\t\t// brackets.\n\t\t\t\tv = ( partial.length === 0\n\t\t\t\t\t? \"[]\"\n\t\t\t\t\t: gap\n\t\t\t\t\t\t? [\n\t\t\t\t\t\t\t\"[\\n\"\n\t\t\t\t\t\t\t, gap\n\t\t\t\t\t\t\t, partial.join(\",\\n\" + gap)\n\t\t\t\t\t\t\t, \"\\n\"\n\t\t\t\t\t\t\t, mind\n\t\t\t\t\t\t\t, \"]\"\n\t\t\t\t\t\t].join(\"\")\n\t\t\t\t\t\t: \"[\" + partial.join(\",\") + \"]\" );\n\t\t\t\treturn v;\n\t\t\t} \n\t\t\tfunction mapToObject(){\n\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"---------- NEW MAP -------------\" );\n\t\t\t\tlet tmp = {tmp:null};\n\t\t\t\tlet out = '{'\n\t\t\t\tlet first = true;\n\t\t\t\t//console.log( \"CONVERT:\", map);\n\t\t\t\tfor (let [key, value] of this) {\n\t\t\t\t\t//console.log( \"er...\", key, value )\n\t\t\t\t\ttmp.tmp = value;\n\t\t\t\t\tlet thisNodeNameIndex = path.length;\n\t\t\t\t\tpath[thisNodeNameIndex] = key;\n\t\t\t\t\t\t\t\n\t\t\t\t\tout += (first?\"\":\",\") + getIdentifier(key) +':' + str(\"tmp\", tmp);\n\t\t\t\t\tpath.length = thisNodeNameIndex;\n\t\t\t\t\tfirst = false;\n\t\t\t\t}\n\t\t\t\tout += '}';\n\t\t\t\t//console.log( \"out is:\", out );\n\t\t\t\treturn out;\n\t\t\t}\n\n\t\t// Produce a string from holder[key].\n\t\tfunction str_(key, holder) {\n\n\t\t\tlet i;          // The loop counter.\n\t\t\tlet k;          // The member key.\n\t\t\tlet v;          // The member value.\n\t\t\tlet length;\n\t\t\tlet partialClass;\n\t\t\tlet partial;\n\t\t\tlet thisNodeNameIndex = path.length;\n\t\t\tlet isValue = true;\n\t\t\tlet value = holder[key];\n\t\t\tlet isObject = (typeof value === \"object\");\n\t\t\tlet c;\n\n\t\t\tif( isObject && ( value !== null ) ) {\n\t\t\t\tif( objectToJSOX ){\n\t\t\t\t\tif( !stringifying.find( val=>val===value ) ) {\n\t\t\t\t\t\tstringifying.push( value );\n\t\t\t\t\t\tencoding[thisNodeNameIndex] = value;\n\t\t\t\t\t\tisValue = false;\n\t\t\t\t\t\tvalue = objectToJSOX.apply(value, [stringifier]);\n\t\t\t\t\t\t//console.log( \"Converted by object lookup -it's now a different type\"\n\t\t\t\t\t\t//\t, protoConverter, objectConverter );\n\t\t\t\t\t\tisObject = ( typeof value === \"object\" );\n\t\t\t\t\t\tstringifying.pop();\n\t\t\t\t\t\tencoding.length = thisNodeNameIndex;\n\t\t\t\t\t\tisObject = (typeof value === \"object\");\n\t\t\t\t\t}\n\t\t\t\t\t//console.log( \"Value convereted to:\", key, value );\n\t\t\t\t}\n\t\t\t}\n\t\t\tconst objType = (value !== undefined && value !== null) && Object.getPrototypeOf( value );\n\t\t\t\n\t\t\tlet protoConverter = objType\n\t\t\t\t&& ( localToProtoTypes.get( objType ) \n\t\t\t\t|| toProtoTypes.get( objType ) \n\t\t\t\t|| null )\n\t\t\tlet objectConverter = !protoConverter && (value !== undefined && value !== null) \n\t\t\t\t&& ( localToObjectTypes.get( Object.keys( value ).toString() ) \n\t\t\t\t|| toObjectTypes.get( Object.keys( value ).toString() ) \n\t\t\t\t|| null )\n\n\t\t\t// If we were called with a replacer function, then call the replacer to\n\t\t\t// obtain a replacement value.\n\n\t\t\tif (typeof rep === \"function\") {\n\t\t\t\tisValue = false;\n\t\t\t\tvalue = rep.call(holder, key, value);\n\t\t\t}\n\t\t\t\t//console.log( \"PROTOTYPE:\", Object.getPrototypeOf( value ) )\n\t\t\t\t//console.log( \"PROTOTYPE:\", toProtoTypes.get(Object.getPrototypeOf( value )) )\n\t\t\t\t//if( protoConverter )\n\t\t\t//_DEBUG_STRINGIFY && console.log( \"TEST()\", value, protoConverter, objectConverter );\n\n\t\t\tlet toJSOX = ( protoConverter && protoConverter.cb ) \n\t\t\t          || ( objectConverter && objectConverter.cb );\n\t\t\t// If the value has a toJSOX method, call it to obtain a replacement value.\n\t\t\t//_DEBUG_STRINGIFY && console.log( \"type:\", typeof value, protoConverter, !!toJSOX, path );\n\n\t\t\tif( value !== undefined\n\t\t\t    && value !== null\n\t\t\t\t&& typeof value === \"object\"\n\t\t\t    && typeof toJSOX === \"function\"\n\t\t\t) {\n\t\t\t\tif( !stringifying.find( val=>val===value ) ) {\n\t\t\t\t\tif( typeof value === \"object\" ) {\n\t\t\t\t\t\tv = getReference( value );\n\t\t\t\t\t\tif( v )\treturn v;\n\t\t\t\t\t}\n\n\t\t\t\t\tstringifying.push( value );\n\t\t\t\t\tencoding[thisNodeNameIndex] = value;\n\t\t\t\t\tvalue = toJSOX.call(value, stringifier);\n\t\t\t\t\tisValue = false;\n\t\t\t\t\tstringifying.pop();\n\t\t\t\t\tif( protoConverter && protoConverter.name ) {\n\t\t\t\t\t\t// stringify may return a unquoted string\n\t\t\t\t\t\t// which needs an extra space betwen its tag and value.\n\t\t\t\t\t\tif( \"string\" === typeof value \n\t\t\t\t\t\t\t&& value[0] !== '-'\n\t\t\t\t\t\t\t&& (value[0] < '0' || value[0] > '9' )\n\t\t\t\t\t\t\t&& value[0] !== '\"'\n\t\t\t\t\t\t\t&& value[0] !== '\\'' \n\t\t\t\t\t\t\t&& value[0] !== '`' \n\t\t\t\t\t\t\t&& value[0] !== '[' \n\t\t\t\t\t\t\t&& value[0] !== '{' \n\t\t\t\t\t\t\t){\n\t\t\t\t\t\t\tvalue = ' ' + value;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t//console.log( \"Value converted:\", value );\n\t\t\t\t\tencoding.length = thisNodeNameIndex;\n\t\t\t\t} else {\n\t\t\t\t\tv = getReference( value );\n\t\t\t\t}\n\t\t} else \n\t\t\t\tif( typeof value === \"object\" ) {\n\t\t\t\t\tv = getReference( value );\n\t\t\t\t\tif( v ) return v;\n\t\t\t\t}\n\n\t\t\t// What happens next depends on the value's type.\n\t\t\tswitch (typeof value) {\n\t\t\tcase \"bigint\":\n\t\t\t\treturn value + 'n';\n\t\t\tcase \"string\":\n\t\t\t\t{\n\t\t\t\t\t//console.log( `Value was converted before?  [${value}]`);\n\t\t\t\t\tvalue = isValue?getIdentifier(value):value;\n\t\t\t\t\tlet c = '';\n\t\t\t\t\tif( key===\"\" )\n\t\t\t\t\t\tc = classes.map( cls=> cls.name+\"{\"+cls.fields.join(\",\")+\"}\" ).join(gap?\"\\n\":\"\")+\n\t\t\t\t\t\t    commonClasses.map( cls=> cls.name+\"{\"+cls.fields.join(\",\")+\"}\" ).join(gap?\"\\n\":\"\")\n\t\t\t\t\t\t\t\t+(gap?\"\\n\":\"\");\n\t\t\t\t\tif( protoConverter && protoConverter.external ) \n\t\t\t\t\t\treturn c + protoConverter.name + value;\n\t\t\t\t\tif( objectConverter && objectConverter.external ) \n\t\t\t\t\t\treturn c + objectConverter.name + value;\n\t\t\t\t\treturn c + value;//useQuote+JSOX.escape( value )+useQuote;\n\t\t\t\t}\n\t\t\tcase \"number\":\n\t\t\tcase \"boolean\":\n\t\t\tcase \"null\":\n\n\t\t\t\t// If the value is a boolean or null, convert it to a string. Note:\n\t\t\t\t// typeof null does not produce \"null\". The case is included here in\n\t\t\t\t// the remote chance that this gets fixed someday.\n\n\t\t\t\treturn String(value);\n\n\t\t\t\t// If the type is \"object\", we might be dealing with an object or an array or\n\t\t\t\t// null.\n\n\t\t\tcase \"object\":\n\t\t\t\t//_DEBUG_STRINGIFY && console.log( \"ENTERINT OBJECT EMISSION WITH:\", v );\n\t\t\t\tif( v ) return \"ref\"+v;\n\n\t\t\t\t// Due to a specification blunder in ECMAScript, typeof null is \"object\",\n\t\t\t\t// so watch out for that case.\n\t\t\t\tif (!value) {\n\t\t\t\t\treturn \"null\";\n\t\t\t\t}\n\n\t\t\t\t// Make an array to hold the partial results of stringifying this object value.\n\t\t\t\tgap += indent;\n\t\t\t\tpartialClass = null;\n\t\t\t\tpartial = [];\n\n\t\t\t\t// If the replacer is an array, use it to select the members to be stringified.\n\t\t\t\tif (rep && typeof rep === \"object\") {\n\t\t\t\t\tlength = rep.length;\n\t\t\t\t\tpartialClass = matchObject( value, rep );\n\t\t\t\t\tfor (i = 0; i < length; i += 1) {\n\t\t\t\t\t\tif (typeof rep[i] === \"string\") {\n\t\t\t\t\t\t\tk = rep[i];\n\t\t\t\t\t\t\tpath[thisNodeNameIndex] = k;\n\t\t\t\t\t\t\tv = str(k, value);\n\n\t\t\t\t\t\t\tif (v !== undefined ) {\n\t\t\t\t\t\t\t\tif( partialClass ) {\n\t\t\t\t\t\t\t\t\tpartial.push(v);\n\t\t\t\t\t\t\t\t} else\n\t\t\t\t\t\t\t\t\tpartial.push( getIdentifier(k) \n\t\t\t\t\t\t\t\t\t+ (\n\t\t\t\t\t\t\t\t\t\t(gap)\n\t\t\t\t\t\t\t\t\t\t\t? \": \"\n\t\t\t\t\t\t\t\t\t\t\t: \":\"\n\t\t\t\t\t\t\t\t\t) + v);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tpath.splice( thisNodeNameIndex, 1 );\n\t\t\t\t} else {\n\n\t\t\t\t\t// Otherwise, iterate through all of the keys in the object.\n\t\t\t\t\tpartialClass = matchObject( value );\n\t\t\t\t\tlet keys = [];\n\t\t\t\t\tfor (k in value) {\n\t\t\t\t\t\tif( ignoreNonEnumerable )\n\t\t\t\t\t\t\tif( !Object.prototype.propertyIsEnumerable.call( value, k ) ){\n\t\t\t\t\t\t\t\t//_DEBUG_STRINGIFY && console.log( \"skipping non-enuerable?\", k );\n\t\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\tif (Object.prototype.hasOwnProperty.call(value, k)) {\n\t\t\t\t\t\t\tlet n;\n\t\t\t\t\t\t\tfor( n = 0; n < keys.length; n++ ) \n\t\t\t\t\t\t\t\tif( keys[n] > k ) {\t\n\t\t\t\t\t\t\t\t\tkeys.splice(n,0,k );\n\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif( n == keys.length )\n\t\t\t\t\t\t\t\tkeys.push(k);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tfor(let n = 0; n < keys.length; n++) {\n\t\t\t\t\t\tk = keys[n];\n\t\t\t\t\t\tif (Object.prototype.hasOwnProperty.call(value, k)) {\n\t\t\t\t\t\t\tpath[thisNodeNameIndex] = k;\n\t\t\t\t\t\t\tv = str(k, value);\n\n\t\t\t\t\t\t\tif (v !== undefined ) {\n\t\t\t\t\t\t\t\tif( partialClass ) {\n\t\t\t\t\t\t\t\t\tpartial.push(v);\n\t\t\t\t\t\t\t\t} else\n\t\t\t\t\t\t\t\t\tpartial.push(getIdentifier(k) + (\n\t\t\t\t\t\t\t\t\t\t(gap)\n\t\t\t\t\t\t\t\t\t\t\t? \": \"\n\t\t\t\t\t\t\t\t\t\t\t: \":\"\n\t\t\t\t\t\t\t\t\t) + v);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tpath.splice( thisNodeNameIndex, 1 );\n\t\t\t\t}\n\n\t\t\t\t// Join all of the member texts together, separated with commas,\n\t\t\t\t// and wrap them in braces.\n\t\t\t\t//_DEBUG_STRINGIFY && console.log( \"partial:\", partial )\n\n\t\t\t\t//let c;\n\t\t\t\tif( key===\"\" )\n\t\t\t\t\tc = ( classes.map( cls=> cls.name+\"{\"+cls.fields.join(\",\")+\"}\" ).join(gap?\"\\n\":\"\")\n\t\t\t\t\t\t|| commonClasses.map( cls=> cls.name+\"{\"+cls.fields.join(\",\")+\"}\" ).join(gap?\"\\n\":\"\"))+(gap?\"\\n\":\"\");\n\t\t\t\telse\n\t\t\t\t\tc = '';\n\n\t\t\t\tif( protoConverter && protoConverter.external ) \n\t\t\t\t\tc = c + getIdentifier(protoConverter.name);\n\n\t\t\t\t//_DEBUG_STRINGIFY && console.log( \"PREFIX FOR THIS FIELD:\", c );\n\t\t\t\tlet ident = null;\n\t\t\t\tif( partialClass )\n\t\t\t\t\tident = getIdentifier( partialClass.name ) ;\n\t\t\t\tv = c +\n\t\t\t\t\t( partial.length === 0\n\t\t\t\t\t? \"{}\"\n\t\t\t\t\t: gap\n\t\t\t\t\t\t\t? (partialClass?ident:\"\")+\"{\\n\" + gap + partial.join(\",\\n\" + gap) + \"\\n\" + mind + \"}\"\n\t\t\t\t\t\t\t: (partialClass?ident:\"\")+\"{\" + partial.join(\",\") + \"}\"\n\t\t\t\t\t);\n\n\t\t\t\tgap = mind;\n\t\t\t\treturn v;\n\t\t\t}\n\t\t}\n\t}\n\n\t}\n}\n\n\t// Converts an ArrayBuffer directly to base64, without any intermediate 'convert to string then\n\t// use window.btoa' step. According to my tests, this appears to be a faster approach:\n\t// http://jsperf.com/encoding-xhr-image-data/5\n\t// doesn't have to be reversable....\n\tconst encodings = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789$_'\n\tconst decodings = { '~':-1\n\t\t,'=':-1\n\t\t,'$':62\n\t\t,'_':63\n\t\t,'+':62\n\t\t,'-':62\n\t\t,'.':62\n\t\t,'/':63\n\t\t,',':63\n\t};\n\t\n\tfor( let x = 0; x < encodings.length; x++ ) {\n\t\tdecodings[encodings[x]] = x;\n\t}\n\tObject.freeze( decodings );\n\t\n\tfunction base64ArrayBuffer(arrayBuffer) {\n\t\tlet base64    = ''\n\t\n\t\tlet bytes         = new Uint8Array(arrayBuffer)\n\t\tlet byteLength    = bytes.byteLength\n\t\tlet byteRemainder = byteLength % 3\n\t\tlet mainLength    = byteLength - byteRemainder\n\t\n\t\tlet a, b, c, d\n\t\tlet chunk\n\t\t//throw \"who's using this?\"\n\t\t//console.log( \"buffer..\", arrayBuffer )\n\t\t// Main loop deals with bytes in chunks of 3\n\t\tfor (let i = 0; i < mainLength; i = i + 3) {\n\t\t\t// Combine the three bytes into a single integer\n\t\t\tchunk = (bytes[i] << 16) | (bytes[i + 1] << 8) | bytes[i + 2]\n\n\t\t\t// Use bitmasks to extract 6-bit segments from the triplet\n\t\t\ta = (chunk & 16515072) >> 18 // 16515072 = (2^6 - 1) << 18\n\t\t\tb = (chunk & 258048)   >> 12 // 258048   = (2^6 - 1) << 12\n\t\t\tc = (chunk & 4032)     >>  6 // 4032     = (2^6 - 1) << 6\n\t\t\td = chunk & 63               // 63       = 2^6 - 1\n\t\n\t\t\t// Convert the raw binary segments to the appropriate ASCII encoding\n\t\t\tbase64 += encodings[a] + encodings[b] + encodings[c] + encodings[d]\n\t\t}\n\t\n\t// Deal with the remaining bytes and padding\n\t\tif (byteRemainder == 1) {\n\t\t\tchunk = bytes[mainLength]\n\t\t\ta = (chunk & 252) >> 2 // 252 = (2^6 - 1) << 2\n\t\t\t// Set the 4 least significant bits to zero\n\t\t\tb = (chunk & 3)   << 4 // 3   = 2^2 - 1\n\t\t\tbase64 += encodings[a] + encodings[b] + '=='\n\t\t} else if (byteRemainder == 2) {\n\t\t\tchunk = (bytes[mainLength] << 8) | bytes[mainLength + 1]\n\t\t\ta = (chunk & 64512) >> 10 // 64512 = (2^6 - 1) << 10\n\t\t\tb = (chunk & 1008)  >>  4 // 1008  = (2^6 - 1) << 4\n\t\t\t// Set the 2 least significant bits to zero\n\t\t\tc = (chunk & 15)    <<  2 // 15    = 2^4 - 1\n\t\t\tbase64 += encodings[a] + encodings[b] + encodings[c] + '='\n\t\t}\n\t\t//console.log( \"dup?\", base64)\n\t\treturn base64\n\t}\n\t\n\t\n\tfunction DecodeBase64( buf ) {\t\n\t\tlet outsize;\n\t\tif( buf.length % 4 == 1 )\n\t\t\toutsize = ((((buf.length + 3) / 4)|0) * 3) - 3;\n\t\telse if( buf.length % 4 == 2 )\n\t\t\toutsize = ((((buf.length + 3) / 4)|0) * 3) - 2;\n\t\telse if( buf.length % 4 == 3 )\n\t\t\toutsize = ((((buf.length + 3) / 4)|0) * 3) - 1;\n\t\telse if( decodings[buf[buf.length - 3]] == -1 )\n\t\t\toutsize = ((((buf.length + 3) / 4)|0) * 3) - 3;\n\t\telse if( decodings[buf[buf.length - 2]] == -1 ) \n\t\t\toutsize = ((((buf.length + 3) / 4)|0) * 3) - 2;\n\t\telse if( decodings[buf[buf.length - 1]] == -1 ) \n\t\t\toutsize = ((((buf.length + 3) / 4)|0) * 3) - 1;\n\t\telse\n\t\t\toutsize = ((((buf.length + 3) / 4)|0) * 3);\n\t\tlet ab = new ArrayBuffer( outsize );\n\t\tlet out = new Uint8Array(ab);\n\n\t\tlet n;\n\t\tlet l = (buf.length+3)>>2;\n\t\tfor( n = 0; n < l; n++ ) {\n\t\t\tlet index0 = decodings[buf[n*4]];\n\t\t\tlet index1 = (n*4+1)<buf.length?decodings[buf[n*4+1]]:-1;\n\t\t\tlet index2 = (index1>=0) && (n*4+2)<buf.length?decodings[buf[n*4+2]]:-1 || 0;\n\t\t\tlet index3 = (index2>=0) && (n*4+3)<buf.length?decodings[buf[n*4+3]]:-1 || 0;\n\t\t\tif( index1 >= 0 )\n\t\t\t\tout[n*3+0] = (( index0 ) << 2 | ( index1 ) >> 4);\n\t\t\tif( index2 >= 0 )\n\t\t\t\tout[n*3+1] = (( index1 ) << 4 | ( ( ( index2 ) >> 2 ) & 0x0f ));\n\t\t\tif( index3 >= 0 )\n\t\t\t\tout[n*3+2] = (( index2 ) << 6 | ( ( index3 ) & 0x3F ));\n\t\t}\n\n\t\treturn ab;\n\t}\n\t\n/**\n * @param {unknown} object \n * @param {(this: unknown, key: string, value: unknown)} [replacer] \n * @param {string | number} [space] \n * @returns {string}\n */\t\nJSOX.stringify = function( object, replacer, space ) {\n\tlet stringifier = JSOX.stringifier();\n\treturn stringifier.stringify( object, replacer, space );\n}\n\nconst nonIdent = \n[ [ 0,256,[ 0xffd9ff,0xff6aff,0x1fc00,0x380000,0x0,0xfffff8,0xffffff,0x7fffff] ]\n].map( row=>{ return{ firstChar : row[0], lastChar: row[1], bits : row[2] }; } );\n\n//} privateizeEverything();\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsox/lib/jsox.mjs\n");

/***/ })

};
;