import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Trash2, Star } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Testimonial } from "./types";

interface TestimonialCardProps {
    testimonial: Testimonial;
    isAdmin: boolean;
    onToggleApproval: (id: string, currentStatus: boolean) => void;
    onDelete: (id: string) => void;
}

const TestimonialCard = ({
    testimonial,
    isAdmin,
    onToggleApproval,
    onDelete,
}: TestimonialCardProps) => {
    const renderStars = (rating: number) => {
        return Array(5)
            .fill(0)
            .map((_, i) => (
                <Star
                    key={i}
                    className={`h-4 w-4 ${
                        i < rating
                            ? "text-spark-yellow fill-spark-yellow"
                            : "text-gray-300"
                    }`}
                />
            ));
    };

    return (
        <Card key={testimonial.id}>
            <CardContent className="p-6">
                <div className="flex justify-between items-start">
                    <div className="flex items-start space-x-4">
                        <Avatar className="h-12 w-12">
                            <AvatarImage
                                src={
                                    testimonial.avatar_url ||
                                    `https://ui-avatars.com/api/?name=${encodeURIComponent(
                                        testimonial.author
                                    )}&background=random`
                                }
                                alt={testimonial.author}
                            />
                            <AvatarFallback>
                                {testimonial.author
                                    .substring(0, 2)
                                    .toUpperCase()}
                            </AvatarFallback>
                        </Avatar>
                        <div>
                            <div className="font-medium">
                                {testimonial.author}
                            </div>
                            <div className="text-sm text-gray-500">
                                {testimonial.role || "No role provided"}
                            </div>
                            <div className="flex mt-1">
                                {renderStars(testimonial.rating)}
                            </div>
                        </div>
                    </div>
                    {isAdmin && (
                        <div className="flex items-center space-x-4">
                            <div className="flex items-center space-x-2">
                                <Switch
                                    checked={testimonial.approved || false}
                                    onCheckedChange={() =>
                                        onToggleApproval(
                                            testimonial.id,
                                            testimonial.approved || false
                                        )
                                    }
                                    id={`approve-${testimonial.id}`}
                                />
                                <span className="text-sm">
                                    {testimonial.approved
                                        ? "Approved"
                                        : "Unapproved"}
                                </span>
                            </div>
                            <Button
                                variant="destructive"
                                size="icon"
                                onClick={() => onDelete(testimonial.id)}
                            >
                                <Trash2 className="h-4 w-4" />
                            </Button>
                        </div>
                    )}
                </div>

                <div className="mt-4 p-4 rounded bg-gray-50">
                    <p className="italic">&quot;{testimonial.content}&quot;</p>
                </div>

                <div className="mt-4 flex justify-between items-center">
                    <div className="text-sm text-gray-500">
                        Submitted:{" "}
                        {new Date(testimonial.created_at).toLocaleDateString()}
                    </div>
                    <Badge
                        variant={
                            testimonial.rating >= 4 ? "default" : "secondary"
                        }
                        className={
                            testimonial.rating >= 4
                                ? "bg-green-100 text-green-800"
                                : ""
                        }
                    >
                        {testimonial.rating >= 4 ? "High Rating" : "Low Rating"}
                    </Badge>
                </div>
            </CardContent>
        </Card>
    );
};

export default TestimonialCard;
