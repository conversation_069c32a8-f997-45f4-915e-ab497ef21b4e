import React from "react";
import { Check } from "lucide-react";

interface CheckoutSummaryProps {
    planName: string;
    monthlyPrice: string;
    billingInfo: string;
}

const CheckoutSummary = ({
    monthlyPrice,
    billingInfo,
}: CheckoutSummaryProps) => {
    return (
        <div className="mb-8 p-6 bg-white rounded-lg border border-gray-200">
            <h2 className="text-2xl font-semibold text-gray-800 mb-2">
                Unlock everything for
            </h2>

            <div className="mb-4">
                <span className="text-4xl sm:text-5xl font-bold text-[#9b87f5]">
                    ${monthlyPrice}
                </span>
                <span className="text-2xl sm:text-3xl text-gray-600">
                    {" "}
                    per month
                </span>
            </div>

            <p className="text-lg text-gray-600 mb-6">
                {billingInfo}. Cancel anytime.
            </p>

            <div className="space-y-4 text-left">
                <div className="flex gap-3">
                    <Check className="h-6 w-6 text-green-500 flex-shrink-0 mt-1" />
                    <p className="text-gray-700">
                        <strong>Instant Access to All Creative Tools</strong>
                        <br />
                        Kids can dive right into storytelling, art, music, and
                        more—powered by safe, kid-friendly AI.
                    </p>
                </div>

                <div className="flex gap-3">
                    <Check className="h-6 w-6 text-green-500 flex-shrink-0 mt-1" />
                    <p className="text-gray-700">
                        <strong>
                            A Private, Secure Account for Your Child
                        </strong>
                        <br />
                        No ads, no data sharing—just a space designed for safe
                        exploration and creative play.
                    </p>
                </div>

                <div className="flex gap-3">
                    <Check className="h-6 w-6 text-green-500 flex-shrink-0 mt-1" />
                    <p className="text-gray-700">
                        <strong>
                            Unlimited Creative Projects, Saved Privately
                        </strong>
                        <br />
                        Kids can create as many stories, drawings, songs, and
                        games as they want—and everything is saved securely in
                        their own space to revisit, share, or build on later.
                    </p>
                </div>
            </div>
        </div>
    );
};

export default CheckoutSummary;
