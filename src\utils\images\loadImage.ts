
/**
 * Loads an image from a URL to a canvas element
 * @param imageUrl URL of the image to load
 * @param canvasRef Reference to the canvas element
 * @param callback Optional callback to execute after image is loaded
 * @param errorCallback Optional callback to execute if image loading fails
 */
export const loadImageToCanvas = (
  imageUrl: string,
  canvasRef: React.RefObject<HTMLCanvasElement>,
  callback?: () => void,
  errorCallback?: (error: ErrorEvent) => void
): void => {
  if (!canvasRef.current) return;
  
  const canvas = canvasRef.current;
  const ctx = canvas.getContext('2d');
  
  if (!ctx) {
    console.error("Could not get canvas context");
    if (errorCallback) errorCallback({ type: "error", message: "Could not get canvas context" } as ErrorEvent);
    return;
  }
  
  // Clear the canvas first in case loading takes time
  ctx.clearRect(0, 0, canvas.width, canvas.height);
  
  // Draw loading text
  ctx.font = "16px Arial";
  ctx.fillStyle = "#666";
  ctx.textAlign = "center";
  ctx.fillText("Loading image...", canvas.width / 2, canvas.height / 2);
  
  const img = new Image();
  
  // Set up error handling with retry mechanism for web URLs
  let retryCount = 0;
  const maxRetries = 2;
  
  const loadWithRetry = () => {
    // Set up error handling
    img.onerror = (error) => {
      console.error("Error loading image:", error, imageUrl);
      
      if (imageUrl.startsWith('http') && retryCount < maxRetries) {
        retryCount++;
        console.log(`Retrying image load (${retryCount}/${maxRetries})...`);
        setTimeout(loadWithRetry, 1000); // Wait 1 second before retrying
        return;
      }
      
      // If we've exhausted retries or it's a local image, show error
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      ctx.font = "16px Arial";
      ctx.fillStyle = "red";
      ctx.textAlign = "center";
      ctx.fillText("Failed to load image", canvas.width / 2, canvas.height / 2 - 10);
      ctx.fillText("Please try again", canvas.width / 2, canvas.height / 2 + 20);
      
      if (errorCallback) errorCallback(error as ErrorEvent);
    };

    // Set up image loading
    img.onload = () => {
      // Clear the canvas first
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      // Calculate scaling to fit the image in the canvas while maintaining aspect ratio
      const scale = Math.min(
        canvas.width / img.width,
        canvas.height / img.height
      );
      
      // Calculate centered position
      const x = (canvas.width - img.width * scale) / 2;
      const y = (canvas.height - img.height * scale) / 2;
      
      // Draw the image on the canvas
      ctx.drawImage(img, x, y, img.width * scale, img.height * scale);
      
      // Execute callback if provided
      if (callback) callback();
    };
    
    // For CORS issues with remote images, try setting crossOrigin
    if (imageUrl.startsWith('http')) {
      img.crossOrigin = "Anonymous";
    }
    
    // Start loading the image
    img.src = imageUrl;
  };
  
  loadWithRetry();
};
