const { PrismaClient } = require('../src/generated/prisma');

const prisma = new PrismaClient();

async function fixTrialUsedFlag() {
  try {
    console.log('🔧 Starting trial_used flag fix...');
    
    // Find all accounts that have active subscriptions but trial_used is still false
    // These are users who have completed their trial and moved to paid subscriptions
    const accountsNeedingTrialUsedFix = await prisma.profile.findMany({
      where: {
        AND: [
          {
            OR: [
              { subscription_status: 'active' },
              { subscription_status: 'cancel_at_period_end' }
            ]
          },
          { trial_used: false },
          { trial_end: { not: null } } // They had a trial
        ]
      },
      include: {
        payments: {
          where: {
            status: 'succeeded'
          },
          orderBy: {
            created_at: 'desc'
          },
          take: 1
        }
      }
    });

    console.log(`Found ${accountsNeedingTrialUsedFix.length} accounts with active subscriptions but trial_used: false`);

    if (accountsNeedingTrialUsedFix.length === 0) {
      console.log('✅ No accounts need trial_used flag fixing');
      return;
    }

    // Show accounts that will be fixed
    console.log('\n📋 Accounts to be fixed:');
    accountsNeedingTrialUsedFix.forEach((account, index) => {
      const trialEndDate = account.trial_end ? new Date(account.trial_end).toISOString().split('T')[0] : 'N/A';
      const hasPayments = account.payments.length > 0;
      console.log(`${index + 1}. ${account.email} - Status: ${account.subscription_status} - Trial End: ${trialEndDate} - Has Payments: ${hasPayments}`);
    });

    console.log('\n🔄 Updating trial_used flags...');

    const updateResults = [];
    for (const account of accountsNeedingTrialUsedFix) {
      try {
        // Check if trial has ended (either by date or by having successful payments)
        const trialHasEnded = account.trial_end && new Date() > new Date(account.trial_end);
        const hasSuccessfulPayments = account.payments.length > 0;
        
        // Only update if trial has actually ended or they have payments
        if (trialHasEnded || hasSuccessfulPayments) {
          await prisma.profile.update({
            where: { id: account.id },
            data: {
              trial_used: true,
              updated_at: new Date()
            }
          });

          updateResults.push({
            email: account.email,
            success: true,
            reason: trialHasEnded ? 'trial_ended' : 'has_payments'
          });

          const reason = trialHasEnded ? 'trial ended' : 'has successful payments';
          console.log(`✅ Updated ${account.email}: trial_used → true (${reason})`);
        } else {
          updateResults.push({
            email: account.email,
            success: false,
            reason: 'trial_still_active'
          });

          console.log(`⏭️ Skipped ${account.email}: trial still active and no payments`);
        }

      } catch (error) {
        console.error(`❌ Failed to update ${account.email}:`, error.message);
        updateResults.push({
          email: account.email,
          success: false,
          error: error.message
        });
      }
    }

    // Also check for any accounts that have past trial end dates but are still marked as trialing
    console.log('\n🔍 Checking for expired trials still marked as trialing...');
    
    const expiredTrialsStillTrialing = await prisma.profile.findMany({
      where: {
        subscription_status: 'trialing',
        trial_end: {
          lt: new Date() // Trial has expired
        },
        trial_used: false
      }
    });

    console.log(`Found ${expiredTrialsStillTrialing.length} expired trials still marked as trialing`);

    for (const account of expiredTrialsStillTrialing) {
      try {
        await prisma.profile.update({
          where: { id: account.id },
          data: {
            trial_used: true,
            updated_at: new Date()
          }
        });

        updateResults.push({
          email: account.email,
          success: true,
          reason: 'expired_trial'
        });

        console.log(`✅ Updated ${account.email}: trial_used → true (expired trial)`);

      } catch (error) {
        console.error(`❌ Failed to update ${account.email}:`, error.message);
        updateResults.push({
          email: account.email,
          success: false,
          error: error.message
        });
      }
    }

    // Summary
    const successCount = updateResults.filter(r => r.success).length;
    const failureCount = updateResults.filter(r => !r.success).length;
    const skippedCount = updateResults.filter(r => !r.success && r.reason === 'trial_still_active').length;

    console.log('\n📊 Summary:');
    console.log(`✅ Successfully updated: ${successCount} accounts`);
    console.log(`⏭️ Skipped (trial still active): ${skippedCount} accounts`);
    console.log(`❌ Failed to update: ${failureCount - skippedCount} accounts`);
    
    if (successCount > 0) {
      console.log('\n🎉 Trial_used flag fix completed successfully!');
      console.log('Users who have completed their trials now have trial_used: true');
    }

    // Show breakdown by reason
    const reasonCounts = updateResults.reduce((acc, result) => {
      const reason = result.reason || 'unknown';
      acc[reason] = (acc[reason] || 0) + 1;
      return acc;
    }, {});

    console.log('\n📈 Update reasons:');
    Object.entries(reasonCounts).forEach(([reason, count]) => {
      console.log(`  ${reason}: ${count} accounts`);
    });

  } catch (error) {
    console.error('❌ Error during trial_used flag fix:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the fix
fixTrialUsedFlag();
