
/**
 * Draws a superhero shape on the canvas
 */
export const drawSuperheroShape = (ctx: CanvasRenderingContext2D, x: number, y: number, size: number) => {
  // Head
  ctx.arc(x, y - size*0.45, size*0.15, 0, 2 * Math.PI);
  
  // Mask
  ctx.moveTo(x - size*0.15, y - size*0.45);
  ctx.quadraticCurveTo(
    x, y - size*0.5,
    x + size*0.15, y - size*0.45
  );
  
  // Body
  ctx.moveTo(x, y - size*0.3);
  ctx.lineTo(x - size*0.25, y + size*0.3);
  ctx.lineTo(x + size*0.25, y + size*0.3);
  ctx.closePath();
  
  // Cape
  ctx.moveTo(x - size*0.2, y - size*0.3);
  ctx.quadraticCurveTo(
    x, y,
    x - size*0.4, y + size*0.5
  );
  ctx.moveTo(x + size*0.2, y - size*0.3);
  ctx.quadraticCurveTo(
    x, y,
    x + size*0.4, y + size*0.5
  );
  
  // Emblem
  ctx.moveTo(x, y - size*0.2);
  ctx.lineTo(x + size*0.1, y - size*0.05);
  ctx.lineTo(x, y + size*0.05);
  ctx.lineTo(x - size*0.1, y - size*0.05);
  ctx.closePath();
  
  // Arms
  ctx.moveTo(x - size*0.15, y - size*0.1);
  ctx.lineTo(x - size*0.35, y + size*0.1);
  
  ctx.moveTo(x + size*0.15, y - size*0.1);
  ctx.lineTo(x + size*0.35, y + size*0.1);
  
  // Legs
  ctx.moveTo(x - size*0.1, y + size*0.3);
  ctx.lineTo(x - size*0.15, y + size*0.6);
  
  ctx.moveTo(x + size*0.1, y + size*0.3);
  ctx.lineTo(x + size*0.15, y + size*0.6);
};
