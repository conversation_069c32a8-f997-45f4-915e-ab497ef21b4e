import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import ArtPromptInput from './controls/ArtPromptInput';
import ArtControlButtons from './controls/ArtControlButtons';
import ArtTips from './controls/ArtTips';
import AspectRatioSelector from './controls/AspectRatioSelector';
import ArtStyleSelector from './controls/ArtStyleSelector';
import ArtSafetyIndicator from './controls/ArtSafetyIndicator';
import { ArtStyleType } from '@/hooks/art/useArtStyle';

interface ArtControlsProps {
  prompt: string;
  setPrompt: (prompt: string) => void;
  title: string;
  setTitle: (title: string) => void;
  handleGenerateArt: () => void;
  handleGenerateIdea: () => void;
  handleImprovePrompt?: () => void;
  isGenerating: boolean;
  isGeneratingIdea: boolean;
  isImprovingPrompt?: boolean;
  artStyle: ArtStyleType;
  setArtStyle: (style: ArtStyleType) => void;
  aspectRatio: string;
  setAspectRatio: (ratio: string) => void;
  learningMode: boolean;
}

const ArtControls = ({
  prompt,
  setPrompt,
  title,
  setTitle,
  handleGenerateArt,
  handleGenerateIdea,
  handleImprovePrompt,
  isGenerating,
  isGeneratingIdea,
  isImprovingPrompt,
  artStyle,
  setArtStyle,
  aspectRatio,
  setAspectRatio,
  learningMode
}: ArtControlsProps) => {
  return (
    <Card className="shadow-md">
      <CardContent className="p-6">
        <div className="space-y-8">
          <ArtSafetyIndicator />
          
          {learningMode && (
            <ArtTips />
          )}
          
          <div className="space-y-6">
            <ArtPromptInput 
              prompt={prompt} 
              setPrompt={setPrompt}
              title={title}
              setTitle={setTitle}
              handleGenerateIdea={handleGenerateIdea}
              isGeneratingIdea={isGeneratingIdea}
              handleImprovePrompt={handleImprovePrompt}
              isImprovingPrompt={isImprovingPrompt}
            />
            
            <div className="grid grid-cols-1 gap-6">
              <ArtStyleSelector 
                artStyle={artStyle} 
                setArtStyle={setArtStyle} 
              />
              <AspectRatioSelector 
                aspectRatio={aspectRatio} 
                setAspectRatio={setAspectRatio} 
              />
            </div>
            
            <ArtControlButtons 
              handleGenerateArt={handleGenerateArt} 
              isGenerating={isGenerating} 
              prompt={prompt}
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ArtControls;

